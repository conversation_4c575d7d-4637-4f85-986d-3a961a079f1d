(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))o(d);new MutationObserver(d=>{for(const c of d)if(c.type==="childList")for(const s of c.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function h(d){const c={};return d.integrity&&(c.integrity=d.integrity),d.referrerPolicy&&(c.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?c.credentials="include":d.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function o(d){if(d.ep)return;d.ep=!0;const c=h(d);fetch(d.href,c)}})();function cg(u){if(Object.prototype.hasOwnProperty.call(u,"__esModule"))return u;var l=u.default;if(typeof l=="function"){var h=function o(){var d=!1;try{d=this instanceof o}catch{}return d?Reflect.construct(l,arguments,this.constructor):l.apply(this,arguments)};h.prototype=l.prototype}else h={};return Object.defineProperty(h,"__esModule",{value:!0}),Object.keys(u).forEach(function(o){var d=Object.getOwnPropertyDescriptor(u,o);Object.defineProperty(h,o,d.get?d:{enumerable:!0,get:function(){return u[o]}})}),h}var Ms={exports:{}},Wr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var id;function fg(){if(id)return Wr;id=1;var u=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function h(o,d,c){var s=null;if(c!==void 0&&(s=""+c),d.key!==void 0&&(s=""+d.key),"key"in d){c={};for(var f in d)f!=="key"&&(c[f]=d[f])}else c=d;return d=c.ref,{$$typeof:u,type:o,key:s,ref:d!==void 0?d:null,props:c}}return Wr.Fragment=l,Wr.jsx=h,Wr.jsxs=h,Wr}var rd;function hg(){return rd||(rd=1,Ms.exports=fg()),Ms.exports}var ft=hg(),_s={exports:{}},At={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ad;function pg(){if(ad)return At;ad=1;var u=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),s=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),D=Symbol.for("react.lazy"),A=Symbol.iterator;function N(q){return q===null||typeof q!="object"?null:(q=A&&q[A]||q["@@iterator"],typeof q=="function"?q:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,B={};function z(q,P,st){this.props=q,this.context=P,this.refs=B,this.updater=st||M}z.prototype.isReactComponent={},z.prototype.setState=function(q,P){if(typeof q!="object"&&typeof q!="function"&&q!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,q,P,"setState")},z.prototype.forceUpdate=function(q){this.updater.enqueueForceUpdate(this,q,"forceUpdate")};function C(){}C.prototype=z.prototype;function _(q,P,st){this.props=q,this.context=P,this.refs=B,this.updater=st||M}var v=_.prototype=new C;v.constructor=_,x(v,z.prototype),v.isPureReactComponent=!0;var U=Array.isArray,X={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function lt(q,P,st,ct,pt,wt){return st=wt.ref,{$$typeof:u,type:q,key:P,ref:st!==void 0?st:null,props:wt}}function $(q,P){return lt(q.type,P,void 0,void 0,void 0,q.props)}function E(q){return typeof q=="object"&&q!==null&&q.$$typeof===u}function J(q){var P={"=":"=0",":":"=2"};return"$"+q.replace(/[=:]/g,function(st){return P[st]})}var it=/\/+/g;function ot(q,P){return typeof q=="object"&&q!==null&&q.key!=null?J(""+q.key):P.toString(36)}function Tt(){}function bt(q){switch(q.status){case"fulfilled":return q.value;case"rejected":throw q.reason;default:switch(typeof q.status=="string"?q.then(Tt,Tt):(q.status="pending",q.then(function(P){q.status==="pending"&&(q.status="fulfilled",q.value=P)},function(P){q.status==="pending"&&(q.status="rejected",q.reason=P)})),q.status){case"fulfilled":return q.value;case"rejected":throw q.reason}}throw q}function St(q,P,st,ct,pt){var wt=typeof q;(wt==="undefined"||wt==="boolean")&&(q=null);var Et=!1;if(q===null)Et=!0;else switch(wt){case"bigint":case"string":case"number":Et=!0;break;case"object":switch(q.$$typeof){case u:case l:Et=!0;break;case D:return Et=q._init,St(Et(q._payload),P,st,ct,pt)}}if(Et)return pt=pt(q),Et=ct===""?"."+ot(q,0):ct,U(pt)?(st="",Et!=null&&(st=Et.replace(it,"$&/")+"/"),St(pt,P,st,"",function(tt){return tt})):pt!=null&&(E(pt)&&(pt=$(pt,st+(pt.key==null||q&&q.key===pt.key?"":(""+pt.key).replace(it,"$&/")+"/")+Et)),P.push(pt)),1;Et=0;var O=ct===""?".":ct+":";if(U(q))for(var T=0;T<q.length;T++)ct=q[T],wt=O+ot(ct,T),Et+=St(ct,P,st,wt,pt);else if(T=N(q),typeof T=="function")for(q=T.call(q),T=0;!(ct=q.next()).done;)ct=ct.value,wt=O+ot(ct,T++),Et+=St(ct,P,st,wt,pt);else if(wt==="object"){if(typeof q.then=="function")return St(bt(q),P,st,ct,pt);throw P=String(q),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(q).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.")}return Et}function I(q,P,st){if(q==null)return q;var ct=[],pt=0;return St(q,ct,"","",function(wt){return P.call(st,wt,pt++)}),ct}function rt(q){if(q._status===-1){var P=q._result;P=P(),P.then(function(st){(q._status===0||q._status===-1)&&(q._status=1,q._result=st)},function(st){(q._status===0||q._status===-1)&&(q._status=2,q._result=st)}),q._status===-1&&(q._status=0,q._result=P)}if(q._status===1)return q._result.default;throw q._result}var yt=typeof reportError=="function"?reportError:function(q){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var P=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof q=="object"&&q!==null&&typeof q.message=="string"?String(q.message):String(q),error:q});if(!window.dispatchEvent(P))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",q);return}console.error(q)};function Bt(){}return At.Children={map:I,forEach:function(q,P,st){I(q,function(){P.apply(this,arguments)},st)},count:function(q){var P=0;return I(q,function(){P++}),P},toArray:function(q){return I(q,function(P){return P})||[]},only:function(q){if(!E(q))throw Error("React.Children.only expected to receive a single React element child.");return q}},At.Component=z,At.Fragment=h,At.Profiler=d,At.PureComponent=_,At.StrictMode=o,At.Suspense=g,At.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=X,At.__COMPILER_RUNTIME={__proto__:null,c:function(q){return X.H.useMemoCache(q)}},At.cache=function(q){return function(){return q.apply(null,arguments)}},At.cloneElement=function(q,P,st){if(q==null)throw Error("The argument must be a React element, but you passed "+q+".");var ct=x({},q.props),pt=q.key,wt=void 0;if(P!=null)for(Et in P.ref!==void 0&&(wt=void 0),P.key!==void 0&&(pt=""+P.key),P)!W.call(P,Et)||Et==="key"||Et==="__self"||Et==="__source"||Et==="ref"&&P.ref===void 0||(ct[Et]=P[Et]);var Et=arguments.length-2;if(Et===1)ct.children=st;else if(1<Et){for(var O=Array(Et),T=0;T<Et;T++)O[T]=arguments[T+2];ct.children=O}return lt(q.type,pt,void 0,void 0,wt,ct)},At.createContext=function(q){return q={$$typeof:s,_currentValue:q,_currentValue2:q,_threadCount:0,Provider:null,Consumer:null},q.Provider=q,q.Consumer={$$typeof:c,_context:q},q},At.createElement=function(q,P,st){var ct,pt={},wt=null;if(P!=null)for(ct in P.key!==void 0&&(wt=""+P.key),P)W.call(P,ct)&&ct!=="key"&&ct!=="__self"&&ct!=="__source"&&(pt[ct]=P[ct]);var Et=arguments.length-2;if(Et===1)pt.children=st;else if(1<Et){for(var O=Array(Et),T=0;T<Et;T++)O[T]=arguments[T+2];pt.children=O}if(q&&q.defaultProps)for(ct in Et=q.defaultProps,Et)pt[ct]===void 0&&(pt[ct]=Et[ct]);return lt(q,wt,void 0,void 0,null,pt)},At.createRef=function(){return{current:null}},At.forwardRef=function(q){return{$$typeof:f,render:q}},At.isValidElement=E,At.lazy=function(q){return{$$typeof:D,_payload:{_status:-1,_result:q},_init:rt}},At.memo=function(q,P){return{$$typeof:b,type:q,compare:P===void 0?null:P}},At.startTransition=function(q){var P=X.T,st={};X.T=st;try{var ct=q(),pt=X.S;pt!==null&&pt(st,ct),typeof ct=="object"&&ct!==null&&typeof ct.then=="function"&&ct.then(Bt,yt)}catch(wt){yt(wt)}finally{X.T=P}},At.unstable_useCacheRefresh=function(){return X.H.useCacheRefresh()},At.use=function(q){return X.H.use(q)},At.useActionState=function(q,P,st){return X.H.useActionState(q,P,st)},At.useCallback=function(q,P){return X.H.useCallback(q,P)},At.useContext=function(q){return X.H.useContext(q)},At.useDebugValue=function(){},At.useDeferredValue=function(q,P){return X.H.useDeferredValue(q,P)},At.useEffect=function(q,P,st){var ct=X.H;if(typeof st=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ct.useEffect(q,P)},At.useId=function(){return X.H.useId()},At.useImperativeHandle=function(q,P,st){return X.H.useImperativeHandle(q,P,st)},At.useInsertionEffect=function(q,P){return X.H.useInsertionEffect(q,P)},At.useLayoutEffect=function(q,P){return X.H.useLayoutEffect(q,P)},At.useMemo=function(q,P){return X.H.useMemo(q,P)},At.useOptimistic=function(q,P){return X.H.useOptimistic(q,P)},At.useReducer=function(q,P,st){return X.H.useReducer(q,P,st)},At.useRef=function(q){return X.H.useRef(q)},At.useState=function(q){return X.H.useState(q)},At.useSyncExternalStore=function(q,P,st){return X.H.useSyncExternalStore(q,P,st)},At.useTransition=function(){return X.H.useTransition()},At.version="19.1.1",At}var ld;function Ks(){return ld||(ld=1,_s.exports=pg()),_s.exports}var Te=Ks(),Rs={exports:{}},$r={},Cs={exports:{}},Ls={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ud;function dg(){return ud||(ud=1,(function(u){function l(I,rt){var yt=I.length;I.push(rt);t:for(;0<yt;){var Bt=yt-1>>>1,q=I[Bt];if(0<d(q,rt))I[Bt]=rt,I[yt]=q,yt=Bt;else break t}}function h(I){return I.length===0?null:I[0]}function o(I){if(I.length===0)return null;var rt=I[0],yt=I.pop();if(yt!==rt){I[0]=yt;t:for(var Bt=0,q=I.length,P=q>>>1;Bt<P;){var st=2*(Bt+1)-1,ct=I[st],pt=st+1,wt=I[pt];if(0>d(ct,yt))pt<q&&0>d(wt,ct)?(I[Bt]=wt,I[pt]=yt,Bt=pt):(I[Bt]=ct,I[st]=yt,Bt=st);else if(pt<q&&0>d(wt,yt))I[Bt]=wt,I[pt]=yt,Bt=pt;else break t}}return rt}function d(I,rt){var yt=I.sortIndex-rt.sortIndex;return yt!==0?yt:I.id-rt.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;u.unstable_now=function(){return c.now()}}else{var s=Date,f=s.now();u.unstable_now=function(){return s.now()-f}}var g=[],b=[],D=1,A=null,N=3,M=!1,x=!1,B=!1,z=!1,C=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,v=typeof setImmediate<"u"?setImmediate:null;function U(I){for(var rt=h(b);rt!==null;){if(rt.callback===null)o(b);else if(rt.startTime<=I)o(b),rt.sortIndex=rt.expirationTime,l(g,rt);else break;rt=h(b)}}function X(I){if(B=!1,U(I),!x)if(h(g)!==null)x=!0,W||(W=!0,ot());else{var rt=h(b);rt!==null&&St(X,rt.startTime-I)}}var W=!1,lt=-1,$=5,E=-1;function J(){return z?!0:!(u.unstable_now()-E<$)}function it(){if(z=!1,W){var I=u.unstable_now();E=I;var rt=!0;try{t:{x=!1,B&&(B=!1,_(lt),lt=-1),M=!0;var yt=N;try{e:{for(U(I),A=h(g);A!==null&&!(A.expirationTime>I&&J());){var Bt=A.callback;if(typeof Bt=="function"){A.callback=null,N=A.priorityLevel;var q=Bt(A.expirationTime<=I);if(I=u.unstable_now(),typeof q=="function"){A.callback=q,U(I),rt=!0;break e}A===h(g)&&o(g),U(I)}else o(g);A=h(g)}if(A!==null)rt=!0;else{var P=h(b);P!==null&&St(X,P.startTime-I),rt=!1}}break t}finally{A=null,N=yt,M=!1}rt=void 0}}finally{rt?ot():W=!1}}}var ot;if(typeof v=="function")ot=function(){v(it)};else if(typeof MessageChannel<"u"){var Tt=new MessageChannel,bt=Tt.port2;Tt.port1.onmessage=it,ot=function(){bt.postMessage(null)}}else ot=function(){C(it,0)};function St(I,rt){lt=C(function(){I(u.unstable_now())},rt)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(I){I.callback=null},u.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<I?Math.floor(1e3/I):5},u.unstable_getCurrentPriorityLevel=function(){return N},u.unstable_next=function(I){switch(N){case 1:case 2:case 3:var rt=3;break;default:rt=N}var yt=N;N=rt;try{return I()}finally{N=yt}},u.unstable_requestPaint=function(){z=!0},u.unstable_runWithPriority=function(I,rt){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var yt=N;N=I;try{return rt()}finally{N=yt}},u.unstable_scheduleCallback=function(I,rt,yt){var Bt=u.unstable_now();switch(typeof yt=="object"&&yt!==null?(yt=yt.delay,yt=typeof yt=="number"&&0<yt?Bt+yt:Bt):yt=Bt,I){case 1:var q=-1;break;case 2:q=250;break;case 5:q=1073741823;break;case 4:q=1e4;break;default:q=5e3}return q=yt+q,I={id:D++,callback:rt,priorityLevel:I,startTime:yt,expirationTime:q,sortIndex:-1},yt>Bt?(I.sortIndex=yt,l(b,I),h(g)===null&&I===h(b)&&(B?(_(lt),lt=-1):B=!0,St(X,yt-Bt))):(I.sortIndex=q,l(g,I),x||M||(x=!0,W||(W=!0,ot()))),I},u.unstable_shouldYield=J,u.unstable_wrapCallback=function(I){var rt=N;return function(){var yt=N;N=rt;try{return I.apply(this,arguments)}finally{N=yt}}}})(Ls)),Ls}var od;function yg(){return od||(od=1,Cs.exports=dg()),Cs.exports}var Us={exports:{}},ve={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sd;function mg(){if(sd)return ve;sd=1;var u=Ks();function l(g){var b="https://react.dev/errors/"+g;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var D=2;D<arguments.length;D++)b+="&args[]="+encodeURIComponent(arguments[D])}return"Minified React error #"+g+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(){}var o={d:{f:h,r:function(){throw Error(l(522))},D:h,C:h,L:h,m:h,X:h,S:h,M:h},p:0,findDOMNode:null},d=Symbol.for("react.portal");function c(g,b,D){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:A==null?null:""+A,children:g,containerInfo:b,implementation:D}}var s=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function f(g,b){if(g==="font")return"";if(typeof b=="string")return b==="use-credentials"?b:""}return ve.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,ve.createPortal=function(g,b){var D=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!b||b.nodeType!==1&&b.nodeType!==9&&b.nodeType!==11)throw Error(l(299));return c(g,b,null,D)},ve.flushSync=function(g){var b=s.T,D=o.p;try{if(s.T=null,o.p=2,g)return g()}finally{s.T=b,o.p=D,o.d.f()}},ve.preconnect=function(g,b){typeof g=="string"&&(b?(b=b.crossOrigin,b=typeof b=="string"?b==="use-credentials"?b:"":void 0):b=null,o.d.C(g,b))},ve.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},ve.preinit=function(g,b){if(typeof g=="string"&&b&&typeof b.as=="string"){var D=b.as,A=f(D,b.crossOrigin),N=typeof b.integrity=="string"?b.integrity:void 0,M=typeof b.fetchPriority=="string"?b.fetchPriority:void 0;D==="style"?o.d.S(g,typeof b.precedence=="string"?b.precedence:void 0,{crossOrigin:A,integrity:N,fetchPriority:M}):D==="script"&&o.d.X(g,{crossOrigin:A,integrity:N,fetchPriority:M,nonce:typeof b.nonce=="string"?b.nonce:void 0})}},ve.preinitModule=function(g,b){if(typeof g=="string")if(typeof b=="object"&&b!==null){if(b.as==null||b.as==="script"){var D=f(b.as,b.crossOrigin);o.d.M(g,{crossOrigin:D,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0})}}else b==null&&o.d.M(g)},ve.preload=function(g,b){if(typeof g=="string"&&typeof b=="object"&&b!==null&&typeof b.as=="string"){var D=b.as,A=f(D,b.crossOrigin);o.d.L(g,D,{crossOrigin:A,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0,type:typeof b.type=="string"?b.type:void 0,fetchPriority:typeof b.fetchPriority=="string"?b.fetchPriority:void 0,referrerPolicy:typeof b.referrerPolicy=="string"?b.referrerPolicy:void 0,imageSrcSet:typeof b.imageSrcSet=="string"?b.imageSrcSet:void 0,imageSizes:typeof b.imageSizes=="string"?b.imageSizes:void 0,media:typeof b.media=="string"?b.media:void 0})}},ve.preloadModule=function(g,b){if(typeof g=="string")if(b){var D=f(b.as,b.crossOrigin);o.d.m(g,{as:typeof b.as=="string"&&b.as!=="script"?b.as:void 0,crossOrigin:D,integrity:typeof b.integrity=="string"?b.integrity:void 0})}else o.d.m(g)},ve.requestFormReset=function(g){o.d.r(g)},ve.unstable_batchedUpdates=function(g,b){return g(b)},ve.useFormState=function(g,b,D){return s.H.useFormState(g,b,D)},ve.useFormStatus=function(){return s.H.useHostTransitionStatus()},ve.version="19.1.1",ve}var cd;function gg(){if(cd)return Us.exports;cd=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(l){console.error(l)}}return u(),Us.exports=mg(),Us.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fd;function Eg(){if(fd)return $r;fd=1;var u=yg(),l=Ks(),h=gg();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function s(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function f(t){if(c(t)!==t)throw Error(o(188))}function g(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,i=e;;){var r=n.return;if(r===null)break;var a=r.alternate;if(a===null){if(i=r.return,i!==null){n=i;continue}break}if(r.child===a.child){for(a=r.child;a;){if(a===n)return f(r),t;if(a===i)return f(r),e;a=a.sibling}throw Error(o(188))}if(n.return!==i.return)n=r,i=a;else{for(var m=!1,S=r.child;S;){if(S===n){m=!0,n=r,i=a;break}if(S===i){m=!0,i=r,n=a;break}S=S.sibling}if(!m){for(S=a.child;S;){if(S===n){m=!0,n=a,i=r;break}if(S===i){m=!0,i=a,n=r;break}S=S.sibling}if(!m)throw Error(o(189))}}if(n.alternate!==i)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function b(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=b(t),e!==null)return e;t=t.sibling}return null}var D=Object.assign,A=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),B=Symbol.for("react.strict_mode"),z=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),_=Symbol.for("react.consumer"),v=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),lt=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),E=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),it=Symbol.iterator;function ot(t){return t===null||typeof t!="object"?null:(t=it&&t[it]||t["@@iterator"],typeof t=="function"?t:null)}var Tt=Symbol.for("react.client.reference");function bt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Tt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case x:return"Fragment";case z:return"Profiler";case B:return"StrictMode";case X:return"Suspense";case W:return"SuspenseList";case E:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case M:return"Portal";case v:return(t.displayName||"Context")+".Provider";case _:return(t._context.displayName||"Context")+".Consumer";case U:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case lt:return e=t.displayName||null,e!==null?e:bt(t.type)||"Memo";case $:e=t._payload,t=t._init;try{return bt(t(e))}catch{}}return null}var St=Array.isArray,I=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,rt=h.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,yt={pending:!1,data:null,method:null,action:null},Bt=[],q=-1;function P(t){return{current:t}}function st(t){0>q||(t.current=Bt[q],Bt[q]=null,q--)}function ct(t,e){q++,Bt[q]=t.current,t.current=e}var pt=P(null),wt=P(null),Et=P(null),O=P(null);function T(t,e){switch(ct(Et,e),ct(wt,t),ct(pt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Rp(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Rp(e),t=Cp(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}st(pt),ct(pt,t)}function tt(){st(pt),st(wt),st(Et)}function Y(t){t.memoizedState!==null&&ct(O,t);var e=pt.current,n=Cp(e,t.type);e!==n&&(ct(wt,t),ct(pt,n))}function xt(t){wt.current===t&&(st(pt),st(wt)),O.current===t&&(st(O),Zr._currentValue=yt)}var Yt=Object.prototype.hasOwnProperty,Lt=u.unstable_scheduleCallback,Jt=u.unstable_cancelCallback,oe=u.unstable_shouldYield,w=u.unstable_requestPaint,p=u.unstable_now,y=u.unstable_getCurrentPriorityLevel,R=u.unstable_ImmediatePriority,V=u.unstable_UserBlockingPriority,at=u.unstable_NormalPriority,ut=u.unstable_LowPriority,Rt=u.unstable_IdlePriority,qt=u.log,Qt=u.unstable_setDisableYieldValue,kt=null,Ct=null;function _e(t){if(typeof qt=="function"&&Qt(t),Ct&&typeof Ct.setStrictMode=="function")try{Ct.setStrictMode(kt,t)}catch{}}var me=Math.clz32?Math.clz32:Jy,ua=Math.log,Py=Math.LN2;function Jy(t){return t>>>=0,t===0?32:31-(ua(t)/Py|0)|0}var oa=256,sa=4194304;function Kn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function ca(t,e,n){var i=t.pendingLanes;if(i===0)return 0;var r=0,a=t.suspendedLanes,m=t.pingedLanes;t=t.warmLanes;var S=i&134217727;return S!==0?(i=S&~a,i!==0?r=Kn(i):(m&=S,m!==0?r=Kn(m):n||(n=S&~t,n!==0&&(r=Kn(n))))):(S=i&~a,S!==0?r=Kn(S):m!==0?r=Kn(m):n||(n=i&~t,n!==0&&(r=Kn(n)))),r===0?0:e!==0&&e!==r&&(e&a)===0&&(a=r&-r,n=e&-e,a>=n||a===32&&(n&4194048)!==0)?e:r}function nr(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function ky(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pc(){var t=oa;return oa<<=1,(oa&4194048)===0&&(oa=256),t}function dc(){var t=sa;return sa<<=1,(sa&62914560)===0&&(sa=4194304),t}function gu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ir(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Wy(t,e,n,i,r,a){var m=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var S=t.entanglements,L=t.expirationTimes,F=t.hiddenUpdates;for(n=m&~n;0<n;){var k=31-me(n),nt=1<<k;S[k]=0,L[k]=-1;var Q=F[k];if(Q!==null)for(F[k]=null,k=0;k<Q.length;k++){var Z=Q[k];Z!==null&&(Z.lane&=-536870913)}n&=~nt}i!==0&&yc(t,i,0),a!==0&&r===0&&t.tag!==0&&(t.suspendedLanes|=a&~(m&~e))}function yc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-me(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|n&4194090}function mc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-me(n),r=1<<i;r&e|t[i]&e&&(t[i]|=e),n&=~r}}function Eu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function vu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function gc(){var t=rt.p;return t!==0?t:(t=window.event,t===void 0?32:kp(t.type))}function $y(t,e){var n=rt.p;try{return rt.p=t,e()}finally{rt.p=n}}var An=Math.random().toString(36).slice(2),ge="__reactFiber$"+An,De="__reactProps$"+An,yi="__reactContainer$"+An,Tu="__reactEvents$"+An,tm="__reactListeners$"+An,em="__reactHandles$"+An,Ec="__reactResources$"+An,rr="__reactMarker$"+An;function bu(t){delete t[ge],delete t[De],delete t[Tu],delete t[tm],delete t[em]}function mi(t){var e=t[ge];if(e)return e;for(var n=t.parentNode;n;){if(e=n[yi]||n[ge]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Xp(t);t!==null;){if(n=t[ge])return n;t=Xp(t)}return e}t=n,n=t.parentNode}return null}function gi(t){if(t=t[ge]||t[yi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ar(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function Ei(t){var e=t[Ec];return e||(e=t[Ec]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function se(t){t[rr]=!0}var vc=new Set,Tc={};function Pn(t,e){vi(t,e),vi(t+"Capture",e)}function vi(t,e){for(Tc[t]=e,t=0;t<e.length;t++)vc.add(e[t])}var nm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),bc={},Sc={};function im(t){return Yt.call(Sc,t)?!0:Yt.call(bc,t)?!1:nm.test(t)?Sc[t]=!0:(bc[t]=!0,!1)}function fa(t,e,n){if(im(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function ha(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function on(t,e,n,i){if(i===null)t.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+i)}}var Su,Dc;function Ti(t){if(Su===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Su=e&&e[1]||"",Dc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Su+t+Dc}var Du=!1;function Au(t,e){if(!t||Du)return"";Du=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var nt=function(){throw Error()};if(Object.defineProperty(nt.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(nt,[])}catch(Z){var Q=Z}Reflect.construct(t,[],nt)}else{try{nt.call()}catch(Z){Q=Z}t.call(nt.prototype)}}else{try{throw Error()}catch(Z){Q=Z}(nt=t())&&typeof nt.catch=="function"&&nt.catch(function(){})}}catch(Z){if(Z&&Q&&typeof Z.stack=="string")return[Z.stack,Q.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=i.DetermineComponentFrameRoot(),m=a[0],S=a[1];if(m&&S){var L=m.split(`
`),F=S.split(`
`);for(r=i=0;i<L.length&&!L[i].includes("DetermineComponentFrameRoot");)i++;for(;r<F.length&&!F[r].includes("DetermineComponentFrameRoot");)r++;if(i===L.length||r===F.length)for(i=L.length-1,r=F.length-1;1<=i&&0<=r&&L[i]!==F[r];)r--;for(;1<=i&&0<=r;i--,r--)if(L[i]!==F[r]){if(i!==1||r!==1)do if(i--,r--,0>r||L[i]!==F[r]){var k=`
`+L[i].replace(" at new "," at ");return t.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",t.displayName)),k}while(1<=i&&0<=r);break}}}finally{Du=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Ti(n):""}function rm(t){switch(t.tag){case 26:case 27:case 5:return Ti(t.type);case 16:return Ti("Lazy");case 13:return Ti("Suspense");case 19:return Ti("SuspenseList");case 0:case 15:return Au(t.type,!1);case 11:return Au(t.type.render,!1);case 1:return Au(t.type,!0);case 31:return Ti("Activity");default:return""}}function Ac(t){try{var e="";do e+=rm(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function qe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Oc(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function am(t){var e=Oc(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,a=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return r.call(this)},set:function(m){i=""+m,a.call(this,m)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(m){i=""+m},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function pa(t){t._valueTracker||(t._valueTracker=am(t))}function wc(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=Oc(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function da(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var lm=/[\n"\\]/g;function je(t){return t.replace(lm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ou(t,e,n,i,r,a,m,S){t.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?t.type=m:t.removeAttribute("type"),e!=null?m==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+qe(e)):t.value!==""+qe(e)&&(t.value=""+qe(e)):m!=="submit"&&m!=="reset"||t.removeAttribute("value"),e!=null?wu(t,m,qe(e)):n!=null?wu(t,m,qe(n)):i!=null&&t.removeAttribute("value"),r==null&&a!=null&&(t.defaultChecked=!!a),r!=null&&(t.checked=r&&typeof r!="function"&&typeof r!="symbol"),S!=null&&typeof S!="function"&&typeof S!="symbol"&&typeof S!="boolean"?t.name=""+qe(S):t.removeAttribute("name")}function Nc(t,e,n,i,r,a,m,S){if(a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.type=a),e!=null||n!=null){if(!(a!=="submit"&&a!=="reset"||e!=null))return;n=n!=null?""+qe(n):"",e=e!=null?""+qe(e):n,S||e===t.value||(t.value=e),t.defaultValue=e}i=i??r,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=S?t.checked:!!i,t.defaultChecked=!!i,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(t.name=m)}function wu(t,e,n){e==="number"&&da(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function bi(t,e,n,i){if(t=t.options,e){e={};for(var r=0;r<n.length;r++)e["$"+n[r]]=!0;for(n=0;n<t.length;n++)r=e.hasOwnProperty("$"+t[n].value),t[n].selected!==r&&(t[n].selected=r),r&&i&&(t[n].defaultSelected=!0)}else{for(n=""+qe(n),e=null,r=0;r<t.length;r++){if(t[r].value===n){t[r].selected=!0,i&&(t[r].defaultSelected=!0);return}e!==null||t[r].disabled||(e=t[r])}e!==null&&(e.selected=!0)}}function xc(t,e,n){if(e!=null&&(e=""+qe(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+qe(n):""}function Mc(t,e,n,i){if(e==null){if(i!=null){if(n!=null)throw Error(o(92));if(St(i)){if(1<i.length)throw Error(o(93));i=i[0]}n=i}n==null&&(n=""),e=n}n=qe(e),t.defaultValue=n,i=t.textContent,i===n&&i!==""&&i!==null&&(t.value=i)}function Si(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var um=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function _c(t,e,n){var i=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,n):typeof n!="number"||n===0||um.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Rc(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var r in e)i=e[r],e.hasOwnProperty(r)&&n[r]!==i&&_c(t,r,i)}else for(var a in e)e.hasOwnProperty(a)&&_c(t,a,e[a])}function Nu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var om=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),sm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ya(t){return sm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var xu=null;function Mu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Di=null,Ai=null;function Cc(t){var e=gi(t);if(e&&(t=e.stateNode)){var n=t[De]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ou(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+je(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var r=i[De]||null;if(!r)throw Error(o(90));Ou(i,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(e=0;e<n.length;e++)i=n[e],i.form===t.form&&wc(i)}break t;case"textarea":xc(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&bi(t,!!n.multiple,e,!1)}}}var _u=!1;function Lc(t,e,n){if(_u)return t(e,n);_u=!0;try{var i=t(e);return i}finally{if(_u=!1,(Di!==null||Ai!==null)&&(tl(),Di&&(e=Di,t=Ai,Ai=Di=null,Cc(e),t)))for(e=0;e<t.length;e++)Cc(t[e])}}function lr(t,e){var n=t.stateNode;if(n===null)return null;var i=n[De]||null;if(i===null)return null;n=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ru=!1;if(sn)try{var ur={};Object.defineProperty(ur,"passive",{get:function(){Ru=!0}}),window.addEventListener("test",ur,ur),window.removeEventListener("test",ur,ur)}catch{Ru=!1}var On=null,Cu=null,ma=null;function Uc(){if(ma)return ma;var t,e=Cu,n=e.length,i,r="value"in On?On.value:On.textContent,a=r.length;for(t=0;t<n&&e[t]===r[t];t++);var m=n-t;for(i=1;i<=m&&e[n-i]===r[a-i];i++);return ma=r.slice(t,1<i?1-i:void 0)}function ga(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ea(){return!0}function Bc(){return!1}function Ae(t){function e(n,i,r,a,m){this._reactName=n,this._targetInst=r,this.type=i,this.nativeEvent=a,this.target=m,this.currentTarget=null;for(var S in t)t.hasOwnProperty(S)&&(n=t[S],this[S]=n?n(a):a[S]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Ea:Bc,this.isPropagationStopped=Bc,this}return D(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ea)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ea)},persist:function(){},isPersistent:Ea}),e}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},va=Ae(Jn),or=D({},Jn,{view:0,detail:0}),cm=Ae(or),Lu,Uu,sr,Ta=D({},or,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==sr&&(sr&&t.type==="mousemove"?(Lu=t.screenX-sr.screenX,Uu=t.screenY-sr.screenY):Uu=Lu=0,sr=t),Lu)},movementY:function(t){return"movementY"in t?t.movementY:Uu}}),Xc=Ae(Ta),fm=D({},Ta,{dataTransfer:0}),hm=Ae(fm),pm=D({},or,{relatedTarget:0}),Bu=Ae(pm),dm=D({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),ym=Ae(dm),mm=D({},Jn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),gm=Ae(mm),Em=D({},Jn,{data:0}),zc=Ae(Em),vm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},bm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=bm[t])?!!e[t]:!1}function Xu(){return Sm}var Dm=D({},or,{key:function(t){if(t.key){var e=vm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ga(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Tm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xu,charCode:function(t){return t.type==="keypress"?ga(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ga(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Am=Ae(Dm),Om=D({},Ta,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qc=Ae(Om),wm=D({},or,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xu}),Nm=Ae(wm),xm=D({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Mm=Ae(xm),_m=D({},Ta,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Rm=Ae(_m),Cm=D({},Jn,{newState:0,oldState:0}),Lm=Ae(Cm),Um=[9,13,27,32],zu=sn&&"CompositionEvent"in window,cr=null;sn&&"documentMode"in document&&(cr=document.documentMode);var Bm=sn&&"TextEvent"in window&&!cr,jc=sn&&(!zu||cr&&8<cr&&11>=cr),Hc=" ",Yc=!1;function Vc(t,e){switch(t){case"keyup":return Um.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gc(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Oi=!1;function Xm(t,e){switch(t){case"compositionend":return Gc(e);case"keypress":return e.which!==32?null:(Yc=!0,Hc);case"textInput":return t=e.data,t===Hc&&Yc?null:t;default:return null}}function zm(t,e){if(Oi)return t==="compositionend"||!zu&&Vc(t,e)?(t=Uc(),ma=Cu=On=null,Oi=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return jc&&e.locale!=="ko"?null:e.data;default:return null}}var qm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!qm[t.type]:e==="textarea"}function Ic(t,e,n,i){Di?Ai?Ai.push(i):Ai=[i]:Di=i,e=ll(e,"onChange"),0<e.length&&(n=new va("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var fr=null,hr=null;function jm(t){wp(t,0)}function ba(t){var e=ar(t);if(wc(e))return t}function Qc(t,e){if(t==="change")return e}var Zc=!1;if(sn){var qu;if(sn){var ju="oninput"in document;if(!ju){var Kc=document.createElement("div");Kc.setAttribute("oninput","return;"),ju=typeof Kc.oninput=="function"}qu=ju}else qu=!1;Zc=qu&&(!document.documentMode||9<document.documentMode)}function Pc(){fr&&(fr.detachEvent("onpropertychange",Jc),hr=fr=null)}function Jc(t){if(t.propertyName==="value"&&ba(hr)){var e=[];Ic(e,hr,t,Mu(t)),Lc(jm,e)}}function Hm(t,e,n){t==="focusin"?(Pc(),fr=e,hr=n,fr.attachEvent("onpropertychange",Jc)):t==="focusout"&&Pc()}function Ym(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ba(hr)}function Vm(t,e){if(t==="click")return ba(e)}function Gm(t,e){if(t==="input"||t==="change")return ba(e)}function Fm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Re=typeof Object.is=="function"?Object.is:Fm;function pr(t,e){if(Re(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var r=n[i];if(!Yt.call(e,r)||!Re(t[r],e[r]))return!1}return!0}function kc(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Wc(t,e){var n=kc(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=kc(n)}}function $c(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?$c(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function tf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=da(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=da(t.document)}return e}function Hu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Im=sn&&"documentMode"in document&&11>=document.documentMode,wi=null,Yu=null,dr=null,Vu=!1;function ef(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Vu||wi==null||wi!==da(i)||(i=wi,"selectionStart"in i&&Hu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),dr&&pr(dr,i)||(dr=i,i=ll(Yu,"onSelect"),0<i.length&&(e=new va("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=wi)))}function kn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ni={animationend:kn("Animation","AnimationEnd"),animationiteration:kn("Animation","AnimationIteration"),animationstart:kn("Animation","AnimationStart"),transitionrun:kn("Transition","TransitionRun"),transitionstart:kn("Transition","TransitionStart"),transitioncancel:kn("Transition","TransitionCancel"),transitionend:kn("Transition","TransitionEnd")},Gu={},nf={};sn&&(nf=document.createElement("div").style,"AnimationEvent"in window||(delete Ni.animationend.animation,delete Ni.animationiteration.animation,delete Ni.animationstart.animation),"TransitionEvent"in window||delete Ni.transitionend.transition);function Wn(t){if(Gu[t])return Gu[t];if(!Ni[t])return t;var e=Ni[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in nf)return Gu[t]=e[n];return t}var rf=Wn("animationend"),af=Wn("animationiteration"),lf=Wn("animationstart"),Qm=Wn("transitionrun"),Zm=Wn("transitionstart"),Km=Wn("transitioncancel"),uf=Wn("transitionend"),of=new Map,Fu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Fu.push("scrollEnd");function Ke(t,e){of.set(t,e),Pn(e,[t])}var sf=new WeakMap;function He(t,e){if(typeof t=="object"&&t!==null){var n=sf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Ac(e)},sf.set(t,e),e)}return{value:t,source:e,stack:Ac(e)}}var Ye=[],xi=0,Iu=0;function Sa(){for(var t=xi,e=Iu=xi=0;e<t;){var n=Ye[e];Ye[e++]=null;var i=Ye[e];Ye[e++]=null;var r=Ye[e];Ye[e++]=null;var a=Ye[e];if(Ye[e++]=null,i!==null&&r!==null){var m=i.pending;m===null?r.next=r:(r.next=m.next,m.next=r),i.pending=r}a!==0&&cf(n,r,a)}}function Da(t,e,n,i){Ye[xi++]=t,Ye[xi++]=e,Ye[xi++]=n,Ye[xi++]=i,Iu|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function Qu(t,e,n,i){return Da(t,e,n,i),Aa(t)}function Mi(t,e){return Da(t,null,null,e),Aa(t)}function cf(t,e,n){t.lanes|=n;var i=t.alternate;i!==null&&(i.lanes|=n);for(var r=!1,a=t.return;a!==null;)a.childLanes|=n,i=a.alternate,i!==null&&(i.childLanes|=n),a.tag===22&&(t=a.stateNode,t===null||t._visibility&1||(r=!0)),t=a,a=a.return;return t.tag===3?(a=t.stateNode,r&&e!==null&&(r=31-me(n),t=a.hiddenUpdates,i=t[r],i===null?t[r]=[e]:i.push(e),e.lane=n|536870912),a):null}function Aa(t){if(50<jr)throw jr=0,$o=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var _i={};function Pm(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ce(t,e,n,i){return new Pm(t,e,n,i)}function Zu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function cn(t,e){var n=t.alternate;return n===null?(n=Ce(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function ff(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Oa(t,e,n,i,r,a){var m=0;if(i=t,typeof t=="function")Zu(t)&&(m=1);else if(typeof t=="string")m=k0(t,n,pt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case E:return t=Ce(31,n,e,r),t.elementType=E,t.lanes=a,t;case x:return $n(n.children,r,a,e);case B:m=8,r|=24;break;case z:return t=Ce(12,n,e,r|2),t.elementType=z,t.lanes=a,t;case X:return t=Ce(13,n,e,r),t.elementType=X,t.lanes=a,t;case W:return t=Ce(19,n,e,r),t.elementType=W,t.lanes=a,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case C:case v:m=10;break t;case _:m=9;break t;case U:m=11;break t;case lt:m=14;break t;case $:m=16,i=null;break t}m=29,n=Error(o(130,t===null?"null":typeof t,"")),i=null}return e=Ce(m,n,e,r),e.elementType=t,e.type=i,e.lanes=a,e}function $n(t,e,n,i){return t=Ce(7,t,i,e),t.lanes=n,t}function Ku(t,e,n){return t=Ce(6,t,null,e),t.lanes=n,t}function Pu(t,e,n){return e=Ce(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ri=[],Ci=0,wa=null,Na=0,Ve=[],Ge=0,ti=null,fn=1,hn="";function ei(t,e){Ri[Ci++]=Na,Ri[Ci++]=wa,wa=t,Na=e}function hf(t,e,n){Ve[Ge++]=fn,Ve[Ge++]=hn,Ve[Ge++]=ti,ti=t;var i=fn;t=hn;var r=32-me(i)-1;i&=~(1<<r),n+=1;var a=32-me(e)+r;if(30<a){var m=r-r%5;a=(i&(1<<m)-1).toString(32),i>>=m,r-=m,fn=1<<32-me(e)+r|n<<r|i,hn=a+t}else fn=1<<a|n<<r|i,hn=t}function Ju(t){t.return!==null&&(ei(t,1),hf(t,1,0))}function ku(t){for(;t===wa;)wa=Ri[--Ci],Ri[Ci]=null,Na=Ri[--Ci],Ri[Ci]=null;for(;t===ti;)ti=Ve[--Ge],Ve[Ge]=null,hn=Ve[--Ge],Ve[Ge]=null,fn=Ve[--Ge],Ve[Ge]=null}var Se=null,Wt=null,zt=!1,ni=null,We=!1,Wu=Error(o(519));function ii(t){var e=Error(o(418,""));throw gr(He(e,t)),Wu}function pf(t){var e=t.stateNode,n=t.type,i=t.memoizedProps;switch(e[ge]=t,e[De]=i,n){case"dialog":_t("cancel",e),_t("close",e);break;case"iframe":case"object":case"embed":_t("load",e);break;case"video":case"audio":for(n=0;n<Yr.length;n++)_t(Yr[n],e);break;case"source":_t("error",e);break;case"img":case"image":case"link":_t("error",e),_t("load",e);break;case"details":_t("toggle",e);break;case"input":_t("invalid",e),Nc(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),pa(e);break;case"select":_t("invalid",e);break;case"textarea":_t("invalid",e),Mc(e,i.value,i.defaultValue,i.children),pa(e)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||i.suppressHydrationWarning===!0||_p(e.textContent,n)?(i.popover!=null&&(_t("beforetoggle",e),_t("toggle",e)),i.onScroll!=null&&_t("scroll",e),i.onScrollEnd!=null&&_t("scrollend",e),i.onClick!=null&&(e.onclick=ul),e=!0):e=!1,e||ii(t)}function df(t){for(Se=t.return;Se;)switch(Se.tag){case 5:case 13:We=!1;return;case 27:case 3:We=!0;return;default:Se=Se.return}}function yr(t){if(t!==Se)return!1;if(!zt)return df(t),zt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ys(t.type,t.memoizedProps)),n=!n),n&&Wt&&ii(t),df(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Wt=Je(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Wt=null}}else e===27?(e=Wt,Yn(t.type)?(t=vs,vs=null,Wt=t):Wt=e):Wt=Se?Je(t.stateNode.nextSibling):null;return!0}function mr(){Wt=Se=null,zt=!1}function yf(){var t=ni;return t!==null&&(Ne===null?Ne=t:Ne.push.apply(Ne,t),ni=null),t}function gr(t){ni===null?ni=[t]:ni.push(t)}var $u=P(null),ri=null,pn=null;function wn(t,e,n){ct($u,e._currentValue),e._currentValue=n}function dn(t){t._currentValue=$u.current,st($u)}function to(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function eo(t,e,n,i){var r=t.child;for(r!==null&&(r.return=t);r!==null;){var a=r.dependencies;if(a!==null){var m=r.child;a=a.firstContext;t:for(;a!==null;){var S=a;a=r;for(var L=0;L<e.length;L++)if(S.context===e[L]){a.lanes|=n,S=a.alternate,S!==null&&(S.lanes|=n),to(a.return,n,t),i||(m=null);break t}a=S.next}}else if(r.tag===18){if(m=r.return,m===null)throw Error(o(341));m.lanes|=n,a=m.alternate,a!==null&&(a.lanes|=n),to(m,n,t),m=null}else m=r.child;if(m!==null)m.return=r;else for(m=r;m!==null;){if(m===t){m=null;break}if(r=m.sibling,r!==null){r.return=m.return,m=r;break}m=m.return}r=m}}function Er(t,e,n,i){t=null;for(var r=e,a=!1;r!==null;){if(!a){if((r.flags&524288)!==0)a=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var m=r.alternate;if(m===null)throw Error(o(387));if(m=m.memoizedProps,m!==null){var S=r.type;Re(r.pendingProps.value,m.value)||(t!==null?t.push(S):t=[S])}}else if(r===O.current){if(m=r.alternate,m===null)throw Error(o(387));m.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(t!==null?t.push(Zr):t=[Zr])}r=r.return}t!==null&&eo(e,t,n,i),e.flags|=262144}function xa(t){for(t=t.firstContext;t!==null;){if(!Re(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ai(t){ri=t,pn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ee(t){return mf(ri,t)}function Ma(t,e){return ri===null&&ai(t),mf(t,e)}function mf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},pn===null){if(t===null)throw Error(o(308));pn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else pn=pn.next=e;return n}var Jm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},km=u.unstable_scheduleCallback,Wm=u.unstable_NormalPriority,le={$$typeof:v,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function no(){return{controller:new Jm,data:new Map,refCount:0}}function vr(t){t.refCount--,t.refCount===0&&km(Wm,function(){t.controller.abort()})}var Tr=null,io=0,Li=0,Ui=null;function $m(t,e){if(Tr===null){var n=Tr=[];io=0,Li=ls(),Ui={status:"pending",value:void 0,then:function(i){n.push(i)}}}return io++,e.then(gf,gf),e}function gf(){if(--io===0&&Tr!==null){Ui!==null&&(Ui.status="fulfilled");var t=Tr;Tr=null,Li=0,Ui=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function t0(t,e){var n=[],i={status:"pending",value:null,reason:null,then:function(r){n.push(r)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var r=0;r<n.length;r++)(0,n[r])(e)},function(r){for(i.status="rejected",i.reason=r,r=0;r<n.length;r++)(0,n[r])(void 0)}),i}var Ef=I.S;I.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&$m(t,e),Ef!==null&&Ef(t,e)};var li=P(null);function ro(){var t=li.current;return t!==null?t:Zt.pooledCache}function _a(t,e){e===null?ct(li,li.current):ct(li,e.pool)}function vf(){var t=ro();return t===null?null:{parent:le._currentValue,pool:t}}var br=Error(o(460)),Tf=Error(o(474)),Ra=Error(o(542)),ao={then:function(){}};function bf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ca(){}function Sf(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ca,Ca),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Af(t),t;default:if(typeof e.status=="string")e.then(Ca,Ca);else{if(t=Zt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var r=e;r.status="fulfilled",r.value=i}},function(i){if(e.status==="pending"){var r=e;r.status="rejected",r.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Af(t),t}throw Sr=e,br}}var Sr=null;function Df(){if(Sr===null)throw Error(o(459));var t=Sr;return Sr=null,t}function Af(t){if(t===br||t===Ra)throw Error(o(483))}var Nn=!1;function lo(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function uo(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function xn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Mn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(jt&2)!==0){var r=i.pending;return r===null?e.next=e:(e.next=r.next,r.next=e),i.pending=e,e=Aa(t),cf(t,null,n),e}return Da(t,i,e,n),Aa(t)}function Dr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,mc(t,n)}}function oo(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var r=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var m={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};a===null?r=a=m:a=a.next=m,n=n.next}while(n!==null);a===null?r=a=e:a=a.next=e}else r=a=e;n={baseState:i.baseState,firstBaseUpdate:r,lastBaseUpdate:a,shared:i.shared,callbacks:i.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var so=!1;function Ar(){if(so){var t=Ui;if(t!==null)throw t}}function Or(t,e,n,i){so=!1;var r=t.updateQueue;Nn=!1;var a=r.firstBaseUpdate,m=r.lastBaseUpdate,S=r.shared.pending;if(S!==null){r.shared.pending=null;var L=S,F=L.next;L.next=null,m===null?a=F:m.next=F,m=L;var k=t.alternate;k!==null&&(k=k.updateQueue,S=k.lastBaseUpdate,S!==m&&(S===null?k.firstBaseUpdate=F:S.next=F,k.lastBaseUpdate=L))}if(a!==null){var nt=r.baseState;m=0,k=F=L=null,S=a;do{var Q=S.lane&-536870913,Z=Q!==S.lane;if(Z?(Ut&Q)===Q:(i&Q)===Q){Q!==0&&Q===Li&&(so=!0),k!==null&&(k=k.next={lane:0,tag:S.tag,payload:S.payload,callback:null,next:null});t:{var vt=t,mt=S;Q=e;var Ft=n;switch(mt.tag){case 1:if(vt=mt.payload,typeof vt=="function"){nt=vt.call(Ft,nt,Q);break t}nt=vt;break t;case 3:vt.flags=vt.flags&-65537|128;case 0:if(vt=mt.payload,Q=typeof vt=="function"?vt.call(Ft,nt,Q):vt,Q==null)break t;nt=D({},nt,Q);break t;case 2:Nn=!0}}Q=S.callback,Q!==null&&(t.flags|=64,Z&&(t.flags|=8192),Z=r.callbacks,Z===null?r.callbacks=[Q]:Z.push(Q))}else Z={lane:Q,tag:S.tag,payload:S.payload,callback:S.callback,next:null},k===null?(F=k=Z,L=nt):k=k.next=Z,m|=Q;if(S=S.next,S===null){if(S=r.shared.pending,S===null)break;Z=S,S=Z.next,Z.next=null,r.lastBaseUpdate=Z,r.shared.pending=null}}while(!0);k===null&&(L=nt),r.baseState=L,r.firstBaseUpdate=F,r.lastBaseUpdate=k,a===null&&(r.shared.lanes=0),zn|=m,t.lanes=m,t.memoizedState=nt}}function Of(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function wf(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Of(n[t],e)}var Bi=P(null),La=P(0);function Nf(t,e){t=bn,ct(La,t),ct(Bi,e),bn=t|e.baseLanes}function co(){ct(La,bn),ct(Bi,Bi.current)}function fo(){bn=La.current,st(Bi),st(La)}var _n=0,Ot=null,Vt=null,ie=null,Ua=!1,Xi=!1,ui=!1,Ba=0,wr=0,zi=null,e0=0;function ee(){throw Error(o(321))}function ho(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Re(t[n],e[n]))return!1;return!0}function po(t,e,n,i,r,a){return _n=a,Ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,I.H=t===null||t.memoizedState===null?ch:fh,ui=!1,a=n(i,r),ui=!1,Xi&&(a=Mf(e,n,i,r)),xf(t),a}function xf(t){I.H=Ya;var e=Vt!==null&&Vt.next!==null;if(_n=0,ie=Vt=Ot=null,Ua=!1,wr=0,zi=null,e)throw Error(o(300));t===null||ce||(t=t.dependencies,t!==null&&xa(t)&&(ce=!0))}function Mf(t,e,n,i){Ot=t;var r=0;do{if(Xi&&(zi=null),wr=0,Xi=!1,25<=r)throw Error(o(301));if(r+=1,ie=Vt=null,t.updateQueue!=null){var a=t.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,a.memoCache!=null&&(a.memoCache.index=0)}I.H=o0,a=e(n,i)}while(Xi);return a}function n0(){var t=I.H,e=t.useState()[0];return e=typeof e.then=="function"?Nr(e):e,t=t.useState()[0],(Vt!==null?Vt.memoizedState:null)!==t&&(Ot.flags|=1024),e}function yo(){var t=Ba!==0;return Ba=0,t}function mo(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function go(t){if(Ua){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Ua=!1}_n=0,ie=Vt=Ot=null,Xi=!1,wr=Ba=0,zi=null}function Oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?Ot.memoizedState=ie=t:ie=ie.next=t,ie}function re(){if(Vt===null){var t=Ot.alternate;t=t!==null?t.memoizedState:null}else t=Vt.next;var e=ie===null?Ot.memoizedState:ie.next;if(e!==null)ie=e,Vt=t;else{if(t===null)throw Ot.alternate===null?Error(o(467)):Error(o(310));Vt=t,t={memoizedState:Vt.memoizedState,baseState:Vt.baseState,baseQueue:Vt.baseQueue,queue:Vt.queue,next:null},ie===null?Ot.memoizedState=ie=t:ie=ie.next=t}return ie}function Eo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Nr(t){var e=wr;return wr+=1,zi===null&&(zi=[]),t=Sf(zi,t,e),e=Ot,(ie===null?e.memoizedState:ie.next)===null&&(e=e.alternate,I.H=e===null||e.memoizedState===null?ch:fh),t}function Xa(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Nr(t);if(t.$$typeof===v)return Ee(t)}throw Error(o(438,String(t)))}function vo(t){var e=null,n=Ot.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var i=Ot.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(r){return r.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Eo(),Ot.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),i=0;i<t;i++)n[i]=J;return e.index++,n}function yn(t,e){return typeof e=="function"?e(t):e}function za(t){var e=re();return To(e,Vt,t)}function To(t,e,n){var i=t.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=n;var r=t.baseQueue,a=i.pending;if(a!==null){if(r!==null){var m=r.next;r.next=a.next,a.next=m}e.baseQueue=r=a,i.pending=null}if(a=t.baseState,r===null)t.memoizedState=a;else{e=r.next;var S=m=null,L=null,F=e,k=!1;do{var nt=F.lane&-536870913;if(nt!==F.lane?(Ut&nt)===nt:(_n&nt)===nt){var Q=F.revertLane;if(Q===0)L!==null&&(L=L.next={lane:0,revertLane:0,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null}),nt===Li&&(k=!0);else if((_n&Q)===Q){F=F.next,Q===Li&&(k=!0);continue}else nt={lane:0,revertLane:F.revertLane,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null},L===null?(S=L=nt,m=a):L=L.next=nt,Ot.lanes|=Q,zn|=Q;nt=F.action,ui&&n(a,nt),a=F.hasEagerState?F.eagerState:n(a,nt)}else Q={lane:nt,revertLane:F.revertLane,action:F.action,hasEagerState:F.hasEagerState,eagerState:F.eagerState,next:null},L===null?(S=L=Q,m=a):L=L.next=Q,Ot.lanes|=nt,zn|=nt;F=F.next}while(F!==null&&F!==e);if(L===null?m=a:L.next=S,!Re(a,t.memoizedState)&&(ce=!0,k&&(n=Ui,n!==null)))throw n;t.memoizedState=a,t.baseState=m,t.baseQueue=L,i.lastRenderedState=a}return r===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function bo(t){var e=re(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var i=n.dispatch,r=n.pending,a=e.memoizedState;if(r!==null){n.pending=null;var m=r=r.next;do a=t(a,m.action),m=m.next;while(m!==r);Re(a,e.memoizedState)||(ce=!0),e.memoizedState=a,e.baseQueue===null&&(e.baseState=a),n.lastRenderedState=a}return[a,i]}function _f(t,e,n){var i=Ot,r=re(),a=zt;if(a){if(n===void 0)throw Error(o(407));n=n()}else n=e();var m=!Re((Vt||r).memoizedState,n);m&&(r.memoizedState=n,ce=!0),r=r.queue;var S=Lf.bind(null,i,r,t);if(xr(2048,8,S,[t]),r.getSnapshot!==e||m||ie!==null&&ie.memoizedState.tag&1){if(i.flags|=2048,qi(9,qa(),Cf.bind(null,i,r,n,e),null),Zt===null)throw Error(o(349));a||(_n&124)!==0||Rf(i,e,n)}return n}function Rf(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=Ot.updateQueue,e===null?(e=Eo(),Ot.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Cf(t,e,n,i){e.value=n,e.getSnapshot=i,Uf(e)&&Bf(t)}function Lf(t,e,n){return n(function(){Uf(e)&&Bf(t)})}function Uf(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Re(t,n)}catch{return!0}}function Bf(t){var e=Mi(t,2);e!==null&&ze(e,t,2)}function So(t){var e=Oe();if(typeof t=="function"){var n=t;if(t=n(),ui){_e(!0);try{n()}finally{_e(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:t},e}function Xf(t,e,n,i){return t.baseState=n,To(t,Vt,typeof i=="function"?i:yn)}function i0(t,e,n,i,r){if(Ha(t))throw Error(o(485));if(t=e.action,t!==null){var a={payload:r,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){a.listeners.push(m)}};I.T!==null?n(!0):a.isTransition=!1,i(a),n=e.pending,n===null?(a.next=e.pending=a,zf(e,a)):(a.next=n.next,e.pending=n.next=a)}}function zf(t,e){var n=e.action,i=e.payload,r=t.state;if(e.isTransition){var a=I.T,m={};I.T=m;try{var S=n(r,i),L=I.S;L!==null&&L(m,S),qf(t,e,S)}catch(F){Do(t,e,F)}finally{I.T=a}}else try{a=n(r,i),qf(t,e,a)}catch(F){Do(t,e,F)}}function qf(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){jf(t,e,i)},function(i){return Do(t,e,i)}):jf(t,e,n)}function jf(t,e,n){e.status="fulfilled",e.value=n,Hf(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,zf(t,n)))}function Do(t,e,n){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=n,Hf(e),e=e.next;while(e!==i)}t.action=null}function Hf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Yf(t,e){return e}function Vf(t,e){if(zt){var n=Zt.formState;if(n!==null){t:{var i=Ot;if(zt){if(Wt){e:{for(var r=Wt,a=We;r.nodeType!==8;){if(!a){r=null;break e}if(r=Je(r.nextSibling),r===null){r=null;break e}}a=r.data,r=a==="F!"||a==="F"?r:null}if(r){Wt=Je(r.nextSibling),i=r.data==="F!";break t}}ii(i)}i=!1}i&&(e=n[0])}}return n=Oe(),n.memoizedState=n.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yf,lastRenderedState:e},n.queue=i,n=uh.bind(null,Ot,i),i.dispatch=n,i=So(!1),a=xo.bind(null,Ot,!1,i.queue),i=Oe(),r={state:e,dispatch:null,action:t,pending:null},i.queue=r,n=i0.bind(null,Ot,r,a,n),r.dispatch=n,i.memoizedState=t,[e,n,!1]}function Gf(t){var e=re();return Ff(e,Vt,t)}function Ff(t,e,n){if(e=To(t,e,Yf)[0],t=za(yn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=Nr(e)}catch(m){throw m===br?Ra:m}else i=e;e=re();var r=e.queue,a=r.dispatch;return n!==e.memoizedState&&(Ot.flags|=2048,qi(9,qa(),r0.bind(null,r,n),null)),[i,a,t]}function r0(t,e){t.action=e}function If(t){var e=re(),n=Vt;if(n!==null)return Ff(e,n,t);re(),e=e.memoizedState,n=re();var i=n.queue.dispatch;return n.memoizedState=t,[e,i,!1]}function qi(t,e,n,i){return t={tag:t,create:n,deps:i,inst:e,next:null},e=Ot.updateQueue,e===null&&(e=Eo(),Ot.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t),t}function qa(){return{destroy:void 0,resource:void 0}}function Qf(){return re().memoizedState}function ja(t,e,n,i){var r=Oe();i=i===void 0?null:i,Ot.flags|=t,r.memoizedState=qi(1|e,qa(),n,i)}function xr(t,e,n,i){var r=re();i=i===void 0?null:i;var a=r.memoizedState.inst;Vt!==null&&i!==null&&ho(i,Vt.memoizedState.deps)?r.memoizedState=qi(e,a,n,i):(Ot.flags|=t,r.memoizedState=qi(1|e,a,n,i))}function Zf(t,e){ja(8390656,8,t,e)}function Kf(t,e){xr(2048,8,t,e)}function Pf(t,e){return xr(4,2,t,e)}function Jf(t,e){return xr(4,4,t,e)}function kf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Wf(t,e,n){n=n!=null?n.concat([t]):null,xr(4,4,kf.bind(null,e,t),n)}function Ao(){}function $f(t,e){var n=re();e=e===void 0?null:e;var i=n.memoizedState;return e!==null&&ho(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function th(t,e){var n=re();e=e===void 0?null:e;var i=n.memoizedState;if(e!==null&&ho(e,i[1]))return i[0];if(i=t(),ui){_e(!0);try{t()}finally{_e(!1)}}return n.memoizedState=[i,e],i}function Oo(t,e,n){return n===void 0||(_n&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=ip(),Ot.lanes|=t,zn|=t,n)}function eh(t,e,n,i){return Re(n,e)?n:Bi.current!==null?(t=Oo(t,n,i),Re(t,e)||(ce=!0),t):(_n&42)===0?(ce=!0,t.memoizedState=n):(t=ip(),Ot.lanes|=t,zn|=t,e)}function nh(t,e,n,i,r){var a=rt.p;rt.p=a!==0&&8>a?a:8;var m=I.T,S={};I.T=S,xo(t,!1,e,n);try{var L=r(),F=I.S;if(F!==null&&F(S,L),L!==null&&typeof L=="object"&&typeof L.then=="function"){var k=t0(L,i);Mr(t,e,k,Xe(t))}else Mr(t,e,i,Xe(t))}catch(nt){Mr(t,e,{then:function(){},status:"rejected",reason:nt},Xe())}finally{rt.p=a,I.T=m}}function a0(){}function wo(t,e,n,i){if(t.tag!==5)throw Error(o(476));var r=ih(t).queue;nh(t,r,e,yt,n===null?a0:function(){return rh(t),n(i)})}function ih(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:yt,baseState:yt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:yt},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function rh(t){var e=ih(t).next.queue;Mr(t,e,{},Xe())}function No(){return Ee(Zr)}function ah(){return re().memoizedState}function lh(){return re().memoizedState}function l0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Xe();t=xn(n);var i=Mn(e,t,n);i!==null&&(ze(i,e,n),Dr(i,e,n)),e={cache:no()},t.payload=e;return}e=e.return}}function u0(t,e,n){var i=Xe();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ha(t)?oh(e,n):(n=Qu(t,e,n,i),n!==null&&(ze(n,t,i),sh(n,e,i)))}function uh(t,e,n){var i=Xe();Mr(t,e,n,i)}function Mr(t,e,n,i){var r={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ha(t))oh(e,r);else{var a=t.alternate;if(t.lanes===0&&(a===null||a.lanes===0)&&(a=e.lastRenderedReducer,a!==null))try{var m=e.lastRenderedState,S=a(m,n);if(r.hasEagerState=!0,r.eagerState=S,Re(S,m))return Da(t,e,r,0),Zt===null&&Sa(),!1}catch{}finally{}if(n=Qu(t,e,r,i),n!==null)return ze(n,t,i),sh(n,e,i),!0}return!1}function xo(t,e,n,i){if(i={lane:2,revertLane:ls(),action:i,hasEagerState:!1,eagerState:null,next:null},Ha(t)){if(e)throw Error(o(479))}else e=Qu(t,n,i,2),e!==null&&ze(e,t,2)}function Ha(t){var e=t.alternate;return t===Ot||e!==null&&e===Ot}function oh(t,e){Xi=Ua=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function sh(t,e,n){if((n&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,mc(t,n)}}var Ya={readContext:Ee,use:Xa,useCallback:ee,useContext:ee,useEffect:ee,useImperativeHandle:ee,useLayoutEffect:ee,useInsertionEffect:ee,useMemo:ee,useReducer:ee,useRef:ee,useState:ee,useDebugValue:ee,useDeferredValue:ee,useTransition:ee,useSyncExternalStore:ee,useId:ee,useHostTransitionStatus:ee,useFormState:ee,useActionState:ee,useOptimistic:ee,useMemoCache:ee,useCacheRefresh:ee},ch={readContext:Ee,use:Xa,useCallback:function(t,e){return Oe().memoizedState=[t,e===void 0?null:e],t},useContext:Ee,useEffect:Zf,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,ja(4194308,4,kf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return ja(4194308,4,t,e)},useInsertionEffect:function(t,e){ja(4,2,t,e)},useMemo:function(t,e){var n=Oe();e=e===void 0?null:e;var i=t();if(ui){_e(!0);try{t()}finally{_e(!1)}}return n.memoizedState=[i,e],i},useReducer:function(t,e,n){var i=Oe();if(n!==void 0){var r=n(e);if(ui){_e(!0);try{n(e)}finally{_e(!1)}}}else r=e;return i.memoizedState=i.baseState=r,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:r},i.queue=t,t=t.dispatch=u0.bind(null,Ot,t),[i.memoizedState,t]},useRef:function(t){var e=Oe();return t={current:t},e.memoizedState=t},useState:function(t){t=So(t);var e=t.queue,n=uh.bind(null,Ot,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Ao,useDeferredValue:function(t,e){var n=Oe();return Oo(n,t,e)},useTransition:function(){var t=So(!1);return t=nh.bind(null,Ot,t.queue,!0,!1),Oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var i=Ot,r=Oe();if(zt){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),Zt===null)throw Error(o(349));(Ut&124)!==0||Rf(i,e,n)}r.memoizedState=n;var a={value:n,getSnapshot:e};return r.queue=a,Zf(Lf.bind(null,i,a,t),[t]),i.flags|=2048,qi(9,qa(),Cf.bind(null,i,a,n,e),null),n},useId:function(){var t=Oe(),e=Zt.identifierPrefix;if(zt){var n=hn,i=fn;n=(i&~(1<<32-me(i)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ba++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=e0++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:No,useFormState:Vf,useActionState:Vf,useOptimistic:function(t){var e=Oe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=xo.bind(null,Ot,!0,n),n.dispatch=e,[t,e]},useMemoCache:vo,useCacheRefresh:function(){return Oe().memoizedState=l0.bind(null,Ot)}},fh={readContext:Ee,use:Xa,useCallback:$f,useContext:Ee,useEffect:Kf,useImperativeHandle:Wf,useInsertionEffect:Pf,useLayoutEffect:Jf,useMemo:th,useReducer:za,useRef:Qf,useState:function(){return za(yn)},useDebugValue:Ao,useDeferredValue:function(t,e){var n=re();return eh(n,Vt.memoizedState,t,e)},useTransition:function(){var t=za(yn)[0],e=re().memoizedState;return[typeof t=="boolean"?t:Nr(t),e]},useSyncExternalStore:_f,useId:ah,useHostTransitionStatus:No,useFormState:Gf,useActionState:Gf,useOptimistic:function(t,e){var n=re();return Xf(n,Vt,t,e)},useMemoCache:vo,useCacheRefresh:lh},o0={readContext:Ee,use:Xa,useCallback:$f,useContext:Ee,useEffect:Kf,useImperativeHandle:Wf,useInsertionEffect:Pf,useLayoutEffect:Jf,useMemo:th,useReducer:bo,useRef:Qf,useState:function(){return bo(yn)},useDebugValue:Ao,useDeferredValue:function(t,e){var n=re();return Vt===null?Oo(n,t,e):eh(n,Vt.memoizedState,t,e)},useTransition:function(){var t=bo(yn)[0],e=re().memoizedState;return[typeof t=="boolean"?t:Nr(t),e]},useSyncExternalStore:_f,useId:ah,useHostTransitionStatus:No,useFormState:If,useActionState:If,useOptimistic:function(t,e){var n=re();return Vt!==null?Xf(n,Vt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:vo,useCacheRefresh:lh},ji=null,_r=0;function Va(t){var e=_r;return _r+=1,ji===null&&(ji=[]),Sf(ji,t,e)}function Rr(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ga(t,e){throw e.$$typeof===A?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function hh(t){var e=t._init;return e(t._payload)}function ph(t){function e(H,j){if(t){var G=H.deletions;G===null?(H.deletions=[j],H.flags|=16):G.push(j)}}function n(H,j){if(!t)return null;for(;j!==null;)e(H,j),j=j.sibling;return null}function i(H){for(var j=new Map;H!==null;)H.key!==null?j.set(H.key,H):j.set(H.index,H),H=H.sibling;return j}function r(H,j){return H=cn(H,j),H.index=0,H.sibling=null,H}function a(H,j,G){return H.index=G,t?(G=H.alternate,G!==null?(G=G.index,G<j?(H.flags|=67108866,j):G):(H.flags|=67108866,j)):(H.flags|=1048576,j)}function m(H){return t&&H.alternate===null&&(H.flags|=67108866),H}function S(H,j,G,et){return j===null||j.tag!==6?(j=Ku(G,H.mode,et),j.return=H,j):(j=r(j,G),j.return=H,j)}function L(H,j,G,et){var ht=G.type;return ht===x?k(H,j,G.props.children,et,G.key):j!==null&&(j.elementType===ht||typeof ht=="object"&&ht!==null&&ht.$$typeof===$&&hh(ht)===j.type)?(j=r(j,G.props),Rr(j,G),j.return=H,j):(j=Oa(G.type,G.key,G.props,null,H.mode,et),Rr(j,G),j.return=H,j)}function F(H,j,G,et){return j===null||j.tag!==4||j.stateNode.containerInfo!==G.containerInfo||j.stateNode.implementation!==G.implementation?(j=Pu(G,H.mode,et),j.return=H,j):(j=r(j,G.children||[]),j.return=H,j)}function k(H,j,G,et,ht){return j===null||j.tag!==7?(j=$n(G,H.mode,et,ht),j.return=H,j):(j=r(j,G),j.return=H,j)}function nt(H,j,G){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return j=Ku(""+j,H.mode,G),j.return=H,j;if(typeof j=="object"&&j!==null){switch(j.$$typeof){case N:return G=Oa(j.type,j.key,j.props,null,H.mode,G),Rr(G,j),G.return=H,G;case M:return j=Pu(j,H.mode,G),j.return=H,j;case $:var et=j._init;return j=et(j._payload),nt(H,j,G)}if(St(j)||ot(j))return j=$n(j,H.mode,G,null),j.return=H,j;if(typeof j.then=="function")return nt(H,Va(j),G);if(j.$$typeof===v)return nt(H,Ma(H,j),G);Ga(H,j)}return null}function Q(H,j,G,et){var ht=j!==null?j.key:null;if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return ht!==null?null:S(H,j,""+G,et);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case N:return G.key===ht?L(H,j,G,et):null;case M:return G.key===ht?F(H,j,G,et):null;case $:return ht=G._init,G=ht(G._payload),Q(H,j,G,et)}if(St(G)||ot(G))return ht!==null?null:k(H,j,G,et,null);if(typeof G.then=="function")return Q(H,j,Va(G),et);if(G.$$typeof===v)return Q(H,j,Ma(H,G),et);Ga(H,G)}return null}function Z(H,j,G,et,ht){if(typeof et=="string"&&et!==""||typeof et=="number"||typeof et=="bigint")return H=H.get(G)||null,S(j,H,""+et,ht);if(typeof et=="object"&&et!==null){switch(et.$$typeof){case N:return H=H.get(et.key===null?G:et.key)||null,L(j,H,et,ht);case M:return H=H.get(et.key===null?G:et.key)||null,F(j,H,et,ht);case $:var Nt=et._init;return et=Nt(et._payload),Z(H,j,G,et,ht)}if(St(et)||ot(et))return H=H.get(G)||null,k(j,H,et,ht,null);if(typeof et.then=="function")return Z(H,j,G,Va(et),ht);if(et.$$typeof===v)return Z(H,j,G,Ma(j,et),ht);Ga(j,et)}return null}function vt(H,j,G,et){for(var ht=null,Nt=null,dt=j,gt=j=0,he=null;dt!==null&&gt<G.length;gt++){dt.index>gt?(he=dt,dt=null):he=dt.sibling;var Xt=Q(H,dt,G[gt],et);if(Xt===null){dt===null&&(dt=he);break}t&&dt&&Xt.alternate===null&&e(H,dt),j=a(Xt,j,gt),Nt===null?ht=Xt:Nt.sibling=Xt,Nt=Xt,dt=he}if(gt===G.length)return n(H,dt),zt&&ei(H,gt),ht;if(dt===null){for(;gt<G.length;gt++)dt=nt(H,G[gt],et),dt!==null&&(j=a(dt,j,gt),Nt===null?ht=dt:Nt.sibling=dt,Nt=dt);return zt&&ei(H,gt),ht}for(dt=i(dt);gt<G.length;gt++)he=Z(dt,H,gt,G[gt],et),he!==null&&(t&&he.alternate!==null&&dt.delete(he.key===null?gt:he.key),j=a(he,j,gt),Nt===null?ht=he:Nt.sibling=he,Nt=he);return t&&dt.forEach(function(Qn){return e(H,Qn)}),zt&&ei(H,gt),ht}function mt(H,j,G,et){if(G==null)throw Error(o(151));for(var ht=null,Nt=null,dt=j,gt=j=0,he=null,Xt=G.next();dt!==null&&!Xt.done;gt++,Xt=G.next()){dt.index>gt?(he=dt,dt=null):he=dt.sibling;var Qn=Q(H,dt,Xt.value,et);if(Qn===null){dt===null&&(dt=he);break}t&&dt&&Qn.alternate===null&&e(H,dt),j=a(Qn,j,gt),Nt===null?ht=Qn:Nt.sibling=Qn,Nt=Qn,dt=he}if(Xt.done)return n(H,dt),zt&&ei(H,gt),ht;if(dt===null){for(;!Xt.done;gt++,Xt=G.next())Xt=nt(H,Xt.value,et),Xt!==null&&(j=a(Xt,j,gt),Nt===null?ht=Xt:Nt.sibling=Xt,Nt=Xt);return zt&&ei(H,gt),ht}for(dt=i(dt);!Xt.done;gt++,Xt=G.next())Xt=Z(dt,H,gt,Xt.value,et),Xt!==null&&(t&&Xt.alternate!==null&&dt.delete(Xt.key===null?gt:Xt.key),j=a(Xt,j,gt),Nt===null?ht=Xt:Nt.sibling=Xt,Nt=Xt);return t&&dt.forEach(function(sg){return e(H,sg)}),zt&&ei(H,gt),ht}function Ft(H,j,G,et){if(typeof G=="object"&&G!==null&&G.type===x&&G.key===null&&(G=G.props.children),typeof G=="object"&&G!==null){switch(G.$$typeof){case N:t:{for(var ht=G.key;j!==null;){if(j.key===ht){if(ht=G.type,ht===x){if(j.tag===7){n(H,j.sibling),et=r(j,G.props.children),et.return=H,H=et;break t}}else if(j.elementType===ht||typeof ht=="object"&&ht!==null&&ht.$$typeof===$&&hh(ht)===j.type){n(H,j.sibling),et=r(j,G.props),Rr(et,G),et.return=H,H=et;break t}n(H,j);break}else e(H,j);j=j.sibling}G.type===x?(et=$n(G.props.children,H.mode,et,G.key),et.return=H,H=et):(et=Oa(G.type,G.key,G.props,null,H.mode,et),Rr(et,G),et.return=H,H=et)}return m(H);case M:t:{for(ht=G.key;j!==null;){if(j.key===ht)if(j.tag===4&&j.stateNode.containerInfo===G.containerInfo&&j.stateNode.implementation===G.implementation){n(H,j.sibling),et=r(j,G.children||[]),et.return=H,H=et;break t}else{n(H,j);break}else e(H,j);j=j.sibling}et=Pu(G,H.mode,et),et.return=H,H=et}return m(H);case $:return ht=G._init,G=ht(G._payload),Ft(H,j,G,et)}if(St(G))return vt(H,j,G,et);if(ot(G)){if(ht=ot(G),typeof ht!="function")throw Error(o(150));return G=ht.call(G),mt(H,j,G,et)}if(typeof G.then=="function")return Ft(H,j,Va(G),et);if(G.$$typeof===v)return Ft(H,j,Ma(H,G),et);Ga(H,G)}return typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint"?(G=""+G,j!==null&&j.tag===6?(n(H,j.sibling),et=r(j,G),et.return=H,H=et):(n(H,j),et=Ku(G,H.mode,et),et.return=H,H=et),m(H)):n(H,j)}return function(H,j,G,et){try{_r=0;var ht=Ft(H,j,G,et);return ji=null,ht}catch(dt){if(dt===br||dt===Ra)throw dt;var Nt=Ce(29,dt,null,H.mode);return Nt.lanes=et,Nt.return=H,Nt}finally{}}}var Hi=ph(!0),dh=ph(!1),Fe=P(null),$e=null;function Rn(t){var e=t.alternate;ct(ue,ue.current&1),ct(Fe,t),$e===null&&(e===null||Bi.current!==null||e.memoizedState!==null)&&($e=t)}function yh(t){if(t.tag===22){if(ct(ue,ue.current),ct(Fe,t),$e===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&($e=t)}}else Cn()}function Cn(){ct(ue,ue.current),ct(Fe,Fe.current)}function mn(t){st(Fe),$e===t&&($e=null),st(ue)}var ue=P(0);function Fa(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Es(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Mo(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:D({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var _o={enqueueSetState:function(t,e,n){t=t._reactInternals;var i=Xe(),r=xn(i);r.payload=e,n!=null&&(r.callback=n),e=Mn(t,r,i),e!==null&&(ze(e,t,i),Dr(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=Xe(),r=xn(i);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=Mn(t,r,i),e!==null&&(ze(e,t,i),Dr(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Xe(),i=xn(n);i.tag=2,e!=null&&(i.callback=e),e=Mn(t,i,n),e!==null&&(ze(e,t,n),Dr(e,t,n))}};function mh(t,e,n,i,r,a,m){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,a,m):e.prototype&&e.prototype.isPureReactComponent?!pr(n,i)||!pr(r,a):!0}function gh(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&_o.enqueueReplaceState(e,e.state,null)}function oi(t,e){var n=e;if("ref"in e){n={};for(var i in e)i!=="ref"&&(n[i]=e[i])}if(t=t.defaultProps){n===e&&(n=D({},n));for(var r in t)n[r]===void 0&&(n[r]=t[r])}return n}var Ia=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Eh(t){Ia(t)}function vh(t){console.error(t)}function Th(t){Ia(t)}function Qa(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function bh(t,e,n){try{var i=t.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ro(t,e,n){return n=xn(n),n.tag=3,n.payload={element:null},n.callback=function(){Qa(t,e)},n}function Sh(t){return t=xn(t),t.tag=3,t}function Dh(t,e,n,i){var r=n.type.getDerivedStateFromError;if(typeof r=="function"){var a=i.value;t.payload=function(){return r(a)},t.callback=function(){bh(e,n,i)}}var m=n.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(t.callback=function(){bh(e,n,i),typeof r!="function"&&(qn===null?qn=new Set([this]):qn.add(this));var S=i.stack;this.componentDidCatch(i.value,{componentStack:S!==null?S:""})})}function s0(t,e,n,i,r){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=n.alternate,e!==null&&Er(e,n,r,!0),n=Fe.current,n!==null){switch(n.tag){case 13:return $e===null?es():n.alternate===null&&$t===0&&($t=3),n.flags&=-257,n.flags|=65536,n.lanes=r,i===ao?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([i]):e.add(i),is(t,i,r)),!1;case 22:return n.flags|=65536,i===ao?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([i]):n.add(i)),is(t,i,r)),!1}throw Error(o(435,n.tag))}return is(t,i,r),es(),!1}if(zt)return e=Fe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=r,i!==Wu&&(t=Error(o(422),{cause:i}),gr(He(t,n)))):(i!==Wu&&(e=Error(o(423),{cause:i}),gr(He(e,n))),t=t.current.alternate,t.flags|=65536,r&=-r,t.lanes|=r,i=He(i,n),r=Ro(t.stateNode,i,r),oo(t,r),$t!==4&&($t=2)),!1;var a=Error(o(520),{cause:i});if(a=He(a,n),qr===null?qr=[a]:qr.push(a),$t!==4&&($t=2),e===null)return!0;i=He(i,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=r&-r,n.lanes|=t,t=Ro(n.stateNode,i,t),oo(n,t),!1;case 1:if(e=n.type,a=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||a!==null&&typeof a.componentDidCatch=="function"&&(qn===null||!qn.has(a))))return n.flags|=65536,r&=-r,n.lanes|=r,r=Sh(r),Dh(r,t,n,i),oo(n,r),!1}n=n.return}while(n!==null);return!1}var Ah=Error(o(461)),ce=!1;function pe(t,e,n,i){e.child=t===null?dh(e,null,n,i):Hi(e,t.child,n,i)}function Oh(t,e,n,i,r){n=n.render;var a=e.ref;if("ref"in i){var m={};for(var S in i)S!=="ref"&&(m[S]=i[S])}else m=i;return ai(e),i=po(t,e,n,m,a,r),S=yo(),t!==null&&!ce?(mo(t,e,r),gn(t,e,r)):(zt&&S&&Ju(e),e.flags|=1,pe(t,e,i,r),e.child)}function wh(t,e,n,i,r){if(t===null){var a=n.type;return typeof a=="function"&&!Zu(a)&&a.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=a,Nh(t,e,a,i,r)):(t=Oa(n.type,null,i,e,e.mode,r),t.ref=e.ref,t.return=e,e.child=t)}if(a=t.child,!jo(t,r)){var m=a.memoizedProps;if(n=n.compare,n=n!==null?n:pr,n(m,i)&&t.ref===e.ref)return gn(t,e,r)}return e.flags|=1,t=cn(a,i),t.ref=e.ref,t.return=e,e.child=t}function Nh(t,e,n,i,r){if(t!==null){var a=t.memoizedProps;if(pr(a,i)&&t.ref===e.ref)if(ce=!1,e.pendingProps=i=a,jo(t,r))(t.flags&131072)!==0&&(ce=!0);else return e.lanes=t.lanes,gn(t,e,r)}return Co(t,e,n,i,r)}function xh(t,e,n){var i=e.pendingProps,r=i.children,a=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=a!==null?a.baseLanes|n:n,t!==null){for(r=e.child=t.child,a=0;r!==null;)a=a|r.lanes|r.childLanes,r=r.sibling;e.childLanes=a&~i}else e.childLanes=0,e.child=null;return Mh(t,e,i,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&_a(e,a!==null?a.cachePool:null),a!==null?Nf(e,a):co(),yh(e);else return e.lanes=e.childLanes=536870912,Mh(t,e,a!==null?a.baseLanes|n:n,n)}else a!==null?(_a(e,a.cachePool),Nf(e,a),Cn(),e.memoizedState=null):(t!==null&&_a(e,null),co(),Cn());return pe(t,e,r,n),e.child}function Mh(t,e,n,i){var r=ro();return r=r===null?null:{parent:le._currentValue,pool:r},e.memoizedState={baseLanes:n,cachePool:r},t!==null&&_a(e,null),co(),yh(e),t!==null&&Er(t,e,i,!0),null}function Za(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Co(t,e,n,i,r){return ai(e),n=po(t,e,n,i,void 0,r),i=yo(),t!==null&&!ce?(mo(t,e,r),gn(t,e,r)):(zt&&i&&Ju(e),e.flags|=1,pe(t,e,n,r),e.child)}function _h(t,e,n,i,r,a){return ai(e),e.updateQueue=null,n=Mf(e,i,n,r),xf(t),i=yo(),t!==null&&!ce?(mo(t,e,a),gn(t,e,a)):(zt&&i&&Ju(e),e.flags|=1,pe(t,e,n,a),e.child)}function Rh(t,e,n,i,r){if(ai(e),e.stateNode===null){var a=_i,m=n.contextType;typeof m=="object"&&m!==null&&(a=Ee(m)),a=new n(i,a),e.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,a.updater=_o,e.stateNode=a,a._reactInternals=e,a=e.stateNode,a.props=i,a.state=e.memoizedState,a.refs={},lo(e),m=n.contextType,a.context=typeof m=="object"&&m!==null?Ee(m):_i,a.state=e.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(Mo(e,n,m,i),a.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(m=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),m!==a.state&&_o.enqueueReplaceState(a,a.state,null),Or(e,i,a,r),Ar(),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){a=e.stateNode;var S=e.memoizedProps,L=oi(n,S);a.props=L;var F=a.context,k=n.contextType;m=_i,typeof k=="object"&&k!==null&&(m=Ee(k));var nt=n.getDerivedStateFromProps;k=typeof nt=="function"||typeof a.getSnapshotBeforeUpdate=="function",S=e.pendingProps!==S,k||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(S||F!==m)&&gh(e,a,i,m),Nn=!1;var Q=e.memoizedState;a.state=Q,Or(e,i,a,r),Ar(),F=e.memoizedState,S||Q!==F||Nn?(typeof nt=="function"&&(Mo(e,n,nt,i),F=e.memoizedState),(L=Nn||mh(e,n,L,i,Q,F,m))?(k||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(e.flags|=4194308)):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=F),a.props=i,a.state=F,a.context=m,i=L):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{a=e.stateNode,uo(t,e),m=e.memoizedProps,k=oi(n,m),a.props=k,nt=e.pendingProps,Q=a.context,F=n.contextType,L=_i,typeof F=="object"&&F!==null&&(L=Ee(F)),S=n.getDerivedStateFromProps,(F=typeof S=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(m!==nt||Q!==L)&&gh(e,a,i,L),Nn=!1,Q=e.memoizedState,a.state=Q,Or(e,i,a,r),Ar();var Z=e.memoizedState;m!==nt||Q!==Z||Nn||t!==null&&t.dependencies!==null&&xa(t.dependencies)?(typeof S=="function"&&(Mo(e,n,S,i),Z=e.memoizedState),(k=Nn||mh(e,n,k,i,Q,Z,L)||t!==null&&t.dependencies!==null&&xa(t.dependencies))?(F||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(i,Z,L),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(i,Z,L)),typeof a.componentDidUpdate=="function"&&(e.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof a.componentDidUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=Z),a.props=i,a.state=Z,a.context=L,i=k):(typeof a.componentDidUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=1024),i=!1)}return a=i,Za(t,e),i=(e.flags&128)!==0,a||i?(a=e.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:a.render(),e.flags|=1,t!==null&&i?(e.child=Hi(e,t.child,null,r),e.child=Hi(e,null,n,r)):pe(t,e,n,r),e.memoizedState=a.state,t=e.child):t=gn(t,e,r),t}function Ch(t,e,n,i){return mr(),e.flags|=256,pe(t,e,n,i),e.child}var Lo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Uo(t){return{baseLanes:t,cachePool:vf()}}function Bo(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ie),t}function Lh(t,e,n){var i=e.pendingProps,r=!1,a=(e.flags&128)!==0,m;if((m=a)||(m=t!==null&&t.memoizedState===null?!1:(ue.current&2)!==0),m&&(r=!0,e.flags&=-129),m=(e.flags&32)!==0,e.flags&=-33,t===null){if(zt){if(r?Rn(e):Cn(),zt){var S=Wt,L;if(L=S){t:{for(L=S,S=We;L.nodeType!==8;){if(!S){S=null;break t}if(L=Je(L.nextSibling),L===null){S=null;break t}}S=L}S!==null?(e.memoizedState={dehydrated:S,treeContext:ti!==null?{id:fn,overflow:hn}:null,retryLane:536870912,hydrationErrors:null},L=Ce(18,null,null,0),L.stateNode=S,L.return=e,e.child=L,Se=e,Wt=null,L=!0):L=!1}L||ii(e)}if(S=e.memoizedState,S!==null&&(S=S.dehydrated,S!==null))return Es(S)?e.lanes=32:e.lanes=536870912,null;mn(e)}return S=i.children,i=i.fallback,r?(Cn(),r=e.mode,S=Ka({mode:"hidden",children:S},r),i=$n(i,r,n,null),S.return=e,i.return=e,S.sibling=i,e.child=S,r=e.child,r.memoizedState=Uo(n),r.childLanes=Bo(t,m,n),e.memoizedState=Lo,i):(Rn(e),Xo(e,S))}if(L=t.memoizedState,L!==null&&(S=L.dehydrated,S!==null)){if(a)e.flags&256?(Rn(e),e.flags&=-257,e=zo(t,e,n)):e.memoizedState!==null?(Cn(),e.child=t.child,e.flags|=128,e=null):(Cn(),r=i.fallback,S=e.mode,i=Ka({mode:"visible",children:i.children},S),r=$n(r,S,n,null),r.flags|=2,i.return=e,r.return=e,i.sibling=r,e.child=i,Hi(e,t.child,null,n),i=e.child,i.memoizedState=Uo(n),i.childLanes=Bo(t,m,n),e.memoizedState=Lo,e=r);else if(Rn(e),Es(S)){if(m=S.nextSibling&&S.nextSibling.dataset,m)var F=m.dgst;m=F,i=Error(o(419)),i.stack="",i.digest=m,gr({value:i,source:null,stack:null}),e=zo(t,e,n)}else if(ce||Er(t,e,n,!1),m=(n&t.childLanes)!==0,ce||m){if(m=Zt,m!==null&&(i=n&-n,i=(i&42)!==0?1:Eu(i),i=(i&(m.suspendedLanes|n))!==0?0:i,i!==0&&i!==L.retryLane))throw L.retryLane=i,Mi(t,i),ze(m,t,i),Ah;S.data==="$?"||es(),e=zo(t,e,n)}else S.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=L.treeContext,Wt=Je(S.nextSibling),Se=e,zt=!0,ni=null,We=!1,t!==null&&(Ve[Ge++]=fn,Ve[Ge++]=hn,Ve[Ge++]=ti,fn=t.id,hn=t.overflow,ti=e),e=Xo(e,i.children),e.flags|=4096);return e}return r?(Cn(),r=i.fallback,S=e.mode,L=t.child,F=L.sibling,i=cn(L,{mode:"hidden",children:i.children}),i.subtreeFlags=L.subtreeFlags&65011712,F!==null?r=cn(F,r):(r=$n(r,S,n,null),r.flags|=2),r.return=e,i.return=e,i.sibling=r,e.child=i,i=r,r=e.child,S=t.child.memoizedState,S===null?S=Uo(n):(L=S.cachePool,L!==null?(F=le._currentValue,L=L.parent!==F?{parent:F,pool:F}:L):L=vf(),S={baseLanes:S.baseLanes|n,cachePool:L}),r.memoizedState=S,r.childLanes=Bo(t,m,n),e.memoizedState=Lo,i):(Rn(e),n=t.child,t=n.sibling,n=cn(n,{mode:"visible",children:i.children}),n.return=e,n.sibling=null,t!==null&&(m=e.deletions,m===null?(e.deletions=[t],e.flags|=16):m.push(t)),e.child=n,e.memoizedState=null,n)}function Xo(t,e){return e=Ka({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ka(t,e){return t=Ce(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function zo(t,e,n){return Hi(e,t.child,null,n),t=Xo(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Uh(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),to(t.return,e,n)}function qo(t,e,n,i,r){var a=t.memoizedState;a===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:r}:(a.isBackwards=e,a.rendering=null,a.renderingStartTime=0,a.last=i,a.tail=n,a.tailMode=r)}function Bh(t,e,n){var i=e.pendingProps,r=i.revealOrder,a=i.tail;if(pe(t,e,i.children,n),i=ue.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Uh(t,n,e);else if(t.tag===19)Uh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch(ct(ue,i),r){case"forwards":for(n=e.child,r=null;n!==null;)t=n.alternate,t!==null&&Fa(t)===null&&(r=n),n=n.sibling;n=r,n===null?(r=e.child,e.child=null):(r=n.sibling,n.sibling=null),qo(e,!1,r,n,a);break;case"backwards":for(n=null,r=e.child,e.child=null;r!==null;){if(t=r.alternate,t!==null&&Fa(t)===null){e.child=r;break}t=r.sibling,r.sibling=n,n=r,r=t}qo(e,!0,n,null,a);break;case"together":qo(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function gn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),zn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Er(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=cn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=cn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function jo(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&xa(t)))}function c0(t,e,n){switch(e.tag){case 3:T(e,e.stateNode.containerInfo),wn(e,le,t.memoizedState.cache),mr();break;case 27:case 5:Y(e);break;case 4:T(e,e.stateNode.containerInfo);break;case 10:wn(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(Rn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Lh(t,e,n):(Rn(e),t=gn(t,e,n),t!==null?t.sibling:null);Rn(e);break;case 19:var r=(t.flags&128)!==0;if(i=(n&e.childLanes)!==0,i||(Er(t,e,n,!1),i=(n&e.childLanes)!==0),r){if(i)return Bh(t,e,n);e.flags|=128}if(r=e.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),ct(ue,ue.current),i)break;return null;case 22:case 23:return e.lanes=0,xh(t,e,n);case 24:wn(e,le,t.memoizedState.cache)}return gn(t,e,n)}function Xh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)ce=!0;else{if(!jo(t,n)&&(e.flags&128)===0)return ce=!1,c0(t,e,n);ce=(t.flags&131072)!==0}else ce=!1,zt&&(e.flags&1048576)!==0&&hf(e,Na,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,r=i._init;if(i=r(i._payload),e.type=i,typeof i=="function")Zu(i)?(t=oi(i,t),e.tag=1,e=Rh(null,e,i,t,n)):(e.tag=0,e=Co(null,e,i,t,n));else{if(i!=null){if(r=i.$$typeof,r===U){e.tag=11,e=Oh(null,e,i,t,n);break t}else if(r===lt){e.tag=14,e=wh(null,e,i,t,n);break t}}throw e=bt(i)||i,Error(o(306,e,""))}}return e;case 0:return Co(t,e,e.type,e.pendingProps,n);case 1:return i=e.type,r=oi(i,e.pendingProps),Rh(t,e,i,r,n);case 3:t:{if(T(e,e.stateNode.containerInfo),t===null)throw Error(o(387));i=e.pendingProps;var a=e.memoizedState;r=a.element,uo(t,e),Or(e,i,null,n);var m=e.memoizedState;if(i=m.cache,wn(e,le,i),i!==a.cache&&eo(e,[le],n,!0),Ar(),i=m.element,a.isDehydrated)if(a={element:i,isDehydrated:!1,cache:m.cache},e.updateQueue.baseState=a,e.memoizedState=a,e.flags&256){e=Ch(t,e,i,n);break t}else if(i!==r){r=He(Error(o(424)),e),gr(r),e=Ch(t,e,i,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Wt=Je(t.firstChild),Se=e,zt=!0,ni=null,We=!0,n=dh(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(mr(),i===r){e=gn(t,e,n);break t}pe(t,e,i,n)}e=e.child}return e;case 26:return Za(t,e),t===null?(n=Hp(e.type,null,e.pendingProps,null))?e.memoizedState=n:zt||(n=e.type,t=e.pendingProps,i=ol(Et.current).createElement(n),i[ge]=e,i[De]=t,ye(i,n,t),se(i),e.stateNode=i):e.memoizedState=Hp(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Y(e),t===null&&zt&&(i=e.stateNode=zp(e.type,e.pendingProps,Et.current),Se=e,We=!0,r=Wt,Yn(e.type)?(vs=r,Wt=Je(i.firstChild)):Wt=r),pe(t,e,e.pendingProps.children,n),Za(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&zt&&((r=i=Wt)&&(i=q0(i,e.type,e.pendingProps,We),i!==null?(e.stateNode=i,Se=e,Wt=Je(i.firstChild),We=!1,r=!0):r=!1),r||ii(e)),Y(e),r=e.type,a=e.pendingProps,m=t!==null?t.memoizedProps:null,i=a.children,ys(r,a)?i=null:m!==null&&ys(r,m)&&(e.flags|=32),e.memoizedState!==null&&(r=po(t,e,n0,null,null,n),Zr._currentValue=r),Za(t,e),pe(t,e,i,n),e.child;case 6:return t===null&&zt&&((t=n=Wt)&&(n=j0(n,e.pendingProps,We),n!==null?(e.stateNode=n,Se=e,Wt=null,t=!0):t=!1),t||ii(e)),null;case 13:return Lh(t,e,n);case 4:return T(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=Hi(e,null,i,n):pe(t,e,i,n),e.child;case 11:return Oh(t,e,e.type,e.pendingProps,n);case 7:return pe(t,e,e.pendingProps,n),e.child;case 8:return pe(t,e,e.pendingProps.children,n),e.child;case 12:return pe(t,e,e.pendingProps.children,n),e.child;case 10:return i=e.pendingProps,wn(e,e.type,i.value),pe(t,e,i.children,n),e.child;case 9:return r=e.type._context,i=e.pendingProps.children,ai(e),r=Ee(r),i=i(r),e.flags|=1,pe(t,e,i,n),e.child;case 14:return wh(t,e,e.type,e.pendingProps,n);case 15:return Nh(t,e,e.type,e.pendingProps,n);case 19:return Bh(t,e,n);case 31:return i=e.pendingProps,n=e.mode,i={mode:i.mode,children:i.children},t===null?(n=Ka(i,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=cn(t.child,i),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return xh(t,e,n);case 24:return ai(e),i=Ee(le),t===null?(r=ro(),r===null&&(r=Zt,a=no(),r.pooledCache=a,a.refCount++,a!==null&&(r.pooledCacheLanes|=n),r=a),e.memoizedState={parent:i,cache:r},lo(e),wn(e,le,r)):((t.lanes&n)!==0&&(uo(t,e),Or(e,null,null,n),Ar()),r=t.memoizedState,a=e.memoizedState,r.parent!==i?(r={parent:i,cache:i},e.memoizedState=r,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=r),wn(e,le,i)):(i=a.cache,wn(e,le,i),i!==r.cache&&eo(e,[le],n,!0))),pe(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function En(t){t.flags|=4}function zh(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ip(e)){if(e=Fe.current,e!==null&&((Ut&4194048)===Ut?$e!==null:(Ut&62914560)!==Ut&&(Ut&536870912)===0||e!==$e))throw Sr=ao,Tf;t.flags|=8192}}function Pa(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?dc():536870912,t.lanes|=e,Fi|=e)}function Cr(t,e){if(!zt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Pt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var r=t.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags&65011712,i|=r.flags&65011712,r.return=t,r=r.sibling;else for(r=t.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags,i|=r.flags,r.return=t,r=r.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function f0(t,e,n){var i=e.pendingProps;switch(ku(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Pt(e),null;case 1:return Pt(e),null;case 3:return n=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),dn(le),tt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(yr(e)?En(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,yf())),Pt(e),null;case 26:return n=e.memoizedState,t===null?(En(e),n!==null?(Pt(e),zh(e,n)):(Pt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(En(e),Pt(e),zh(e,n)):(Pt(e),e.flags&=-16777217):(t.memoizedProps!==i&&En(e),Pt(e),e.flags&=-16777217),null;case 27:xt(e),n=Et.current;var r=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&En(e);else{if(!i){if(e.stateNode===null)throw Error(o(166));return Pt(e),null}t=pt.current,yr(e)?pf(e):(t=zp(r,i,n),e.stateNode=t,En(e))}return Pt(e),null;case 5:if(xt(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&En(e);else{if(!i){if(e.stateNode===null)throw Error(o(166));return Pt(e),null}if(t=pt.current,yr(e))pf(e);else{switch(r=ol(Et.current),t){case 1:t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=r.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?r.createElement("select",{is:i.is}):r.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?r.createElement(n,{is:i.is}):r.createElement(n)}}t[ge]=e,t[De]=i;t:for(r=e.child;r!==null;){if(r.tag===5||r.tag===6)t.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===e)break t;for(;r.sibling===null;){if(r.return===null||r.return===e)break t;r=r.return}r.sibling.return=r.return,r=r.sibling}e.stateNode=t;t:switch(ye(t,n,i),n){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&En(e)}}return Pt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&En(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(o(166));if(t=Et.current,yr(e)){if(t=e.stateNode,n=e.memoizedProps,i=null,r=Se,r!==null)switch(r.tag){case 27:case 5:i=r.memoizedProps}t[ge]=e,t=!!(t.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||_p(t.nodeValue,n)),t||ii(e)}else t=ol(t).createTextNode(i),t[ge]=e,e.stateNode=t}return Pt(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(r=yr(e),i!==null&&i.dehydrated!==null){if(t===null){if(!r)throw Error(o(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(o(317));r[ge]=e}else mr(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Pt(e),r=!1}else r=yf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=r),r=!0;if(!r)return e.flags&256?(mn(e),e):(mn(e),null)}if(mn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=i!==null,t=t!==null&&t.memoizedState!==null,n){i=e.child,r=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(r=i.alternate.memoizedState.cachePool.pool);var a=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(a=i.memoizedState.cachePool.pool),a!==r&&(i.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Pa(e,e.updateQueue),Pt(e),null;case 4:return tt(),t===null&&cs(e.stateNode.containerInfo),Pt(e),null;case 10:return dn(e.type),Pt(e),null;case 19:if(st(ue),r=e.memoizedState,r===null)return Pt(e),null;if(i=(e.flags&128)!==0,a=r.rendering,a===null)if(i)Cr(r,!1);else{if($t!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(a=Fa(t),a!==null){for(e.flags|=128,Cr(r,!1),t=a.updateQueue,e.updateQueue=t,Pa(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)ff(n,t),n=n.sibling;return ct(ue,ue.current&1|2),e.child}t=t.sibling}r.tail!==null&&p()>Wa&&(e.flags|=128,i=!0,Cr(r,!1),e.lanes=4194304)}else{if(!i)if(t=Fa(a),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,Pa(e,t),Cr(r,!0),r.tail===null&&r.tailMode==="hidden"&&!a.alternate&&!zt)return Pt(e),null}else 2*p()-r.renderingStartTime>Wa&&n!==536870912&&(e.flags|=128,i=!0,Cr(r,!1),e.lanes=4194304);r.isBackwards?(a.sibling=e.child,e.child=a):(t=r.last,t!==null?t.sibling=a:e.child=a,r.last=a)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=p(),e.sibling=null,t=ue.current,ct(ue,i?t&1|2:t&1),e):(Pt(e),null);case 22:case 23:return mn(e),fo(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(n&536870912)!==0&&(e.flags&128)===0&&(Pt(e),e.subtreeFlags&6&&(e.flags|=8192)):Pt(e),n=e.updateQueue,n!==null&&Pa(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==n&&(e.flags|=2048),t!==null&&st(li),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),dn(le),Pt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function h0(t,e){switch(ku(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return dn(le),tt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return xt(e),null;case 13:if(mn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));mr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return st(ue),null;case 4:return tt(),null;case 10:return dn(e.type),null;case 22:case 23:return mn(e),fo(),t!==null&&st(li),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return dn(le),null;case 25:return null;default:return null}}function qh(t,e){switch(ku(e),e.tag){case 3:dn(le),tt();break;case 26:case 27:case 5:xt(e);break;case 4:tt();break;case 13:mn(e);break;case 19:st(ue);break;case 10:dn(e.type);break;case 22:case 23:mn(e),fo(),t!==null&&st(li);break;case 24:dn(le)}}function Lr(t,e){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var r=i.next;n=r;do{if((n.tag&t)===t){i=void 0;var a=n.create,m=n.inst;i=a(),m.destroy=i}n=n.next}while(n!==r)}}catch(S){It(e,e.return,S)}}function Ln(t,e,n){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var a=r.next;i=a;do{if((i.tag&t)===t){var m=i.inst,S=m.destroy;if(S!==void 0){m.destroy=void 0,r=e;var L=n,F=S;try{F()}catch(k){It(r,L,k)}}}i=i.next}while(i!==a)}}catch(k){It(e,e.return,k)}}function jh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{wf(e,n)}catch(i){It(t,t.return,i)}}}function Hh(t,e,n){n.props=oi(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(i){It(t,e,i)}}function Ur(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof n=="function"?t.refCleanup=n(i):n.current=i}}catch(r){It(t,e,r)}}function tn(t,e){var n=t.ref,i=t.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(r){It(t,e,r)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(r){It(t,e,r)}else n.current=null}function Yh(t){var e=t.type,n=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break t;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(r){It(t,t.return,r)}}function Ho(t,e,n){try{var i=t.stateNode;L0(i,t.type,n,e),i[De]=e}catch(r){It(t,t.return,r)}}function Vh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Yn(t.type)||t.tag===4}function Yo(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Vh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Yn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Vo(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ul));else if(i!==4&&(i===27&&Yn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Vo(t,e,n),t=t.sibling;t!==null;)Vo(t,e,n),t=t.sibling}function Ja(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(i===27&&Yn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Ja(t,e,n),t=t.sibling;t!==null;)Ja(t,e,n),t=t.sibling}function Gh(t){var e=t.stateNode,n=t.memoizedProps;try{for(var i=t.type,r=e.attributes;r.length;)e.removeAttributeNode(r[0]);ye(e,i,n),e[ge]=t,e[De]=n}catch(a){It(t,t.return,a)}}var vn=!1,ne=!1,Go=!1,Fh=typeof WeakSet=="function"?WeakSet:Set,fe=null;function p0(t,e){if(t=t.containerInfo,ps=dl,t=tf(t),Hu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var r=i.anchorOffset,a=i.focusNode;i=i.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break t}var m=0,S=-1,L=-1,F=0,k=0,nt=t,Q=null;e:for(;;){for(var Z;nt!==n||r!==0&&nt.nodeType!==3||(S=m+r),nt!==a||i!==0&&nt.nodeType!==3||(L=m+i),nt.nodeType===3&&(m+=nt.nodeValue.length),(Z=nt.firstChild)!==null;)Q=nt,nt=Z;for(;;){if(nt===t)break e;if(Q===n&&++F===r&&(S=m),Q===a&&++k===i&&(L=m),(Z=nt.nextSibling)!==null)break;nt=Q,Q=nt.parentNode}nt=Z}n=S===-1||L===-1?null:{start:S,end:L}}else n=null}n=n||{start:0,end:0}}else n=null;for(ds={focusedElem:t,selectionRange:n},dl=!1,fe=e;fe!==null;)if(e=fe,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,fe=t;else for(;fe!==null;){switch(e=fe,a=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&a!==null){t=void 0,n=e,r=a.memoizedProps,a=a.memoizedState,i=n.stateNode;try{var vt=oi(n.type,r,n.elementType===n.type);t=i.getSnapshotBeforeUpdate(vt,a),i.__reactInternalSnapshotBeforeUpdate=t}catch(mt){It(n,n.return,mt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)gs(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gs(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,fe=t;break}fe=e.return}}function Ih(t,e,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Un(t,n),i&4&&Lr(5,n);break;case 1:if(Un(t,n),i&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(m){It(n,n.return,m)}else{var r=oi(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(r,e,t.__reactInternalSnapshotBeforeUpdate)}catch(m){It(n,n.return,m)}}i&64&&jh(n),i&512&&Ur(n,n.return);break;case 3:if(Un(t,n),i&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{wf(t,e)}catch(m){It(n,n.return,m)}}break;case 27:e===null&&i&4&&Gh(n);case 26:case 5:Un(t,n),e===null&&i&4&&Yh(n),i&512&&Ur(n,n.return);break;case 12:Un(t,n);break;case 13:Un(t,n),i&4&&Kh(t,n),i&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=S0.bind(null,n),H0(t,n))));break;case 22:if(i=n.memoizedState!==null||vn,!i){e=e!==null&&e.memoizedState!==null||ne,r=vn;var a=ne;vn=i,(ne=e)&&!a?Bn(t,n,(n.subtreeFlags&8772)!==0):Un(t,n),vn=r,ne=a}break;case 30:break;default:Un(t,n)}}function Qh(t){var e=t.alternate;e!==null&&(t.alternate=null,Qh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&bu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Kt=null,we=!1;function Tn(t,e,n){for(n=n.child;n!==null;)Zh(t,e,n),n=n.sibling}function Zh(t,e,n){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(kt,n)}catch{}switch(n.tag){case 26:ne||tn(n,e),Tn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ne||tn(n,e);var i=Kt,r=we;Yn(n.type)&&(Kt=n.stateNode,we=!1),Tn(t,e,n),Gr(n.stateNode),Kt=i,we=r;break;case 5:ne||tn(n,e);case 6:if(i=Kt,r=we,Kt=null,Tn(t,e,n),Kt=i,we=r,Kt!==null)if(we)try{(Kt.nodeType===9?Kt.body:Kt.nodeName==="HTML"?Kt.ownerDocument.body:Kt).removeChild(n.stateNode)}catch(a){It(n,e,a)}else try{Kt.removeChild(n.stateNode)}catch(a){It(n,e,a)}break;case 18:Kt!==null&&(we?(t=Kt,Bp(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),kr(t)):Bp(Kt,n.stateNode));break;case 4:i=Kt,r=we,Kt=n.stateNode.containerInfo,we=!0,Tn(t,e,n),Kt=i,we=r;break;case 0:case 11:case 14:case 15:ne||Ln(2,n,e),ne||Ln(4,n,e),Tn(t,e,n);break;case 1:ne||(tn(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Hh(n,e,i)),Tn(t,e,n);break;case 21:Tn(t,e,n);break;case 22:ne=(i=ne)||n.memoizedState!==null,Tn(t,e,n),ne=i;break;default:Tn(t,e,n)}}function Kh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{kr(t)}catch(n){It(e,e.return,n)}}function d0(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Fh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Fh),e;default:throw Error(o(435,t.tag))}}function Fo(t,e){var n=d0(t);e.forEach(function(i){var r=D0.bind(null,t,i);n.has(i)||(n.add(i),i.then(r,r))})}function Le(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var r=n[i],a=t,m=e,S=m;t:for(;S!==null;){switch(S.tag){case 27:if(Yn(S.type)){Kt=S.stateNode,we=!1;break t}break;case 5:Kt=S.stateNode,we=!1;break t;case 3:case 4:Kt=S.stateNode.containerInfo,we=!0;break t}S=S.return}if(Kt===null)throw Error(o(160));Zh(a,m,r),Kt=null,we=!1,a=r.alternate,a!==null&&(a.return=null),r.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Ph(e,t),e=e.sibling}var Pe=null;function Ph(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Le(e,t),Ue(t),i&4&&(Ln(3,t,t.return),Lr(3,t),Ln(5,t,t.return));break;case 1:Le(e,t),Ue(t),i&512&&(ne||n===null||tn(n,n.return)),i&64&&vn&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var r=Pe;if(Le(e,t),Ue(t),i&512&&(ne||n===null||tn(n,n.return)),i&4){var a=n!==null?n.memoizedState:null;if(i=t.memoizedState,n===null)if(i===null)if(t.stateNode===null){t:{i=t.type,n=t.memoizedProps,r=r.ownerDocument||r;e:switch(i){case"title":a=r.getElementsByTagName("title")[0],(!a||a[rr]||a[ge]||a.namespaceURI==="http://www.w3.org/2000/svg"||a.hasAttribute("itemprop"))&&(a=r.createElement(i),r.head.insertBefore(a,r.querySelector("head > title"))),ye(a,i,n),a[ge]=t,se(a),i=a;break t;case"link":var m=Gp("link","href",r).get(i+(n.href||""));if(m){for(var S=0;S<m.length;S++)if(a=m[S],a.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&a.getAttribute("rel")===(n.rel==null?null:n.rel)&&a.getAttribute("title")===(n.title==null?null:n.title)&&a.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){m.splice(S,1);break e}}a=r.createElement(i),ye(a,i,n),r.head.appendChild(a);break;case"meta":if(m=Gp("meta","content",r).get(i+(n.content||""))){for(S=0;S<m.length;S++)if(a=m[S],a.getAttribute("content")===(n.content==null?null:""+n.content)&&a.getAttribute("name")===(n.name==null?null:n.name)&&a.getAttribute("property")===(n.property==null?null:n.property)&&a.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&a.getAttribute("charset")===(n.charSet==null?null:n.charSet)){m.splice(S,1);break e}}a=r.createElement(i),ye(a,i,n),r.head.appendChild(a);break;default:throw Error(o(468,i))}a[ge]=t,se(a),i=a}t.stateNode=i}else Fp(r,t.type,t.stateNode);else t.stateNode=Vp(r,i,t.memoizedProps);else a!==i?(a===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):a.count--,i===null?Fp(r,t.type,t.stateNode):Vp(r,i,t.memoizedProps)):i===null&&t.stateNode!==null&&Ho(t,t.memoizedProps,n.memoizedProps)}break;case 27:Le(e,t),Ue(t),i&512&&(ne||n===null||tn(n,n.return)),n!==null&&i&4&&Ho(t,t.memoizedProps,n.memoizedProps);break;case 5:if(Le(e,t),Ue(t),i&512&&(ne||n===null||tn(n,n.return)),t.flags&32){r=t.stateNode;try{Si(r,"")}catch(Z){It(t,t.return,Z)}}i&4&&t.stateNode!=null&&(r=t.memoizedProps,Ho(t,r,n!==null?n.memoizedProps:r)),i&1024&&(Go=!0);break;case 6:if(Le(e,t),Ue(t),i&4){if(t.stateNode===null)throw Error(o(162));i=t.memoizedProps,n=t.stateNode;try{n.nodeValue=i}catch(Z){It(t,t.return,Z)}}break;case 3:if(fl=null,r=Pe,Pe=sl(e.containerInfo),Le(e,t),Pe=r,Ue(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{kr(e.containerInfo)}catch(Z){It(t,t.return,Z)}Go&&(Go=!1,Jh(t));break;case 4:i=Pe,Pe=sl(t.stateNode.containerInfo),Le(e,t),Ue(t),Pe=i;break;case 12:Le(e,t),Ue(t);break;case 13:Le(e,t),Ue(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Jo=p()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Fo(t,i)));break;case 22:r=t.memoizedState!==null;var L=n!==null&&n.memoizedState!==null,F=vn,k=ne;if(vn=F||r,ne=k||L,Le(e,t),ne=k,vn=F,Ue(t),i&8192)t:for(e=t.stateNode,e._visibility=r?e._visibility&-2:e._visibility|1,r&&(n===null||L||vn||ne||si(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){L=n=e;try{if(a=L.stateNode,r)m=a.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{S=L.stateNode;var nt=L.memoizedProps.style,Q=nt!=null&&nt.hasOwnProperty("display")?nt.display:null;S.style.display=Q==null||typeof Q=="boolean"?"":(""+Q).trim()}}catch(Z){It(L,L.return,Z)}}}else if(e.tag===6){if(n===null){L=e;try{L.stateNode.nodeValue=r?"":L.memoizedProps}catch(Z){It(L,L.return,Z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Fo(t,n))));break;case 19:Le(e,t),Ue(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Fo(t,i)));break;case 30:break;case 21:break;default:Le(e,t),Ue(t)}}function Ue(t){var e=t.flags;if(e&2){try{for(var n,i=t.return;i!==null;){if(Vh(i)){n=i;break}i=i.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var r=n.stateNode,a=Yo(t);Ja(t,a,r);break;case 5:var m=n.stateNode;n.flags&32&&(Si(m,""),n.flags&=-33);var S=Yo(t);Ja(t,S,m);break;case 3:case 4:var L=n.stateNode.containerInfo,F=Yo(t);Vo(t,F,L);break;default:throw Error(o(161))}}catch(k){It(t,t.return,k)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Un(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Ih(t,e.alternate,e),e=e.sibling}function si(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Ln(4,e,e.return),si(e);break;case 1:tn(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Hh(e,e.return,n),si(e);break;case 27:Gr(e.stateNode);case 26:case 5:tn(e,e.return),si(e);break;case 22:e.memoizedState===null&&si(e);break;case 30:si(e);break;default:si(e)}t=t.sibling}}function Bn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,r=t,a=e,m=a.flags;switch(a.tag){case 0:case 11:case 15:Bn(r,a,n),Lr(4,a);break;case 1:if(Bn(r,a,n),i=a,r=i.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(F){It(i,i.return,F)}if(i=a,r=i.updateQueue,r!==null){var S=i.stateNode;try{var L=r.shared.hiddenCallbacks;if(L!==null)for(r.shared.hiddenCallbacks=null,r=0;r<L.length;r++)Of(L[r],S)}catch(F){It(i,i.return,F)}}n&&m&64&&jh(a),Ur(a,a.return);break;case 27:Gh(a);case 26:case 5:Bn(r,a,n),n&&i===null&&m&4&&Yh(a),Ur(a,a.return);break;case 12:Bn(r,a,n);break;case 13:Bn(r,a,n),n&&m&4&&Kh(r,a);break;case 22:a.memoizedState===null&&Bn(r,a,n),Ur(a,a.return);break;case 30:break;default:Bn(r,a,n)}e=e.sibling}}function Io(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&vr(n))}function Qo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&vr(t))}function en(t,e,n,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)kh(t,e,n,i),e=e.sibling}function kh(t,e,n,i){var r=e.flags;switch(e.tag){case 0:case 11:case 15:en(t,e,n,i),r&2048&&Lr(9,e);break;case 1:en(t,e,n,i);break;case 3:en(t,e,n,i),r&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&vr(t)));break;case 12:if(r&2048){en(t,e,n,i),t=e.stateNode;try{var a=e.memoizedProps,m=a.id,S=a.onPostCommit;typeof S=="function"&&S(m,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(L){It(e,e.return,L)}}else en(t,e,n,i);break;case 13:en(t,e,n,i);break;case 23:break;case 22:a=e.stateNode,m=e.alternate,e.memoizedState!==null?a._visibility&2?en(t,e,n,i):Br(t,e):a._visibility&2?en(t,e,n,i):(a._visibility|=2,Yi(t,e,n,i,(e.subtreeFlags&10256)!==0)),r&2048&&Io(m,e);break;case 24:en(t,e,n,i),r&2048&&Qo(e.alternate,e);break;default:en(t,e,n,i)}}function Yi(t,e,n,i,r){for(r=r&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var a=t,m=e,S=n,L=i,F=m.flags;switch(m.tag){case 0:case 11:case 15:Yi(a,m,S,L,r),Lr(8,m);break;case 23:break;case 22:var k=m.stateNode;m.memoizedState!==null?k._visibility&2?Yi(a,m,S,L,r):Br(a,m):(k._visibility|=2,Yi(a,m,S,L,r)),r&&F&2048&&Io(m.alternate,m);break;case 24:Yi(a,m,S,L,r),r&&F&2048&&Qo(m.alternate,m);break;default:Yi(a,m,S,L,r)}e=e.sibling}}function Br(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,i=e,r=i.flags;switch(i.tag){case 22:Br(n,i),r&2048&&Io(i.alternate,i);break;case 24:Br(n,i),r&2048&&Qo(i.alternate,i);break;default:Br(n,i)}e=e.sibling}}var Xr=8192;function Vi(t){if(t.subtreeFlags&Xr)for(t=t.child;t!==null;)Wh(t),t=t.sibling}function Wh(t){switch(t.tag){case 26:Vi(t),t.flags&Xr&&t.memoizedState!==null&&$0(Pe,t.memoizedState,t.memoizedProps);break;case 5:Vi(t);break;case 3:case 4:var e=Pe;Pe=sl(t.stateNode.containerInfo),Vi(t),Pe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Xr,Xr=16777216,Vi(t),Xr=e):Vi(t));break;default:Vi(t)}}function $h(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function zr(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];fe=i,ep(i,t)}$h(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)tp(t),t=t.sibling}function tp(t){switch(t.tag){case 0:case 11:case 15:zr(t),t.flags&2048&&Ln(9,t,t.return);break;case 3:zr(t);break;case 12:zr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ka(t)):zr(t);break;default:zr(t)}}function ka(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];fe=i,ep(i,t)}$h(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Ln(8,e,e.return),ka(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ka(e));break;default:ka(e)}t=t.sibling}}function ep(t,e){for(;fe!==null;){var n=fe;switch(n.tag){case 0:case 11:case 15:Ln(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:vr(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,fe=i;else t:for(n=t;fe!==null;){i=fe;var r=i.sibling,a=i.return;if(Qh(i),i===n){fe=null;break t}if(r!==null){r.return=a,fe=r;break t}fe=a}}}var y0={getCacheForType:function(t){var e=Ee(le),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},m0=typeof WeakMap=="function"?WeakMap:Map,jt=0,Zt=null,Mt=null,Ut=0,Ht=0,Be=null,Xn=!1,Gi=!1,Zo=!1,bn=0,$t=0,zn=0,ci=0,Ko=0,Ie=0,Fi=0,qr=null,Ne=null,Po=!1,Jo=0,Wa=1/0,$a=null,qn=null,de=0,jn=null,Ii=null,Qi=0,ko=0,Wo=null,np=null,jr=0,$o=null;function Xe(){if((jt&2)!==0&&Ut!==0)return Ut&-Ut;if(I.T!==null){var t=Li;return t!==0?t:ls()}return gc()}function ip(){Ie===0&&(Ie=(Ut&536870912)===0||zt?pc():536870912);var t=Fe.current;return t!==null&&(t.flags|=32),Ie}function ze(t,e,n){(t===Zt&&(Ht===2||Ht===9)||t.cancelPendingCommit!==null)&&(Zi(t,0),Hn(t,Ut,Ie,!1)),ir(t,n),((jt&2)===0||t!==Zt)&&(t===Zt&&((jt&2)===0&&(ci|=n),$t===4&&Hn(t,Ut,Ie,!1)),nn(t))}function rp(t,e,n){if((jt&6)!==0)throw Error(o(327));var i=!n&&(e&124)===0&&(e&t.expiredLanes)===0||nr(t,e),r=i?v0(t,e):ns(t,e,!0),a=i;do{if(r===0){Gi&&!i&&Hn(t,e,0,!1);break}else{if(n=t.current.alternate,a&&!g0(n)){r=ns(t,e,!1),a=!1;continue}if(r===2){if(a=e,t.errorRecoveryDisabledLanes&a)var m=0;else m=t.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){e=m;t:{var S=t;r=qr;var L=S.current.memoizedState.isDehydrated;if(L&&(Zi(S,m).flags|=256),m=ns(S,m,!1),m!==2){if(Zo&&!L){S.errorRecoveryDisabledLanes|=a,ci|=a,r=4;break t}a=Ne,Ne=r,a!==null&&(Ne===null?Ne=a:Ne.push.apply(Ne,a))}r=m}if(a=!1,r!==2)continue}}if(r===1){Zi(t,0),Hn(t,e,0,!0);break}t:{switch(i=t,a=r,a){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Hn(i,e,Ie,!Xn);break t;case 2:Ne=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(r=Jo+300-p(),10<r)){if(Hn(i,e,Ie,!Xn),ca(i,0,!0)!==0)break t;i.timeoutHandle=Lp(ap.bind(null,i,n,Ne,$a,Po,e,Ie,ci,Fi,Xn,a,2,-0,0),r);break t}ap(i,n,Ne,$a,Po,e,Ie,ci,Fi,Xn,a,0,-0,0)}}break}while(!0);nn(t)}function ap(t,e,n,i,r,a,m,S,L,F,k,nt,Q,Z){if(t.timeoutHandle=-1,nt=e.subtreeFlags,(nt&8192||(nt&16785408)===16785408)&&(Qr={stylesheets:null,count:0,unsuspend:W0},Wh(e),nt=tg(),nt!==null)){t.cancelPendingCommit=nt(hp.bind(null,t,e,a,n,i,r,m,S,L,k,1,Q,Z)),Hn(t,a,m,!F);return}hp(t,e,a,n,i,r,m,S,L)}function g0(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var r=n[i],a=r.getSnapshot;r=r.value;try{if(!Re(a(),r))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Hn(t,e,n,i){e&=~Ko,e&=~ci,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var r=e;0<r;){var a=31-me(r),m=1<<a;i[a]=-1,r&=~m}n!==0&&yc(t,n,e)}function tl(){return(jt&6)===0?(Hr(0),!1):!0}function ts(){if(Mt!==null){if(Ht===0)var t=Mt.return;else t=Mt,pn=ri=null,go(t),ji=null,_r=0,t=Mt;for(;t!==null;)qh(t.alternate,t),t=t.return;Mt=null}}function Zi(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,B0(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),ts(),Zt=t,Mt=n=cn(t.current,null),Ut=e,Ht=0,Be=null,Xn=!1,Gi=nr(t,e),Zo=!1,Fi=Ie=Ko=ci=zn=$t=0,Ne=qr=null,Po=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var r=31-me(i),a=1<<r;e|=t[r],i&=~a}return bn=e,Sa(),n}function lp(t,e){Ot=null,I.H=Ya,e===br||e===Ra?(e=Df(),Ht=3):e===Tf?(e=Df(),Ht=4):Ht=e===Ah?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Be=e,Mt===null&&($t=1,Qa(t,He(e,t.current)))}function up(){var t=I.H;return I.H=Ya,t===null?Ya:t}function op(){var t=I.A;return I.A=y0,t}function es(){$t=4,Xn||(Ut&4194048)!==Ut&&Fe.current!==null||(Gi=!0),(zn&134217727)===0&&(ci&134217727)===0||Zt===null||Hn(Zt,Ut,Ie,!1)}function ns(t,e,n){var i=jt;jt|=2;var r=up(),a=op();(Zt!==t||Ut!==e)&&($a=null,Zi(t,e)),e=!1;var m=$t;t:do try{if(Ht!==0&&Mt!==null){var S=Mt,L=Be;switch(Ht){case 8:ts(),m=6;break t;case 3:case 2:case 9:case 6:Fe.current===null&&(e=!0);var F=Ht;if(Ht=0,Be=null,Ki(t,S,L,F),n&&Gi){m=0;break t}break;default:F=Ht,Ht=0,Be=null,Ki(t,S,L,F)}}E0(),m=$t;break}catch(k){lp(t,k)}while(!0);return e&&t.shellSuspendCounter++,pn=ri=null,jt=i,I.H=r,I.A=a,Mt===null&&(Zt=null,Ut=0,Sa()),m}function E0(){for(;Mt!==null;)sp(Mt)}function v0(t,e){var n=jt;jt|=2;var i=up(),r=op();Zt!==t||Ut!==e?($a=null,Wa=p()+500,Zi(t,e)):Gi=nr(t,e);t:do try{if(Ht!==0&&Mt!==null){e=Mt;var a=Be;e:switch(Ht){case 1:Ht=0,Be=null,Ki(t,e,a,1);break;case 2:case 9:if(bf(a)){Ht=0,Be=null,cp(e);break}e=function(){Ht!==2&&Ht!==9||Zt!==t||(Ht=7),nn(t)},a.then(e,e);break t;case 3:Ht=7;break t;case 4:Ht=5;break t;case 7:bf(a)?(Ht=0,Be=null,cp(e)):(Ht=0,Be=null,Ki(t,e,a,7));break;case 5:var m=null;switch(Mt.tag){case 26:m=Mt.memoizedState;case 5:case 27:var S=Mt;if(!m||Ip(m)){Ht=0,Be=null;var L=S.sibling;if(L!==null)Mt=L;else{var F=S.return;F!==null?(Mt=F,el(F)):Mt=null}break e}}Ht=0,Be=null,Ki(t,e,a,5);break;case 6:Ht=0,Be=null,Ki(t,e,a,6);break;case 8:ts(),$t=6;break t;default:throw Error(o(462))}}T0();break}catch(k){lp(t,k)}while(!0);return pn=ri=null,I.H=i,I.A=r,jt=n,Mt!==null?0:(Zt=null,Ut=0,Sa(),$t)}function T0(){for(;Mt!==null&&!oe();)sp(Mt)}function sp(t){var e=Xh(t.alternate,t,bn);t.memoizedProps=t.pendingProps,e===null?el(t):Mt=e}function cp(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=_h(n,e,e.pendingProps,e.type,void 0,Ut);break;case 11:e=_h(n,e,e.pendingProps,e.type.render,e.ref,Ut);break;case 5:go(e);default:qh(n,e),e=Mt=ff(e,bn),e=Xh(n,e,bn)}t.memoizedProps=t.pendingProps,e===null?el(t):Mt=e}function Ki(t,e,n,i){pn=ri=null,go(e),ji=null,_r=0;var r=e.return;try{if(s0(t,r,e,n,Ut)){$t=1,Qa(t,He(n,t.current)),Mt=null;return}}catch(a){if(r!==null)throw Mt=r,a;$t=1,Qa(t,He(n,t.current)),Mt=null;return}e.flags&32768?(zt||i===1?t=!0:Gi||(Ut&536870912)!==0?t=!1:(Xn=t=!0,(i===2||i===9||i===3||i===6)&&(i=Fe.current,i!==null&&i.tag===13&&(i.flags|=16384))),fp(e,t)):el(e)}function el(t){var e=t;do{if((e.flags&32768)!==0){fp(e,Xn);return}t=e.return;var n=f0(e.alternate,e,bn);if(n!==null){Mt=n;return}if(e=e.sibling,e!==null){Mt=e;return}Mt=e=t}while(e!==null);$t===0&&($t=5)}function fp(t,e){do{var n=h0(t.alternate,t);if(n!==null){n.flags&=32767,Mt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){Mt=t;return}Mt=t=n}while(t!==null);$t=6,Mt=null}function hp(t,e,n,i,r,a,m,S,L){t.cancelPendingCommit=null;do nl();while(de!==0);if((jt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(a=e.lanes|e.childLanes,a|=Iu,Wy(t,n,a,m,S,L),t===Zt&&(Mt=Zt=null,Ut=0),Ii=e,jn=t,Qi=n,ko=a,Wo=r,np=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,A0(at,function(){return gp(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=I.T,I.T=null,r=rt.p,rt.p=2,m=jt,jt|=4;try{p0(t,e,n)}finally{jt=m,rt.p=r,I.T=i}}de=1,pp(),dp(),yp()}}function pp(){if(de===1){de=0;var t=jn,e=Ii,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=I.T,I.T=null;var i=rt.p;rt.p=2;var r=jt;jt|=4;try{Ph(e,t);var a=ds,m=tf(t.containerInfo),S=a.focusedElem,L=a.selectionRange;if(m!==S&&S&&S.ownerDocument&&$c(S.ownerDocument.documentElement,S)){if(L!==null&&Hu(S)){var F=L.start,k=L.end;if(k===void 0&&(k=F),"selectionStart"in S)S.selectionStart=F,S.selectionEnd=Math.min(k,S.value.length);else{var nt=S.ownerDocument||document,Q=nt&&nt.defaultView||window;if(Q.getSelection){var Z=Q.getSelection(),vt=S.textContent.length,mt=Math.min(L.start,vt),Ft=L.end===void 0?mt:Math.min(L.end,vt);!Z.extend&&mt>Ft&&(m=Ft,Ft=mt,mt=m);var H=Wc(S,mt),j=Wc(S,Ft);if(H&&j&&(Z.rangeCount!==1||Z.anchorNode!==H.node||Z.anchorOffset!==H.offset||Z.focusNode!==j.node||Z.focusOffset!==j.offset)){var G=nt.createRange();G.setStart(H.node,H.offset),Z.removeAllRanges(),mt>Ft?(Z.addRange(G),Z.extend(j.node,j.offset)):(G.setEnd(j.node,j.offset),Z.addRange(G))}}}}for(nt=[],Z=S;Z=Z.parentNode;)Z.nodeType===1&&nt.push({element:Z,left:Z.scrollLeft,top:Z.scrollTop});for(typeof S.focus=="function"&&S.focus(),S=0;S<nt.length;S++){var et=nt[S];et.element.scrollLeft=et.left,et.element.scrollTop=et.top}}dl=!!ps,ds=ps=null}finally{jt=r,rt.p=i,I.T=n}}t.current=e,de=2}}function dp(){if(de===2){de=0;var t=jn,e=Ii,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=I.T,I.T=null;var i=rt.p;rt.p=2;var r=jt;jt|=4;try{Ih(t,e.alternate,e)}finally{jt=r,rt.p=i,I.T=n}}de=3}}function yp(){if(de===4||de===3){de=0,w();var t=jn,e=Ii,n=Qi,i=np;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?de=5:(de=0,Ii=jn=null,mp(t,t.pendingLanes));var r=t.pendingLanes;if(r===0&&(qn=null),vu(n),e=e.stateNode,Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(kt,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=I.T,r=rt.p,rt.p=2,I.T=null;try{for(var a=t.onRecoverableError,m=0;m<i.length;m++){var S=i[m];a(S.value,{componentStack:S.stack})}}finally{I.T=e,rt.p=r}}(Qi&3)!==0&&nl(),nn(t),r=t.pendingLanes,(n&4194090)!==0&&(r&42)!==0?t===$o?jr++:(jr=0,$o=t):jr=0,Hr(0)}}function mp(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,vr(e)))}function nl(t){return pp(),dp(),yp(),gp()}function gp(){if(de!==5)return!1;var t=jn,e=ko;ko=0;var n=vu(Qi),i=I.T,r=rt.p;try{rt.p=32>n?32:n,I.T=null,n=Wo,Wo=null;var a=jn,m=Qi;if(de=0,Ii=jn=null,Qi=0,(jt&6)!==0)throw Error(o(331));var S=jt;if(jt|=4,tp(a.current),kh(a,a.current,m,n),jt=S,Hr(0,!1),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(kt,a)}catch{}return!0}finally{rt.p=r,I.T=i,mp(t,e)}}function Ep(t,e,n){e=He(n,e),e=Ro(t.stateNode,e,2),t=Mn(t,e,2),t!==null&&(ir(t,2),nn(t))}function It(t,e,n){if(t.tag===3)Ep(t,t,n);else for(;e!==null;){if(e.tag===3){Ep(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(qn===null||!qn.has(i))){t=He(n,t),n=Sh(2),i=Mn(e,n,2),i!==null&&(Dh(n,i,e,t),ir(i,2),nn(i));break}}e=e.return}}function is(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new m0;var r=new Set;i.set(e,r)}else r=i.get(e),r===void 0&&(r=new Set,i.set(e,r));r.has(n)||(Zo=!0,r.add(n),t=b0.bind(null,t,e,n),e.then(t,t))}function b0(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Zt===t&&(Ut&n)===n&&($t===4||$t===3&&(Ut&62914560)===Ut&&300>p()-Jo?(jt&2)===0&&Zi(t,0):Ko|=n,Fi===Ut&&(Fi=0)),nn(t)}function vp(t,e){e===0&&(e=dc()),t=Mi(t,e),t!==null&&(ir(t,e),nn(t))}function S0(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),vp(t,n)}function D0(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,r=t.memoizedState;r!==null&&(n=r.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(o(314))}i!==null&&i.delete(e),vp(t,n)}function A0(t,e){return Lt(t,e)}var il=null,Pi=null,rs=!1,rl=!1,as=!1,fi=0;function nn(t){t!==Pi&&t.next===null&&(Pi===null?il=Pi=t:Pi=Pi.next=t),rl=!0,rs||(rs=!0,w0())}function Hr(t,e){if(!as&&rl){as=!0;do for(var n=!1,i=il;i!==null;){if(t!==0){var r=i.pendingLanes;if(r===0)var a=0;else{var m=i.suspendedLanes,S=i.pingedLanes;a=(1<<31-me(42|t)+1)-1,a&=r&~(m&~S),a=a&201326741?a&201326741|1:a?a|2:0}a!==0&&(n=!0,Dp(i,a))}else a=Ut,a=ca(i,i===Zt?a:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(a&3)===0||nr(i,a)||(n=!0,Dp(i,a));i=i.next}while(n);as=!1}}function O0(){Tp()}function Tp(){rl=rs=!1;var t=0;fi!==0&&(U0()&&(t=fi),fi=0);for(var e=p(),n=null,i=il;i!==null;){var r=i.next,a=bp(i,e);a===0?(i.next=null,n===null?il=r:n.next=r,r===null&&(Pi=n)):(n=i,(t!==0||(a&3)!==0)&&(rl=!0)),i=r}Hr(t)}function bp(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,r=t.expirationTimes,a=t.pendingLanes&-62914561;0<a;){var m=31-me(a),S=1<<m,L=r[m];L===-1?((S&n)===0||(S&i)!==0)&&(r[m]=ky(S,e)):L<=e&&(t.expiredLanes|=S),a&=~S}if(e=Zt,n=Ut,n=ca(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,n===0||t===e&&(Ht===2||Ht===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&Jt(i),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||nr(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(i!==null&&Jt(i),vu(n)){case 2:case 8:n=V;break;case 32:n=at;break;case 268435456:n=Rt;break;default:n=at}return i=Sp.bind(null,t),n=Lt(n,i),t.callbackPriority=e,t.callbackNode=n,e}return i!==null&&i!==null&&Jt(i),t.callbackPriority=2,t.callbackNode=null,2}function Sp(t,e){if(de!==0&&de!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(nl()&&t.callbackNode!==n)return null;var i=Ut;return i=ca(t,t===Zt?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(rp(t,i,e),bp(t,p()),t.callbackNode!=null&&t.callbackNode===n?Sp.bind(null,t):null)}function Dp(t,e){if(nl())return null;rp(t,e,!0)}function w0(){X0(function(){(jt&6)!==0?Lt(R,O0):Tp()})}function ls(){return fi===0&&(fi=pc()),fi}function Ap(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ya(""+t)}function Op(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function N0(t,e,n,i,r){if(e==="submit"&&n&&n.stateNode===r){var a=Ap((r[De]||null).action),m=i.submitter;m&&(e=(e=m[De]||null)?Ap(e.formAction):m.getAttribute("formAction"),e!==null&&(a=e,m=null));var S=new va("action","action",null,i,r);t.push({event:S,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(fi!==0){var L=m?Op(r,m):new FormData(r);wo(n,{pending:!0,data:L,method:r.method,action:a},null,L)}}else typeof a=="function"&&(S.preventDefault(),L=m?Op(r,m):new FormData(r),wo(n,{pending:!0,data:L,method:r.method,action:a},a,L))},currentTarget:r}]})}}for(var us=0;us<Fu.length;us++){var os=Fu[us],x0=os.toLowerCase(),M0=os[0].toUpperCase()+os.slice(1);Ke(x0,"on"+M0)}Ke(rf,"onAnimationEnd"),Ke(af,"onAnimationIteration"),Ke(lf,"onAnimationStart"),Ke("dblclick","onDoubleClick"),Ke("focusin","onFocus"),Ke("focusout","onBlur"),Ke(Qm,"onTransitionRun"),Ke(Zm,"onTransitionStart"),Ke(Km,"onTransitionCancel"),Ke(uf,"onTransitionEnd"),vi("onMouseEnter",["mouseout","mouseover"]),vi("onMouseLeave",["mouseout","mouseover"]),vi("onPointerEnter",["pointerout","pointerover"]),vi("onPointerLeave",["pointerout","pointerover"]),Pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Pn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Yr));function wp(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],r=i.event;i=i.listeners;t:{var a=void 0;if(e)for(var m=i.length-1;0<=m;m--){var S=i[m],L=S.instance,F=S.currentTarget;if(S=S.listener,L!==a&&r.isPropagationStopped())break t;a=S,r.currentTarget=F;try{a(r)}catch(k){Ia(k)}r.currentTarget=null,a=L}else for(m=0;m<i.length;m++){if(S=i[m],L=S.instance,F=S.currentTarget,S=S.listener,L!==a&&r.isPropagationStopped())break t;a=S,r.currentTarget=F;try{a(r)}catch(k){Ia(k)}r.currentTarget=null,a=L}}}}function _t(t,e){var n=e[Tu];n===void 0&&(n=e[Tu]=new Set);var i=t+"__bubble";n.has(i)||(Np(e,t,2,!1),n.add(i))}function ss(t,e,n){var i=0;e&&(i|=4),Np(n,t,i,e)}var al="_reactListening"+Math.random().toString(36).slice(2);function cs(t){if(!t[al]){t[al]=!0,vc.forEach(function(n){n!=="selectionchange"&&(_0.has(n)||ss(n,!1,t),ss(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[al]||(e[al]=!0,ss("selectionchange",!1,e))}}function Np(t,e,n,i){switch(kp(e)){case 2:var r=ig;break;case 8:r=rg;break;default:r=As}n=r.bind(null,e,n,t),r=void 0,!Ru||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(r=!0),i?r!==void 0?t.addEventListener(e,n,{capture:!0,passive:r}):t.addEventListener(e,n,!0):r!==void 0?t.addEventListener(e,n,{passive:r}):t.addEventListener(e,n,!1)}function fs(t,e,n,i,r){var a=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var m=i.tag;if(m===3||m===4){var S=i.stateNode.containerInfo;if(S===r)break;if(m===4)for(m=i.return;m!==null;){var L=m.tag;if((L===3||L===4)&&m.stateNode.containerInfo===r)return;m=m.return}for(;S!==null;){if(m=mi(S),m===null)return;if(L=m.tag,L===5||L===6||L===26||L===27){i=a=m;continue t}S=S.parentNode}}i=i.return}Lc(function(){var F=a,k=Mu(n),nt=[];t:{var Q=of.get(t);if(Q!==void 0){var Z=va,vt=t;switch(t){case"keypress":if(ga(n)===0)break t;case"keydown":case"keyup":Z=Am;break;case"focusin":vt="focus",Z=Bu;break;case"focusout":vt="blur",Z=Bu;break;case"beforeblur":case"afterblur":Z=Bu;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Z=Xc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Z=hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Z=Nm;break;case rf:case af:case lf:Z=ym;break;case uf:Z=Mm;break;case"scroll":case"scrollend":Z=cm;break;case"wheel":Z=Rm;break;case"copy":case"cut":case"paste":Z=gm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Z=qc;break;case"toggle":case"beforetoggle":Z=Lm}var mt=(e&4)!==0,Ft=!mt&&(t==="scroll"||t==="scrollend"),H=mt?Q!==null?Q+"Capture":null:Q;mt=[];for(var j=F,G;j!==null;){var et=j;if(G=et.stateNode,et=et.tag,et!==5&&et!==26&&et!==27||G===null||H===null||(et=lr(j,H),et!=null&&mt.push(Vr(j,et,G))),Ft)break;j=j.return}0<mt.length&&(Q=new Z(Q,vt,null,n,k),nt.push({event:Q,listeners:mt}))}}if((e&7)===0){t:{if(Q=t==="mouseover"||t==="pointerover",Z=t==="mouseout"||t==="pointerout",Q&&n!==xu&&(vt=n.relatedTarget||n.fromElement)&&(mi(vt)||vt[yi]))break t;if((Z||Q)&&(Q=k.window===k?k:(Q=k.ownerDocument)?Q.defaultView||Q.parentWindow:window,Z?(vt=n.relatedTarget||n.toElement,Z=F,vt=vt?mi(vt):null,vt!==null&&(Ft=c(vt),mt=vt.tag,vt!==Ft||mt!==5&&mt!==27&&mt!==6)&&(vt=null)):(Z=null,vt=F),Z!==vt)){if(mt=Xc,et="onMouseLeave",H="onMouseEnter",j="mouse",(t==="pointerout"||t==="pointerover")&&(mt=qc,et="onPointerLeave",H="onPointerEnter",j="pointer"),Ft=Z==null?Q:ar(Z),G=vt==null?Q:ar(vt),Q=new mt(et,j+"leave",Z,n,k),Q.target=Ft,Q.relatedTarget=G,et=null,mi(k)===F&&(mt=new mt(H,j+"enter",vt,n,k),mt.target=G,mt.relatedTarget=Ft,et=mt),Ft=et,Z&&vt)e:{for(mt=Z,H=vt,j=0,G=mt;G;G=Ji(G))j++;for(G=0,et=H;et;et=Ji(et))G++;for(;0<j-G;)mt=Ji(mt),j--;for(;0<G-j;)H=Ji(H),G--;for(;j--;){if(mt===H||H!==null&&mt===H.alternate)break e;mt=Ji(mt),H=Ji(H)}mt=null}else mt=null;Z!==null&&xp(nt,Q,Z,mt,!1),vt!==null&&Ft!==null&&xp(nt,Ft,vt,mt,!0)}}t:{if(Q=F?ar(F):window,Z=Q.nodeName&&Q.nodeName.toLowerCase(),Z==="select"||Z==="input"&&Q.type==="file")var ht=Qc;else if(Fc(Q))if(Zc)ht=Gm;else{ht=Ym;var Nt=Hm}else Z=Q.nodeName,!Z||Z.toLowerCase()!=="input"||Q.type!=="checkbox"&&Q.type!=="radio"?F&&Nu(F.elementType)&&(ht=Qc):ht=Vm;if(ht&&(ht=ht(t,F))){Ic(nt,ht,n,k);break t}Nt&&Nt(t,Q,F),t==="focusout"&&F&&Q.type==="number"&&F.memoizedProps.value!=null&&wu(Q,"number",Q.value)}switch(Nt=F?ar(F):window,t){case"focusin":(Fc(Nt)||Nt.contentEditable==="true")&&(wi=Nt,Yu=F,dr=null);break;case"focusout":dr=Yu=wi=null;break;case"mousedown":Vu=!0;break;case"contextmenu":case"mouseup":case"dragend":Vu=!1,ef(nt,n,k);break;case"selectionchange":if(Im)break;case"keydown":case"keyup":ef(nt,n,k)}var dt;if(zu)t:{switch(t){case"compositionstart":var gt="onCompositionStart";break t;case"compositionend":gt="onCompositionEnd";break t;case"compositionupdate":gt="onCompositionUpdate";break t}gt=void 0}else Oi?Vc(t,n)&&(gt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(gt="onCompositionStart");gt&&(jc&&n.locale!=="ko"&&(Oi||gt!=="onCompositionStart"?gt==="onCompositionEnd"&&Oi&&(dt=Uc()):(On=k,Cu="value"in On?On.value:On.textContent,Oi=!0)),Nt=ll(F,gt),0<Nt.length&&(gt=new zc(gt,t,null,n,k),nt.push({event:gt,listeners:Nt}),dt?gt.data=dt:(dt=Gc(n),dt!==null&&(gt.data=dt)))),(dt=Bm?Xm(t,n):zm(t,n))&&(gt=ll(F,"onBeforeInput"),0<gt.length&&(Nt=new zc("onBeforeInput","beforeinput",null,n,k),nt.push({event:Nt,listeners:gt}),Nt.data=dt)),N0(nt,t,F,n,k)}wp(nt,e)})}function Vr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function ll(t,e){for(var n=e+"Capture",i=[];t!==null;){var r=t,a=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||a===null||(r=lr(t,n),r!=null&&i.unshift(Vr(t,r,a)),r=lr(t,e),r!=null&&i.push(Vr(t,r,a))),t.tag===3)return i;t=t.return}return[]}function Ji(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function xp(t,e,n,i,r){for(var a=e._reactName,m=[];n!==null&&n!==i;){var S=n,L=S.alternate,F=S.stateNode;if(S=S.tag,L!==null&&L===i)break;S!==5&&S!==26&&S!==27||F===null||(L=F,r?(F=lr(n,a),F!=null&&m.unshift(Vr(n,F,L))):r||(F=lr(n,a),F!=null&&m.push(Vr(n,F,L)))),n=n.return}m.length!==0&&t.push({event:e,listeners:m})}var R0=/\r\n?/g,C0=/\u0000|\uFFFD/g;function Mp(t){return(typeof t=="string"?t:""+t).replace(R0,`
`).replace(C0,"")}function _p(t,e){return e=Mp(e),Mp(t)===e}function ul(){}function Gt(t,e,n,i,r,a){switch(n){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||Si(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&Si(t,""+i);break;case"className":ha(t,"class",i);break;case"tabIndex":ha(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":ha(t,n,i);break;case"style":Rc(t,i,a);break;case"data":if(e!=="object"){ha(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=ya(""+i),t.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof a=="function"&&(n==="formAction"?(e!=="input"&&Gt(t,e,"name",r.name,r,null),Gt(t,e,"formEncType",r.formEncType,r,null),Gt(t,e,"formMethod",r.formMethod,r,null),Gt(t,e,"formTarget",r.formTarget,r,null)):(Gt(t,e,"encType",r.encType,r,null),Gt(t,e,"method",r.method,r,null),Gt(t,e,"target",r.target,r,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=ya(""+i),t.setAttribute(n,i);break;case"onClick":i!=null&&(t.onclick=ul);break;case"onScroll":i!=null&&_t("scroll",t);break;case"onScrollEnd":i!=null&&_t("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(r.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}n=ya(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""+i):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":i===!0?t.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,i):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(n,i):t.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(n):t.setAttribute(n,i);break;case"popover":_t("beforetoggle",t),_t("toggle",t),fa(t,"popover",i);break;case"xlinkActuate":on(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":on(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":on(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":on(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":on(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":on(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":on(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":on(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":on(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":fa(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=om.get(n)||n,fa(t,n,i))}}function hs(t,e,n,i,r,a){switch(n){case"style":Rc(t,i,a);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(r.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof i=="string"?Si(t,i):(typeof i=="number"||typeof i=="bigint")&&Si(t,""+i);break;case"onScroll":i!=null&&_t("scroll",t);break;case"onScrollEnd":i!=null&&_t("scrollend",t);break;case"onClick":i!=null&&(t.onclick=ul);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Tc.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(r=n.endsWith("Capture"),e=n.slice(2,r?n.length-7:void 0),a=t[De]||null,a=a!=null?a[n]:null,typeof a=="function"&&t.removeEventListener(e,a,r),typeof i=="function")){typeof a!="function"&&a!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,i,r);break t}n in t?t[n]=i:i===!0?t.setAttribute(n,""):fa(t,n,i)}}}function ye(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_t("error",t),_t("load",t);var i=!1,r=!1,a;for(a in n)if(n.hasOwnProperty(a)){var m=n[a];if(m!=null)switch(a){case"src":i=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Gt(t,e,a,m,n,null)}}r&&Gt(t,e,"srcSet",n.srcSet,n,null),i&&Gt(t,e,"src",n.src,n,null);return;case"input":_t("invalid",t);var S=a=m=r=null,L=null,F=null;for(i in n)if(n.hasOwnProperty(i)){var k=n[i];if(k!=null)switch(i){case"name":r=k;break;case"type":m=k;break;case"checked":L=k;break;case"defaultChecked":F=k;break;case"value":a=k;break;case"defaultValue":S=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(o(137,e));break;default:Gt(t,e,i,k,n,null)}}Nc(t,a,S,L,F,m,r,!1),pa(t);return;case"select":_t("invalid",t),i=m=a=null;for(r in n)if(n.hasOwnProperty(r)&&(S=n[r],S!=null))switch(r){case"value":a=S;break;case"defaultValue":m=S;break;case"multiple":i=S;default:Gt(t,e,r,S,n,null)}e=a,n=m,t.multiple=!!i,e!=null?bi(t,!!i,e,!1):n!=null&&bi(t,!!i,n,!0);return;case"textarea":_t("invalid",t),a=r=i=null;for(m in n)if(n.hasOwnProperty(m)&&(S=n[m],S!=null))switch(m){case"value":i=S;break;case"defaultValue":r=S;break;case"children":a=S;break;case"dangerouslySetInnerHTML":if(S!=null)throw Error(o(91));break;default:Gt(t,e,m,S,n,null)}Mc(t,i,r,a),pa(t);return;case"option":for(L in n)if(n.hasOwnProperty(L)&&(i=n[L],i!=null))switch(L){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Gt(t,e,L,i,n,null)}return;case"dialog":_t("beforetoggle",t),_t("toggle",t),_t("cancel",t),_t("close",t);break;case"iframe":case"object":_t("load",t);break;case"video":case"audio":for(i=0;i<Yr.length;i++)_t(Yr[i],t);break;case"image":_t("error",t),_t("load",t);break;case"details":_t("toggle",t);break;case"embed":case"source":case"link":_t("error",t),_t("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(F in n)if(n.hasOwnProperty(F)&&(i=n[F],i!=null))switch(F){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Gt(t,e,F,i,n,null)}return;default:if(Nu(e)){for(k in n)n.hasOwnProperty(k)&&(i=n[k],i!==void 0&&hs(t,e,k,i,n,void 0));return}}for(S in n)n.hasOwnProperty(S)&&(i=n[S],i!=null&&Gt(t,e,S,i,n,null))}function L0(t,e,n,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,a=null,m=null,S=null,L=null,F=null,k=null;for(Z in n){var nt=n[Z];if(n.hasOwnProperty(Z)&&nt!=null)switch(Z){case"checked":break;case"value":break;case"defaultValue":L=nt;default:i.hasOwnProperty(Z)||Gt(t,e,Z,null,i,nt)}}for(var Q in i){var Z=i[Q];if(nt=n[Q],i.hasOwnProperty(Q)&&(Z!=null||nt!=null))switch(Q){case"type":a=Z;break;case"name":r=Z;break;case"checked":F=Z;break;case"defaultChecked":k=Z;break;case"value":m=Z;break;case"defaultValue":S=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(o(137,e));break;default:Z!==nt&&Gt(t,e,Q,Z,i,nt)}}Ou(t,m,S,L,F,k,a,r);return;case"select":Z=m=S=Q=null;for(a in n)if(L=n[a],n.hasOwnProperty(a)&&L!=null)switch(a){case"value":break;case"multiple":Z=L;default:i.hasOwnProperty(a)||Gt(t,e,a,null,i,L)}for(r in i)if(a=i[r],L=n[r],i.hasOwnProperty(r)&&(a!=null||L!=null))switch(r){case"value":Q=a;break;case"defaultValue":S=a;break;case"multiple":m=a;default:a!==L&&Gt(t,e,r,a,i,L)}e=S,n=m,i=Z,Q!=null?bi(t,!!n,Q,!1):!!i!=!!n&&(e!=null?bi(t,!!n,e,!0):bi(t,!!n,n?[]:"",!1));return;case"textarea":Z=Q=null;for(S in n)if(r=n[S],n.hasOwnProperty(S)&&r!=null&&!i.hasOwnProperty(S))switch(S){case"value":break;case"children":break;default:Gt(t,e,S,null,i,r)}for(m in i)if(r=i[m],a=n[m],i.hasOwnProperty(m)&&(r!=null||a!=null))switch(m){case"value":Q=r;break;case"defaultValue":Z=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:r!==a&&Gt(t,e,m,r,i,a)}xc(t,Q,Z);return;case"option":for(var vt in n)if(Q=n[vt],n.hasOwnProperty(vt)&&Q!=null&&!i.hasOwnProperty(vt))switch(vt){case"selected":t.selected=!1;break;default:Gt(t,e,vt,null,i,Q)}for(L in i)if(Q=i[L],Z=n[L],i.hasOwnProperty(L)&&Q!==Z&&(Q!=null||Z!=null))switch(L){case"selected":t.selected=Q&&typeof Q!="function"&&typeof Q!="symbol";break;default:Gt(t,e,L,Q,i,Z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var mt in n)Q=n[mt],n.hasOwnProperty(mt)&&Q!=null&&!i.hasOwnProperty(mt)&&Gt(t,e,mt,null,i,Q);for(F in i)if(Q=i[F],Z=n[F],i.hasOwnProperty(F)&&Q!==Z&&(Q!=null||Z!=null))switch(F){case"children":case"dangerouslySetInnerHTML":if(Q!=null)throw Error(o(137,e));break;default:Gt(t,e,F,Q,i,Z)}return;default:if(Nu(e)){for(var Ft in n)Q=n[Ft],n.hasOwnProperty(Ft)&&Q!==void 0&&!i.hasOwnProperty(Ft)&&hs(t,e,Ft,void 0,i,Q);for(k in i)Q=i[k],Z=n[k],!i.hasOwnProperty(k)||Q===Z||Q===void 0&&Z===void 0||hs(t,e,k,Q,i,Z);return}}for(var H in n)Q=n[H],n.hasOwnProperty(H)&&Q!=null&&!i.hasOwnProperty(H)&&Gt(t,e,H,null,i,Q);for(nt in i)Q=i[nt],Z=n[nt],!i.hasOwnProperty(nt)||Q===Z||Q==null&&Z==null||Gt(t,e,nt,Q,i,Z)}var ps=null,ds=null;function ol(t){return t.nodeType===9?t:t.ownerDocument}function Rp(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Cp(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ys(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ms=null;function U0(){var t=window.event;return t&&t.type==="popstate"?t===ms?!1:(ms=t,!0):(ms=null,!1)}var Lp=typeof setTimeout=="function"?setTimeout:void 0,B0=typeof clearTimeout=="function"?clearTimeout:void 0,Up=typeof Promise=="function"?Promise:void 0,X0=typeof queueMicrotask=="function"?queueMicrotask:typeof Up<"u"?function(t){return Up.resolve(null).then(t).catch(z0)}:Lp;function z0(t){setTimeout(function(){throw t})}function Yn(t){return t==="head"}function Bp(t,e){var n=e,i=0,r=0;do{var a=n.nextSibling;if(t.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(0<i&&8>i){n=i;var m=t.ownerDocument;if(n&1&&Gr(m.documentElement),n&2&&Gr(m.body),n&4)for(n=m.head,Gr(n),m=n.firstChild;m;){var S=m.nextSibling,L=m.nodeName;m[rr]||L==="SCRIPT"||L==="STYLE"||L==="LINK"&&m.rel.toLowerCase()==="stylesheet"||n.removeChild(m),m=S}}if(r===0){t.removeChild(a),kr(e);return}r--}else n==="$"||n==="$?"||n==="$!"?r++:i=n.charCodeAt(0)-48;else i=0;n=a}while(n);kr(e)}function gs(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gs(n),bu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function q0(t,e,n,i){for(;t.nodeType===1;){var r=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[rr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(a=t.getAttribute("rel"),a==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(a!==r.rel||t.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||t.getAttribute("title")!==(r.title==null?null:r.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(a=t.getAttribute("src"),(a!==(r.src==null?null:r.src)||t.getAttribute("type")!==(r.type==null?null:r.type)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&a&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var a=r.name==null?null:""+r.name;if(r.type==="hidden"&&t.getAttribute("name")===a)return t}else return t;if(t=Je(t.nextSibling),t===null)break}return null}function j0(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Je(t.nextSibling),t===null))return null;return t}function Es(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function H0(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var i=function(){e(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function Je(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var vs=null;function Xp(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function zp(t,e,n){switch(e=ol(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Gr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);bu(t)}var Qe=new Map,qp=new Set;function sl(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Sn=rt.d;rt.d={f:Y0,r:V0,D:G0,C:F0,L:I0,m:Q0,X:K0,S:Z0,M:P0};function Y0(){var t=Sn.f(),e=tl();return t||e}function V0(t){var e=gi(t);e!==null&&e.tag===5&&e.type==="form"?rh(e):Sn.r(t)}var ki=typeof document>"u"?null:document;function jp(t,e,n){var i=ki;if(i&&typeof e=="string"&&e){var r=je(e);r='link[rel="'+t+'"][href="'+r+'"]',typeof n=="string"&&(r+='[crossorigin="'+n+'"]'),qp.has(r)||(qp.add(r),t={rel:t,crossOrigin:n,href:e},i.querySelector(r)===null&&(e=i.createElement("link"),ye(e,"link",t),se(e),i.head.appendChild(e)))}}function G0(t){Sn.D(t),jp("dns-prefetch",t,null)}function F0(t,e){Sn.C(t,e),jp("preconnect",t,e)}function I0(t,e,n){Sn.L(t,e,n);var i=ki;if(i&&t&&e){var r='link[rel="preload"][as="'+je(e)+'"]';e==="image"&&n&&n.imageSrcSet?(r+='[imagesrcset="'+je(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(r+='[imagesizes="'+je(n.imageSizes)+'"]')):r+='[href="'+je(t)+'"]';var a=r;switch(e){case"style":a=Wi(t);break;case"script":a=$i(t)}Qe.has(a)||(t=D({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Qe.set(a,t),i.querySelector(r)!==null||e==="style"&&i.querySelector(Fr(a))||e==="script"&&i.querySelector(Ir(a))||(e=i.createElement("link"),ye(e,"link",t),se(e),i.head.appendChild(e)))}}function Q0(t,e){Sn.m(t,e);var n=ki;if(n&&t){var i=e&&typeof e.as=="string"?e.as:"script",r='link[rel="modulepreload"][as="'+je(i)+'"][href="'+je(t)+'"]',a=r;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=$i(t)}if(!Qe.has(a)&&(t=D({rel:"modulepreload",href:t},e),Qe.set(a,t),n.querySelector(r)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ir(a)))return}i=n.createElement("link"),ye(i,"link",t),se(i),n.head.appendChild(i)}}}function Z0(t,e,n){Sn.S(t,e,n);var i=ki;if(i&&t){var r=Ei(i).hoistableStyles,a=Wi(t);e=e||"default";var m=r.get(a);if(!m){var S={loading:0,preload:null};if(m=i.querySelector(Fr(a)))S.loading=5;else{t=D({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Qe.get(a))&&Ts(t,n);var L=m=i.createElement("link");se(L),ye(L,"link",t),L._p=new Promise(function(F,k){L.onload=F,L.onerror=k}),L.addEventListener("load",function(){S.loading|=1}),L.addEventListener("error",function(){S.loading|=2}),S.loading|=4,cl(m,e,i)}m={type:"stylesheet",instance:m,count:1,state:S},r.set(a,m)}}}function K0(t,e){Sn.X(t,e);var n=ki;if(n&&t){var i=Ei(n).hoistableScripts,r=$i(t),a=i.get(r);a||(a=n.querySelector(Ir(r)),a||(t=D({src:t,async:!0},e),(e=Qe.get(r))&&bs(t,e),a=n.createElement("script"),se(a),ye(a,"link",t),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}}function P0(t,e){Sn.M(t,e);var n=ki;if(n&&t){var i=Ei(n).hoistableScripts,r=$i(t),a=i.get(r);a||(a=n.querySelector(Ir(r)),a||(t=D({src:t,async:!0,type:"module"},e),(e=Qe.get(r))&&bs(t,e),a=n.createElement("script"),se(a),ye(a,"link",t),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}}function Hp(t,e,n,i){var r=(r=Et.current)?sl(r):null;if(!r)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Wi(n.href),n=Ei(r).hoistableStyles,i=n.get(e),i||(i={type:"style",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Wi(n.href);var a=Ei(r).hoistableStyles,m=a.get(t);if(m||(r=r.ownerDocument||r,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},a.set(t,m),(a=r.querySelector(Fr(t)))&&!a._p&&(m.instance=a,m.state.loading=5),Qe.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Qe.set(t,n),a||J0(r,t,n,m.state))),e&&i===null)throw Error(o(528,""));return m}if(e&&i!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$i(n),n=Ei(r).hoistableScripts,i=n.get(e),i||(i={type:"script",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Wi(t){return'href="'+je(t)+'"'}function Fr(t){return'link[rel="stylesheet"]['+t+"]"}function Yp(t){return D({},t,{"data-precedence":t.precedence,precedence:null})}function J0(t,e,n,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),ye(e,"link",n),se(e),t.head.appendChild(e))}function $i(t){return'[src="'+je(t)+'"]'}function Ir(t){return"script[async]"+t}function Vp(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+je(n.href)+'"]');if(i)return e.instance=i,se(i),i;var r=D({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),se(i),ye(i,"style",r),cl(i,n.precedence,t),e.instance=i;case"stylesheet":r=Wi(n.href);var a=t.querySelector(Fr(r));if(a)return e.state.loading|=4,e.instance=a,se(a),a;i=Yp(n),(r=Qe.get(r))&&Ts(i,r),a=(t.ownerDocument||t).createElement("link"),se(a);var m=a;return m._p=new Promise(function(S,L){m.onload=S,m.onerror=L}),ye(a,"link",i),e.state.loading|=4,cl(a,n.precedence,t),e.instance=a;case"script":return a=$i(n.src),(r=t.querySelector(Ir(a)))?(e.instance=r,se(r),r):(i=n,(r=Qe.get(a))&&(i=D({},n),bs(i,r)),t=t.ownerDocument||t,r=t.createElement("script"),se(r),ye(r,"link",i),t.head.appendChild(r),e.instance=r);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,cl(i,n.precedence,t));return e.instance}function cl(t,e,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=i.length?i[i.length-1]:null,a=r,m=0;m<i.length;m++){var S=i[m];if(S.dataset.precedence===e)a=S;else if(a!==r)break}a?a.parentNode.insertBefore(t,a.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Ts(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function bs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var fl=null;function Gp(t,e,n){if(fl===null){var i=new Map,r=fl=new Map;r.set(n,i)}else r=fl,i=r.get(n),i||(i=new Map,r.set(n,i));if(i.has(t))return i;for(i.set(t,null),n=n.getElementsByTagName(t),r=0;r<n.length;r++){var a=n[r];if(!(a[rr]||a[ge]||t==="link"&&a.getAttribute("rel")==="stylesheet")&&a.namespaceURI!=="http://www.w3.org/2000/svg"){var m=a.getAttribute(e)||"";m=t+m;var S=i.get(m);S?S.push(a):i.set(m,[a])}}return i}function Fp(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function k0(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ip(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Qr=null;function W0(){}function $0(t,e,n){if(Qr===null)throw Error(o(475));var i=Qr;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var r=Wi(n.href),a=t.querySelector(Fr(r));if(a){t=a._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=hl.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=a,se(a);return}a=t.ownerDocument||t,n=Yp(n),(r=Qe.get(r))&&Ts(n,r),a=a.createElement("link"),se(a);var m=a;m._p=new Promise(function(S,L){m.onload=S,m.onerror=L}),ye(a,"link",n),e.instance=a}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=hl.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function tg(){if(Qr===null)throw Error(o(475));var t=Qr;return t.stylesheets&&t.count===0&&Ss(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Ss(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function hl(){if(this.count--,this.count===0){if(this.stylesheets)Ss(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var pl=null;function Ss(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,pl=new Map,e.forEach(eg,t),pl=null,hl.call(t))}function eg(t,e){if(!(e.state.loading&4)){var n=pl.get(t);if(n)var i=n.get(null);else{n=new Map,pl.set(t,n);for(var r=t.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<r.length;a++){var m=r[a];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(n.set(m.dataset.precedence,m),i=m)}i&&n.set(null,i)}r=e.instance,m=r.getAttribute("data-precedence"),a=n.get(m)||i,a===i&&n.set(null,r),n.set(m,r),this.count++,i=hl.bind(this),r.addEventListener("load",i),r.addEventListener("error",i),a?a.parentNode.insertBefore(r,a.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(r,t.firstChild)),e.state.loading|=4}}var Zr={$$typeof:v,Provider:null,Consumer:null,_currentValue:yt,_currentValue2:yt,_threadCount:0};function ng(t,e,n,i,r,a,m,S){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gu(0),this.hiddenUpdates=gu(null),this.identifierPrefix=i,this.onUncaughtError=r,this.onCaughtError=a,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=S,this.incompleteTransitions=new Map}function Qp(t,e,n,i,r,a,m,S,L,F,k,nt){return t=new ng(t,e,n,m,S,L,F,nt),e=1,a===!0&&(e|=24),a=Ce(3,null,null,e),t.current=a,a.stateNode=t,e=no(),e.refCount++,t.pooledCache=e,e.refCount++,a.memoizedState={element:i,isDehydrated:n,cache:e},lo(a),t}function Zp(t){return t?(t=_i,t):_i}function Kp(t,e,n,i,r,a){r=Zp(r),i.context===null?i.context=r:i.pendingContext=r,i=xn(e),i.payload={element:n},a=a===void 0?null:a,a!==null&&(i.callback=a),n=Mn(t,i,e),n!==null&&(ze(n,t,e),Dr(n,t,e))}function Pp(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Ds(t,e){Pp(t,e),(t=t.alternate)&&Pp(t,e)}function Jp(t){if(t.tag===13){var e=Mi(t,67108864);e!==null&&ze(e,t,67108864),Ds(t,67108864)}}var dl=!0;function ig(t,e,n,i){var r=I.T;I.T=null;var a=rt.p;try{rt.p=2,As(t,e,n,i)}finally{rt.p=a,I.T=r}}function rg(t,e,n,i){var r=I.T;I.T=null;var a=rt.p;try{rt.p=8,As(t,e,n,i)}finally{rt.p=a,I.T=r}}function As(t,e,n,i){if(dl){var r=Os(i);if(r===null)fs(t,e,i,yl,n),Wp(t,i);else if(lg(r,t,e,n,i))i.stopPropagation();else if(Wp(t,i),e&4&&-1<ag.indexOf(t)){for(;r!==null;){var a=gi(r);if(a!==null)switch(a.tag){case 3:if(a=a.stateNode,a.current.memoizedState.isDehydrated){var m=Kn(a.pendingLanes);if(m!==0){var S=a;for(S.pendingLanes|=2,S.entangledLanes|=2;m;){var L=1<<31-me(m);S.entanglements[1]|=L,m&=~L}nn(a),(jt&6)===0&&(Wa=p()+500,Hr(0))}}break;case 13:S=Mi(a,2),S!==null&&ze(S,a,2),tl(),Ds(a,2)}if(a=Os(i),a===null&&fs(t,e,i,yl,n),a===r)break;r=a}r!==null&&i.stopPropagation()}else fs(t,e,i,null,n)}}function Os(t){return t=Mu(t),ws(t)}var yl=null;function ws(t){if(yl=null,t=mi(t),t!==null){var e=c(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=s(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return yl=t,null}function kp(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(y()){case R:return 2;case V:return 8;case at:case ut:return 32;case Rt:return 268435456;default:return 32}default:return 32}}var Ns=!1,Vn=null,Gn=null,Fn=null,Kr=new Map,Pr=new Map,In=[],ag="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Wp(t,e){switch(t){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":Gn=null;break;case"mouseover":case"mouseout":Fn=null;break;case"pointerover":case"pointerout":Kr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pr.delete(e.pointerId)}}function Jr(t,e,n,i,r,a){return t===null||t.nativeEvent!==a?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:a,targetContainers:[r]},e!==null&&(e=gi(e),e!==null&&Jp(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,r!==null&&e.indexOf(r)===-1&&e.push(r),t)}function lg(t,e,n,i,r){switch(e){case"focusin":return Vn=Jr(Vn,t,e,n,i,r),!0;case"dragenter":return Gn=Jr(Gn,t,e,n,i,r),!0;case"mouseover":return Fn=Jr(Fn,t,e,n,i,r),!0;case"pointerover":var a=r.pointerId;return Kr.set(a,Jr(Kr.get(a)||null,t,e,n,i,r)),!0;case"gotpointercapture":return a=r.pointerId,Pr.set(a,Jr(Pr.get(a)||null,t,e,n,i,r)),!0}return!1}function $p(t){var e=mi(t.target);if(e!==null){var n=c(e);if(n!==null){if(e=n.tag,e===13){if(e=s(n),e!==null){t.blockedOn=e,$y(t.priority,function(){if(n.tag===13){var i=Xe();i=Eu(i);var r=Mi(n,i);r!==null&&ze(r,n,i),Ds(n,i)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ml(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Os(t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);xu=i,n.target.dispatchEvent(i),xu=null}else return e=gi(n),e!==null&&Jp(e),t.blockedOn=n,!1;e.shift()}return!0}function td(t,e,n){ml(t)&&n.delete(e)}function ug(){Ns=!1,Vn!==null&&ml(Vn)&&(Vn=null),Gn!==null&&ml(Gn)&&(Gn=null),Fn!==null&&ml(Fn)&&(Fn=null),Kr.forEach(td),Pr.forEach(td)}function gl(t,e){t.blockedOn===e&&(t.blockedOn=null,Ns||(Ns=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,ug)))}var El=null;function ed(t){El!==t&&(El=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){El===t&&(El=null);for(var e=0;e<t.length;e+=3){var n=t[e],i=t[e+1],r=t[e+2];if(typeof i!="function"){if(ws(i||n)===null)continue;break}var a=gi(n);a!==null&&(t.splice(e,3),e-=3,wo(a,{pending:!0,data:r,method:n.method,action:i},i,r))}}))}function kr(t){function e(L){return gl(L,t)}Vn!==null&&gl(Vn,t),Gn!==null&&gl(Gn,t),Fn!==null&&gl(Fn,t),Kr.forEach(e),Pr.forEach(e);for(var n=0;n<In.length;n++){var i=In[n];i.blockedOn===t&&(i.blockedOn=null)}for(;0<In.length&&(n=In[0],n.blockedOn===null);)$p(n),n.blockedOn===null&&In.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var r=n[i],a=n[i+1],m=r[De]||null;if(typeof a=="function")m||ed(n);else if(m){var S=null;if(a&&a.hasAttribute("formAction")){if(r=a,m=a[De]||null)S=m.formAction;else if(ws(r)!==null)continue}else S=m.action;typeof S=="function"?n[i+1]=S:(n.splice(i,3),i-=3),ed(n)}}}function xs(t){this._internalRoot=t}vl.prototype.render=xs.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,i=Xe();Kp(n,i,t,e,null,null)},vl.prototype.unmount=xs.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Kp(t.current,2,null,t,null,null),tl(),e[yi]=null}};function vl(t){this._internalRoot=t}vl.prototype.unstable_scheduleHydration=function(t){if(t){var e=gc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<In.length&&e!==0&&e<In[n].priority;n++);In.splice(n,0,t),n===0&&$p(t)}};var nd=l.version;if(nd!=="19.1.1")throw Error(o(527,nd,"19.1.1"));rt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=g(e),t=t!==null?b(t):null,t=t===null?null:t.stateNode,t};var og={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:I,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Tl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Tl.isDisabled&&Tl.supportsFiber)try{kt=Tl.inject(og),Ct=Tl}catch{}}return $r.createRoot=function(t,e){if(!d(t))throw Error(o(299));var n=!1,i="",r=Eh,a=vh,m=Th,S=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(r=e.onUncaughtError),e.onCaughtError!==void 0&&(a=e.onCaughtError),e.onRecoverableError!==void 0&&(m=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(S=e.unstable_transitionCallbacks)),e=Qp(t,1,!1,null,null,n,i,r,a,m,S,null),t[yi]=e.current,cs(t),new xs(e)},$r.hydrateRoot=function(t,e,n){if(!d(t))throw Error(o(299));var i=!1,r="",a=Eh,m=vh,S=Th,L=null,F=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(a=n.onUncaughtError),n.onCaughtError!==void 0&&(m=n.onCaughtError),n.onRecoverableError!==void 0&&(S=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(L=n.unstable_transitionCallbacks),n.formState!==void 0&&(F=n.formState)),e=Qp(t,1,!0,e,n??null,i,r,a,m,S,L,F),e.context=Zp(null),n=e.current,i=Xe(),i=Eu(i),r=xn(i),r.callback=null,Mn(n,r,i),n=i,e.current.lanes=n,ir(e,n),nn(e),t[yi]=e.current,cs(t),new vl(e)},$r.version="19.1.1",$r}var hd;function vg(){if(hd)return Rs.exports;hd=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(l){console.error(l)}}return u(),Rs.exports=Eg(),Rs.exports}var Tg=vg();const bg=()=>{const[u,l]=Te.useState(navigator.onLine),[h,o]=Te.useState(new Date);return Te.useEffect(()=>{const d=()=>{l(!0),o(new Date)},c=()=>{l(!1),o(new Date)},s=async()=>{try{const g=await fetch("https://www.google.com/favicon.ico",{method:"HEAD",mode:"no-cors",cache:"no-cache"});l(!0)}catch{l(!1)}o(new Date)};window.addEventListener("online",d),window.addEventListener("offline",c);const f=setInterval(s,3e4);return s(),()=>{window.removeEventListener("online",d),window.removeEventListener("offline",c),clearInterval(f)}},[]),ft.jsxs("div",{className:"status-container",children:[ft.jsx("div",{className:"status-header",children:ft.jsx("h3",{children:"Internet Connection"})}),ft.jsxs("div",{className:`status-indicator ${u?"online":"offline"}`,children:[ft.jsx("div",{className:"status-icon",children:u?"🌐":"❌"}),ft.jsxs("div",{className:"status-details",children:[ft.jsx("div",{className:"status-text",children:u?"Connected":"Disconnected"}),ft.jsxs("div",{className:"status-timestamp",children:["Last checked: ",h.toLocaleTimeString()]})]})]})]})};function gy(u,l){return function(){return u.apply(l,arguments)}}const{toString:Sg}=Object.prototype,{getPrototypeOf:Ps}=Object,{iterator:su,toStringTag:Ey}=Symbol,cu=(u=>l=>{const h=Sg.call(l);return u[h]||(u[h]=h.slice(8,-1).toLowerCase())})(Object.create(null)),ke=u=>(u=u.toLowerCase(),l=>cu(l)===u),fu=u=>l=>typeof l===u,{isArray:tr}=Array,na=fu("undefined");function ia(u){return u!==null&&!na(u)&&u.constructor!==null&&!na(u.constructor)&&xe(u.constructor.isBuffer)&&u.constructor.isBuffer(u)}const vy=ke("ArrayBuffer");function Dg(u){let l;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?l=ArrayBuffer.isView(u):l=u&&u.buffer&&vy(u.buffer),l}const Ag=fu("string"),xe=fu("function"),Ty=fu("number"),ra=u=>u!==null&&typeof u=="object",Og=u=>u===!0||u===!1,Nl=u=>{if(cu(u)!=="object")return!1;const l=Ps(u);return(l===null||l===Object.prototype||Object.getPrototypeOf(l)===null)&&!(Ey in u)&&!(su in u)},wg=u=>{if(!ra(u)||ia(u))return!1;try{return Object.keys(u).length===0&&Object.getPrototypeOf(u)===Object.prototype}catch{return!1}},Ng=ke("Date"),xg=ke("File"),Mg=ke("Blob"),_g=ke("FileList"),Rg=u=>ra(u)&&xe(u.pipe),Cg=u=>{let l;return u&&(typeof FormData=="function"&&u instanceof FormData||xe(u.append)&&((l=cu(u))==="formdata"||l==="object"&&xe(u.toString)&&u.toString()==="[object FormData]"))},Lg=ke("URLSearchParams"),[Ug,Bg,Xg,zg]=["ReadableStream","Request","Response","Headers"].map(ke),qg=u=>u.trim?u.trim():u.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function aa(u,l,{allOwnKeys:h=!1}={}){if(u===null||typeof u>"u")return;let o,d;if(typeof u!="object"&&(u=[u]),tr(u))for(o=0,d=u.length;o<d;o++)l.call(null,u[o],o,u);else{if(ia(u))return;const c=h?Object.getOwnPropertyNames(u):Object.keys(u),s=c.length;let f;for(o=0;o<s;o++)f=c[o],l.call(null,u[f],f,u)}}function by(u,l){if(ia(u))return null;l=l.toLowerCase();const h=Object.keys(u);let o=h.length,d;for(;o-- >0;)if(d=h[o],l===d.toLowerCase())return d;return null}const hi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Sy=u=>!na(u)&&u!==hi;function Ys(){const{caseless:u}=Sy(this)&&this||{},l={},h=(o,d)=>{const c=u&&by(l,d)||d;Nl(l[c])&&Nl(o)?l[c]=Ys(l[c],o):Nl(o)?l[c]=Ys({},o):tr(o)?l[c]=o.slice():l[c]=o};for(let o=0,d=arguments.length;o<d;o++)arguments[o]&&aa(arguments[o],h);return l}const jg=(u,l,h,{allOwnKeys:o}={})=>(aa(l,(d,c)=>{h&&xe(d)?u[c]=gy(d,h):u[c]=d},{allOwnKeys:o}),u),Hg=u=>(u.charCodeAt(0)===65279&&(u=u.slice(1)),u),Yg=(u,l,h,o)=>{u.prototype=Object.create(l.prototype,o),u.prototype.constructor=u,Object.defineProperty(u,"super",{value:l.prototype}),h&&Object.assign(u.prototype,h)},Vg=(u,l,h,o)=>{let d,c,s;const f={};if(l=l||{},u==null)return l;do{for(d=Object.getOwnPropertyNames(u),c=d.length;c-- >0;)s=d[c],(!o||o(s,u,l))&&!f[s]&&(l[s]=u[s],f[s]=!0);u=h!==!1&&Ps(u)}while(u&&(!h||h(u,l))&&u!==Object.prototype);return l},Gg=(u,l,h)=>{u=String(u),(h===void 0||h>u.length)&&(h=u.length),h-=l.length;const o=u.indexOf(l,h);return o!==-1&&o===h},Fg=u=>{if(!u)return null;if(tr(u))return u;let l=u.length;if(!Ty(l))return null;const h=new Array(l);for(;l-- >0;)h[l]=u[l];return h},Ig=(u=>l=>u&&l instanceof u)(typeof Uint8Array<"u"&&Ps(Uint8Array)),Qg=(u,l)=>{const o=(u&&u[su]).call(u);let d;for(;(d=o.next())&&!d.done;){const c=d.value;l.call(u,c[0],c[1])}},Zg=(u,l)=>{let h;const o=[];for(;(h=u.exec(l))!==null;)o.push(h);return o},Kg=ke("HTMLFormElement"),Pg=u=>u.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(h,o,d){return o.toUpperCase()+d}),pd=(({hasOwnProperty:u})=>(l,h)=>u.call(l,h))(Object.prototype),Jg=ke("RegExp"),Dy=(u,l)=>{const h=Object.getOwnPropertyDescriptors(u),o={};aa(h,(d,c)=>{let s;(s=l(d,c,u))!==!1&&(o[c]=s||d)}),Object.defineProperties(u,o)},kg=u=>{Dy(u,(l,h)=>{if(xe(u)&&["arguments","caller","callee"].indexOf(h)!==-1)return!1;const o=u[h];if(xe(o)){if(l.enumerable=!1,"writable"in l){l.writable=!1;return}l.set||(l.set=()=>{throw Error("Can not rewrite read-only method '"+h+"'")})}})},Wg=(u,l)=>{const h={},o=d=>{d.forEach(c=>{h[c]=!0})};return tr(u)?o(u):o(String(u).split(l)),h},$g=()=>{},t1=(u,l)=>u!=null&&Number.isFinite(u=+u)?u:l;function e1(u){return!!(u&&xe(u.append)&&u[Ey]==="FormData"&&u[su])}const n1=u=>{const l=new Array(10),h=(o,d)=>{if(ra(o)){if(l.indexOf(o)>=0)return;if(ia(o))return o;if(!("toJSON"in o)){l[d]=o;const c=tr(o)?[]:{};return aa(o,(s,f)=>{const g=h(s,d+1);!na(g)&&(c[f]=g)}),l[d]=void 0,c}}return o};return h(u,0)},i1=ke("AsyncFunction"),r1=u=>u&&(ra(u)||xe(u))&&xe(u.then)&&xe(u.catch),Ay=((u,l)=>u?setImmediate:l?((h,o)=>(hi.addEventListener("message",({source:d,data:c})=>{d===hi&&c===h&&o.length&&o.shift()()},!1),d=>{o.push(d),hi.postMessage(h,"*")}))(`axios@${Math.random()}`,[]):h=>setTimeout(h))(typeof setImmediate=="function",xe(hi.postMessage)),a1=typeof queueMicrotask<"u"?queueMicrotask.bind(hi):typeof process<"u"&&process.nextTick||Ay,l1=u=>u!=null&&xe(u[su]),K={isArray:tr,isArrayBuffer:vy,isBuffer:ia,isFormData:Cg,isArrayBufferView:Dg,isString:Ag,isNumber:Ty,isBoolean:Og,isObject:ra,isPlainObject:Nl,isEmptyObject:wg,isReadableStream:Ug,isRequest:Bg,isResponse:Xg,isHeaders:zg,isUndefined:na,isDate:Ng,isFile:xg,isBlob:Mg,isRegExp:Jg,isFunction:xe,isStream:Rg,isURLSearchParams:Lg,isTypedArray:Ig,isFileList:_g,forEach:aa,merge:Ys,extend:jg,trim:qg,stripBOM:Hg,inherits:Yg,toFlatObject:Vg,kindOf:cu,kindOfTest:ke,endsWith:Gg,toArray:Fg,forEachEntry:Qg,matchAll:Zg,isHTMLForm:Kg,hasOwnProperty:pd,hasOwnProp:pd,reduceDescriptors:Dy,freezeMethods:kg,toObjectSet:Wg,toCamelCase:Pg,noop:$g,toFiniteNumber:t1,findKey:by,global:hi,isContextDefined:Sy,isSpecCompliantForm:e1,toJSONObject:n1,isAsyncFn:i1,isThenable:r1,setImmediate:Ay,asap:a1,isIterable:l1};function Dt(u,l,h,o,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=u,this.name="AxiosError",l&&(this.code=l),h&&(this.config=h),o&&(this.request=o),d&&(this.response=d,this.status=d.status?d.status:null)}K.inherits(Dt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:K.toJSONObject(this.config),code:this.code,status:this.status}}});const Oy=Dt.prototype,wy={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(u=>{wy[u]={value:u}});Object.defineProperties(Dt,wy);Object.defineProperty(Oy,"isAxiosError",{value:!0});Dt.from=(u,l,h,o,d,c)=>{const s=Object.create(Oy);return K.toFlatObject(u,s,function(g){return g!==Error.prototype},f=>f!=="isAxiosError"),Dt.call(s,u.message,l,h,o,d),s.cause=u,s.name=u.name,c&&Object.assign(s,c),s};const u1=null;function Vs(u){return K.isPlainObject(u)||K.isArray(u)}function Ny(u){return K.endsWith(u,"[]")?u.slice(0,-2):u}function dd(u,l,h){return u?u.concat(l).map(function(d,c){return d=Ny(d),!h&&c?"["+d+"]":d}).join(h?".":""):l}function o1(u){return K.isArray(u)&&!u.some(Vs)}const s1=K.toFlatObject(K,{},null,function(l){return/^is[A-Z]/.test(l)});function hu(u,l,h){if(!K.isObject(u))throw new TypeError("target must be an object");l=l||new FormData,h=K.toFlatObject(h,{metaTokens:!0,dots:!1,indexes:!1},!1,function(B,z){return!K.isUndefined(z[B])});const o=h.metaTokens,d=h.visitor||D,c=h.dots,s=h.indexes,g=(h.Blob||typeof Blob<"u"&&Blob)&&K.isSpecCompliantForm(l);if(!K.isFunction(d))throw new TypeError("visitor must be a function");function b(x){if(x===null)return"";if(K.isDate(x))return x.toISOString();if(K.isBoolean(x))return x.toString();if(!g&&K.isBlob(x))throw new Dt("Blob is not supported. Use a Buffer instead.");return K.isArrayBuffer(x)||K.isTypedArray(x)?g&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function D(x,B,z){let C=x;if(x&&!z&&typeof x=="object"){if(K.endsWith(B,"{}"))B=o?B:B.slice(0,-2),x=JSON.stringify(x);else if(K.isArray(x)&&o1(x)||(K.isFileList(x)||K.endsWith(B,"[]"))&&(C=K.toArray(x)))return B=Ny(B),C.forEach(function(v,U){!(K.isUndefined(v)||v===null)&&l.append(s===!0?dd([B],U,c):s===null?B:B+"[]",b(v))}),!1}return Vs(x)?!0:(l.append(dd(z,B,c),b(x)),!1)}const A=[],N=Object.assign(s1,{defaultVisitor:D,convertValue:b,isVisitable:Vs});function M(x,B){if(!K.isUndefined(x)){if(A.indexOf(x)!==-1)throw Error("Circular reference detected in "+B.join("."));A.push(x),K.forEach(x,function(C,_){(!(K.isUndefined(C)||C===null)&&d.call(l,C,K.isString(_)?_.trim():_,B,N))===!0&&M(C,B?B.concat(_):[_])}),A.pop()}}if(!K.isObject(u))throw new TypeError("data must be an object");return M(u),l}function yd(u){const l={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(u).replace(/[!'()~]|%20|%00/g,function(o){return l[o]})}function Js(u,l){this._pairs=[],u&&hu(u,this,l)}const xy=Js.prototype;xy.append=function(l,h){this._pairs.push([l,h])};xy.toString=function(l){const h=l?function(o){return l.call(this,o,yd)}:yd;return this._pairs.map(function(d){return h(d[0])+"="+h(d[1])},"").join("&")};function c1(u){return encodeURIComponent(u).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function My(u,l,h){if(!l)return u;const o=h&&h.encode||c1;K.isFunction(h)&&(h={serialize:h});const d=h&&h.serialize;let c;if(d?c=d(l,h):c=K.isURLSearchParams(l)?l.toString():new Js(l,h).toString(o),c){const s=u.indexOf("#");s!==-1&&(u=u.slice(0,s)),u+=(u.indexOf("?")===-1?"?":"&")+c}return u}class md{constructor(){this.handlers=[]}use(l,h,o){return this.handlers.push({fulfilled:l,rejected:h,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(l){this.handlers[l]&&(this.handlers[l]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(l){K.forEach(this.handlers,function(o){o!==null&&l(o)})}}const _y={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},f1=typeof URLSearchParams<"u"?URLSearchParams:Js,h1=typeof FormData<"u"?FormData:null,p1=typeof Blob<"u"?Blob:null,d1={isBrowser:!0,classes:{URLSearchParams:f1,FormData:h1,Blob:p1},protocols:["http","https","file","blob","url","data"]},ks=typeof window<"u"&&typeof document<"u",Gs=typeof navigator=="object"&&navigator||void 0,y1=ks&&(!Gs||["ReactNative","NativeScript","NS"].indexOf(Gs.product)<0),m1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",g1=ks&&window.location.href||"http://localhost",E1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ks,hasStandardBrowserEnv:y1,hasStandardBrowserWebWorkerEnv:m1,navigator:Gs,origin:g1},Symbol.toStringTag,{value:"Module"})),be={...E1,...d1};function v1(u,l){return hu(u,new be.classes.URLSearchParams,{visitor:function(h,o,d,c){return be.isNode&&K.isBuffer(h)?(this.append(o,h.toString("base64")),!1):c.defaultVisitor.apply(this,arguments)},...l})}function T1(u){return K.matchAll(/\w+|\[(\w*)]/g,u).map(l=>l[0]==="[]"?"":l[1]||l[0])}function b1(u){const l={},h=Object.keys(u);let o;const d=h.length;let c;for(o=0;o<d;o++)c=h[o],l[c]=u[c];return l}function Ry(u){function l(h,o,d,c){let s=h[c++];if(s==="__proto__")return!0;const f=Number.isFinite(+s),g=c>=h.length;return s=!s&&K.isArray(d)?d.length:s,g?(K.hasOwnProp(d,s)?d[s]=[d[s],o]:d[s]=o,!f):((!d[s]||!K.isObject(d[s]))&&(d[s]=[]),l(h,o,d[s],c)&&K.isArray(d[s])&&(d[s]=b1(d[s])),!f)}if(K.isFormData(u)&&K.isFunction(u.entries)){const h={};return K.forEachEntry(u,(o,d)=>{l(T1(o),d,h,0)}),h}return null}function S1(u,l,h){if(K.isString(u))try{return(l||JSON.parse)(u),K.trim(u)}catch(o){if(o.name!=="SyntaxError")throw o}return(h||JSON.stringify)(u)}const la={transitional:_y,adapter:["xhr","http","fetch"],transformRequest:[function(l,h){const o=h.getContentType()||"",d=o.indexOf("application/json")>-1,c=K.isObject(l);if(c&&K.isHTMLForm(l)&&(l=new FormData(l)),K.isFormData(l))return d?JSON.stringify(Ry(l)):l;if(K.isArrayBuffer(l)||K.isBuffer(l)||K.isStream(l)||K.isFile(l)||K.isBlob(l)||K.isReadableStream(l))return l;if(K.isArrayBufferView(l))return l.buffer;if(K.isURLSearchParams(l))return h.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),l.toString();let f;if(c){if(o.indexOf("application/x-www-form-urlencoded")>-1)return v1(l,this.formSerializer).toString();if((f=K.isFileList(l))||o.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return hu(f?{"files[]":l}:l,g&&new g,this.formSerializer)}}return c||d?(h.setContentType("application/json",!1),S1(l)):l}],transformResponse:[function(l){const h=this.transitional||la.transitional,o=h&&h.forcedJSONParsing,d=this.responseType==="json";if(K.isResponse(l)||K.isReadableStream(l))return l;if(l&&K.isString(l)&&(o&&!this.responseType||d)){const s=!(h&&h.silentJSONParsing)&&d;try{return JSON.parse(l)}catch(f){if(s)throw f.name==="SyntaxError"?Dt.from(f,Dt.ERR_BAD_RESPONSE,this,null,this.response):f}}return l}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:be.classes.FormData,Blob:be.classes.Blob},validateStatus:function(l){return l>=200&&l<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};K.forEach(["delete","get","head","post","put","patch"],u=>{la.headers[u]={}});const D1=K.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),A1=u=>{const l={};let h,o,d;return u&&u.split(`
`).forEach(function(s){d=s.indexOf(":"),h=s.substring(0,d).trim().toLowerCase(),o=s.substring(d+1).trim(),!(!h||l[h]&&D1[h])&&(h==="set-cookie"?l[h]?l[h].push(o):l[h]=[o]:l[h]=l[h]?l[h]+", "+o:o)}),l},gd=Symbol("internals");function ta(u){return u&&String(u).trim().toLowerCase()}function xl(u){return u===!1||u==null?u:K.isArray(u)?u.map(xl):String(u)}function O1(u){const l=Object.create(null),h=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=h.exec(u);)l[o[1]]=o[2];return l}const w1=u=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(u.trim());function Bs(u,l,h,o,d){if(K.isFunction(o))return o.call(this,l,h);if(d&&(l=h),!!K.isString(l)){if(K.isString(o))return l.indexOf(o)!==-1;if(K.isRegExp(o))return o.test(l)}}function N1(u){return u.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(l,h,o)=>h.toUpperCase()+o)}function x1(u,l){const h=K.toCamelCase(" "+l);["get","set","has"].forEach(o=>{Object.defineProperty(u,o+h,{value:function(d,c,s){return this[o].call(this,l,d,c,s)},configurable:!0})})}let Me=class{constructor(l){l&&this.set(l)}set(l,h,o){const d=this;function c(f,g,b){const D=ta(g);if(!D)throw new Error("header name must be a non-empty string");const A=K.findKey(d,D);(!A||d[A]===void 0||b===!0||b===void 0&&d[A]!==!1)&&(d[A||g]=xl(f))}const s=(f,g)=>K.forEach(f,(b,D)=>c(b,D,g));if(K.isPlainObject(l)||l instanceof this.constructor)s(l,h);else if(K.isString(l)&&(l=l.trim())&&!w1(l))s(A1(l),h);else if(K.isObject(l)&&K.isIterable(l)){let f={},g,b;for(const D of l){if(!K.isArray(D))throw TypeError("Object iterator must return a key-value pair");f[b=D[0]]=(g=f[b])?K.isArray(g)?[...g,D[1]]:[g,D[1]]:D[1]}s(f,h)}else l!=null&&c(h,l,o);return this}get(l,h){if(l=ta(l),l){const o=K.findKey(this,l);if(o){const d=this[o];if(!h)return d;if(h===!0)return O1(d);if(K.isFunction(h))return h.call(this,d,o);if(K.isRegExp(h))return h.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(l,h){if(l=ta(l),l){const o=K.findKey(this,l);return!!(o&&this[o]!==void 0&&(!h||Bs(this,this[o],o,h)))}return!1}delete(l,h){const o=this;let d=!1;function c(s){if(s=ta(s),s){const f=K.findKey(o,s);f&&(!h||Bs(o,o[f],f,h))&&(delete o[f],d=!0)}}return K.isArray(l)?l.forEach(c):c(l),d}clear(l){const h=Object.keys(this);let o=h.length,d=!1;for(;o--;){const c=h[o];(!l||Bs(this,this[c],c,l,!0))&&(delete this[c],d=!0)}return d}normalize(l){const h=this,o={};return K.forEach(this,(d,c)=>{const s=K.findKey(o,c);if(s){h[s]=xl(d),delete h[c];return}const f=l?N1(c):String(c).trim();f!==c&&delete h[c],h[f]=xl(d),o[f]=!0}),this}concat(...l){return this.constructor.concat(this,...l)}toJSON(l){const h=Object.create(null);return K.forEach(this,(o,d)=>{o!=null&&o!==!1&&(h[d]=l&&K.isArray(o)?o.join(", "):o)}),h}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([l,h])=>l+": "+h).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(l){return l instanceof this?l:new this(l)}static concat(l,...h){const o=new this(l);return h.forEach(d=>o.set(d)),o}static accessor(l){const o=(this[gd]=this[gd]={accessors:{}}).accessors,d=this.prototype;function c(s){const f=ta(s);o[f]||(x1(d,s),o[f]=!0)}return K.isArray(l)?l.forEach(c):c(l),this}};Me.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);K.reduceDescriptors(Me.prototype,({value:u},l)=>{let h=l[0].toUpperCase()+l.slice(1);return{get:()=>u,set(o){this[h]=o}}});K.freezeMethods(Me);function Xs(u,l){const h=this||la,o=l||h,d=Me.from(o.headers);let c=o.data;return K.forEach(u,function(f){c=f.call(h,c,d.normalize(),l?l.status:void 0)}),d.normalize(),c}function Cy(u){return!!(u&&u.__CANCEL__)}function er(u,l,h){Dt.call(this,u??"canceled",Dt.ERR_CANCELED,l,h),this.name="CanceledError"}K.inherits(er,Dt,{__CANCEL__:!0});function Ly(u,l,h){const o=h.config.validateStatus;!h.status||!o||o(h.status)?u(h):l(new Dt("Request failed with status code "+h.status,[Dt.ERR_BAD_REQUEST,Dt.ERR_BAD_RESPONSE][Math.floor(h.status/100)-4],h.config,h.request,h))}function M1(u){const l=/^([-+\w]{1,25})(:?\/\/|:)/.exec(u);return l&&l[1]||""}function _1(u,l){u=u||10;const h=new Array(u),o=new Array(u);let d=0,c=0,s;return l=l!==void 0?l:1e3,function(g){const b=Date.now(),D=o[c];s||(s=b),h[d]=g,o[d]=b;let A=c,N=0;for(;A!==d;)N+=h[A++],A=A%u;if(d=(d+1)%u,d===c&&(c=(c+1)%u),b-s<l)return;const M=D&&b-D;return M?Math.round(N*1e3/M):void 0}}function R1(u,l){let h=0,o=1e3/l,d,c;const s=(b,D=Date.now())=>{h=D,d=null,c&&(clearTimeout(c),c=null),u(...b)};return[(...b)=>{const D=Date.now(),A=D-h;A>=o?s(b,D):(d=b,c||(c=setTimeout(()=>{c=null,s(d)},o-A)))},()=>d&&s(d)]}const uu=(u,l,h=3)=>{let o=0;const d=_1(50,250);return R1(c=>{const s=c.loaded,f=c.lengthComputable?c.total:void 0,g=s-o,b=d(g),D=s<=f;o=s;const A={loaded:s,total:f,progress:f?s/f:void 0,bytes:g,rate:b||void 0,estimated:b&&f&&D?(f-s)/b:void 0,event:c,lengthComputable:f!=null,[l?"download":"upload"]:!0};u(A)},h)},Ed=(u,l)=>{const h=u!=null;return[o=>l[0]({lengthComputable:h,total:u,loaded:o}),l[1]]},vd=u=>(...l)=>K.asap(()=>u(...l)),C1=be.hasStandardBrowserEnv?((u,l)=>h=>(h=new URL(h,be.origin),u.protocol===h.protocol&&u.host===h.host&&(l||u.port===h.port)))(new URL(be.origin),be.navigator&&/(msie|trident)/i.test(be.navigator.userAgent)):()=>!0,L1=be.hasStandardBrowserEnv?{write(u,l,h,o,d,c){const s=[u+"="+encodeURIComponent(l)];K.isNumber(h)&&s.push("expires="+new Date(h).toGMTString()),K.isString(o)&&s.push("path="+o),K.isString(d)&&s.push("domain="+d),c===!0&&s.push("secure"),document.cookie=s.join("; ")},read(u){const l=document.cookie.match(new RegExp("(^|;\\s*)("+u+")=([^;]*)"));return l?decodeURIComponent(l[3]):null},remove(u){this.write(u,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function U1(u){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(u)}function B1(u,l){return l?u.replace(/\/?\/$/,"")+"/"+l.replace(/^\/+/,""):u}function Uy(u,l,h){let o=!U1(l);return u&&(o||h==!1)?B1(u,l):l}const Td=u=>u instanceof Me?{...u}:u;function di(u,l){l=l||{};const h={};function o(b,D,A,N){return K.isPlainObject(b)&&K.isPlainObject(D)?K.merge.call({caseless:N},b,D):K.isPlainObject(D)?K.merge({},D):K.isArray(D)?D.slice():D}function d(b,D,A,N){if(K.isUndefined(D)){if(!K.isUndefined(b))return o(void 0,b,A,N)}else return o(b,D,A,N)}function c(b,D){if(!K.isUndefined(D))return o(void 0,D)}function s(b,D){if(K.isUndefined(D)){if(!K.isUndefined(b))return o(void 0,b)}else return o(void 0,D)}function f(b,D,A){if(A in l)return o(b,D);if(A in u)return o(void 0,b)}const g={url:c,method:c,data:c,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:f,headers:(b,D,A)=>d(Td(b),Td(D),A,!0)};return K.forEach(Object.keys({...u,...l}),function(D){const A=g[D]||d,N=A(u[D],l[D],D);K.isUndefined(N)&&A!==f||(h[D]=N)}),h}const By=u=>{const l=di({},u);let{data:h,withXSRFToken:o,xsrfHeaderName:d,xsrfCookieName:c,headers:s,auth:f}=l;l.headers=s=Me.from(s),l.url=My(Uy(l.baseURL,l.url,l.allowAbsoluteUrls),u.params,u.paramsSerializer),f&&s.set("Authorization","Basic "+btoa((f.username||"")+":"+(f.password?unescape(encodeURIComponent(f.password)):"")));let g;if(K.isFormData(h)){if(be.hasStandardBrowserEnv||be.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((g=s.getContentType())!==!1){const[b,...D]=g?g.split(";").map(A=>A.trim()).filter(Boolean):[];s.setContentType([b||"multipart/form-data",...D].join("; "))}}if(be.hasStandardBrowserEnv&&(o&&K.isFunction(o)&&(o=o(l)),o||o!==!1&&C1(l.url))){const b=d&&c&&L1.read(c);b&&s.set(d,b)}return l},X1=typeof XMLHttpRequest<"u",z1=X1&&function(u){return new Promise(function(h,o){const d=By(u);let c=d.data;const s=Me.from(d.headers).normalize();let{responseType:f,onUploadProgress:g,onDownloadProgress:b}=d,D,A,N,M,x;function B(){M&&M(),x&&x(),d.cancelToken&&d.cancelToken.unsubscribe(D),d.signal&&d.signal.removeEventListener("abort",D)}let z=new XMLHttpRequest;z.open(d.method.toUpperCase(),d.url,!0),z.timeout=d.timeout;function C(){if(!z)return;const v=Me.from("getAllResponseHeaders"in z&&z.getAllResponseHeaders()),X={data:!f||f==="text"||f==="json"?z.responseText:z.response,status:z.status,statusText:z.statusText,headers:v,config:u,request:z};Ly(function(lt){h(lt),B()},function(lt){o(lt),B()},X),z=null}"onloadend"in z?z.onloadend=C:z.onreadystatechange=function(){!z||z.readyState!==4||z.status===0&&!(z.responseURL&&z.responseURL.indexOf("file:")===0)||setTimeout(C)},z.onabort=function(){z&&(o(new Dt("Request aborted",Dt.ECONNABORTED,u,z)),z=null)},z.onerror=function(){o(new Dt("Network Error",Dt.ERR_NETWORK,u,z)),z=null},z.ontimeout=function(){let U=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const X=d.transitional||_y;d.timeoutErrorMessage&&(U=d.timeoutErrorMessage),o(new Dt(U,X.clarifyTimeoutError?Dt.ETIMEDOUT:Dt.ECONNABORTED,u,z)),z=null},c===void 0&&s.setContentType(null),"setRequestHeader"in z&&K.forEach(s.toJSON(),function(U,X){z.setRequestHeader(X,U)}),K.isUndefined(d.withCredentials)||(z.withCredentials=!!d.withCredentials),f&&f!=="json"&&(z.responseType=d.responseType),b&&([N,x]=uu(b,!0),z.addEventListener("progress",N)),g&&z.upload&&([A,M]=uu(g),z.upload.addEventListener("progress",A),z.upload.addEventListener("loadend",M)),(d.cancelToken||d.signal)&&(D=v=>{z&&(o(!v||v.type?new er(null,u,z):v),z.abort(),z=null)},d.cancelToken&&d.cancelToken.subscribe(D),d.signal&&(d.signal.aborted?D():d.signal.addEventListener("abort",D)));const _=M1(d.url);if(_&&be.protocols.indexOf(_)===-1){o(new Dt("Unsupported protocol "+_+":",Dt.ERR_BAD_REQUEST,u));return}z.send(c||null)})},q1=(u,l)=>{const{length:h}=u=u?u.filter(Boolean):[];if(l||h){let o=new AbortController,d;const c=function(b){if(!d){d=!0,f();const D=b instanceof Error?b:this.reason;o.abort(D instanceof Dt?D:new er(D instanceof Error?D.message:D))}};let s=l&&setTimeout(()=>{s=null,c(new Dt(`timeout ${l} of ms exceeded`,Dt.ETIMEDOUT))},l);const f=()=>{u&&(s&&clearTimeout(s),s=null,u.forEach(b=>{b.unsubscribe?b.unsubscribe(c):b.removeEventListener("abort",c)}),u=null)};u.forEach(b=>b.addEventListener("abort",c));const{signal:g}=o;return g.unsubscribe=()=>K.asap(f),g}},j1=function*(u,l){let h=u.byteLength;if(h<l){yield u;return}let o=0,d;for(;o<h;)d=o+l,yield u.slice(o,d),o=d},H1=async function*(u,l){for await(const h of Y1(u))yield*j1(h,l)},Y1=async function*(u){if(u[Symbol.asyncIterator]){yield*u;return}const l=u.getReader();try{for(;;){const{done:h,value:o}=await l.read();if(h)break;yield o}}finally{await l.cancel()}},bd=(u,l,h,o)=>{const d=H1(u,l);let c=0,s,f=g=>{s||(s=!0,o&&o(g))};return new ReadableStream({async pull(g){try{const{done:b,value:D}=await d.next();if(b){f(),g.close();return}let A=D.byteLength;if(h){let N=c+=A;h(N)}g.enqueue(new Uint8Array(D))}catch(b){throw f(b),b}},cancel(g){return f(g),d.return()}},{highWaterMark:2})},pu=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Xy=pu&&typeof ReadableStream=="function",V1=pu&&(typeof TextEncoder=="function"?(u=>l=>u.encode(l))(new TextEncoder):async u=>new Uint8Array(await new Response(u).arrayBuffer())),zy=(u,...l)=>{try{return!!u(...l)}catch{return!1}},G1=Xy&&zy(()=>{let u=!1;const l=new Request(be.origin,{body:new ReadableStream,method:"POST",get duplex(){return u=!0,"half"}}).headers.has("Content-Type");return u&&!l}),Sd=64*1024,Fs=Xy&&zy(()=>K.isReadableStream(new Response("").body)),ou={stream:Fs&&(u=>u.body)};pu&&(u=>{["text","arrayBuffer","blob","formData","stream"].forEach(l=>{!ou[l]&&(ou[l]=K.isFunction(u[l])?h=>h[l]():(h,o)=>{throw new Dt(`Response type '${l}' is not supported`,Dt.ERR_NOT_SUPPORT,o)})})})(new Response);const F1=async u=>{if(u==null)return 0;if(K.isBlob(u))return u.size;if(K.isSpecCompliantForm(u))return(await new Request(be.origin,{method:"POST",body:u}).arrayBuffer()).byteLength;if(K.isArrayBufferView(u)||K.isArrayBuffer(u))return u.byteLength;if(K.isURLSearchParams(u)&&(u=u+""),K.isString(u))return(await V1(u)).byteLength},I1=async(u,l)=>{const h=K.toFiniteNumber(u.getContentLength());return h??F1(l)},Q1=pu&&(async u=>{let{url:l,method:h,data:o,signal:d,cancelToken:c,timeout:s,onDownloadProgress:f,onUploadProgress:g,responseType:b,headers:D,withCredentials:A="same-origin",fetchOptions:N}=By(u);b=b?(b+"").toLowerCase():"text";let M=q1([d,c&&c.toAbortSignal()],s),x;const B=M&&M.unsubscribe&&(()=>{M.unsubscribe()});let z;try{if(g&&G1&&h!=="get"&&h!=="head"&&(z=await I1(D,o))!==0){let X=new Request(l,{method:"POST",body:o,duplex:"half"}),W;if(K.isFormData(o)&&(W=X.headers.get("content-type"))&&D.setContentType(W),X.body){const[lt,$]=Ed(z,uu(vd(g)));o=bd(X.body,Sd,lt,$)}}K.isString(A)||(A=A?"include":"omit");const C="credentials"in Request.prototype;x=new Request(l,{...N,signal:M,method:h.toUpperCase(),headers:D.normalize().toJSON(),body:o,duplex:"half",credentials:C?A:void 0});let _=await fetch(x,N);const v=Fs&&(b==="stream"||b==="response");if(Fs&&(f||v&&B)){const X={};["status","statusText","headers"].forEach(E=>{X[E]=_[E]});const W=K.toFiniteNumber(_.headers.get("content-length")),[lt,$]=f&&Ed(W,uu(vd(f),!0))||[];_=new Response(bd(_.body,Sd,lt,()=>{$&&$(),B&&B()}),X)}b=b||"text";let U=await ou[K.findKey(ou,b)||"text"](_,u);return!v&&B&&B(),await new Promise((X,W)=>{Ly(X,W,{data:U,headers:Me.from(_.headers),status:_.status,statusText:_.statusText,config:u,request:x})})}catch(C){throw B&&B(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new Dt("Network Error",Dt.ERR_NETWORK,u,x),{cause:C.cause||C}):Dt.from(C,C&&C.code,u,x)}}),Is={http:u1,xhr:z1,fetch:Q1};K.forEach(Is,(u,l)=>{if(u){try{Object.defineProperty(u,"name",{value:l})}catch{}Object.defineProperty(u,"adapterName",{value:l})}});const Dd=u=>`- ${u}`,Z1=u=>K.isFunction(u)||u===null||u===!1,qy={getAdapter:u=>{u=K.isArray(u)?u:[u];const{length:l}=u;let h,o;const d={};for(let c=0;c<l;c++){h=u[c];let s;if(o=h,!Z1(h)&&(o=Is[(s=String(h)).toLowerCase()],o===void 0))throw new Dt(`Unknown adapter '${s}'`);if(o)break;d[s||"#"+c]=o}if(!o){const c=Object.entries(d).map(([f,g])=>`adapter ${f} `+(g===!1?"is not supported by the environment":"is not available in the build"));let s=l?c.length>1?`since :
`+c.map(Dd).join(`
`):" "+Dd(c[0]):"as no adapter specified";throw new Dt("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return o},adapters:Is};function zs(u){if(u.cancelToken&&u.cancelToken.throwIfRequested(),u.signal&&u.signal.aborted)throw new er(null,u)}function Ad(u){return zs(u),u.headers=Me.from(u.headers),u.data=Xs.call(u,u.transformRequest),["post","put","patch"].indexOf(u.method)!==-1&&u.headers.setContentType("application/x-www-form-urlencoded",!1),qy.getAdapter(u.adapter||la.adapter)(u).then(function(o){return zs(u),o.data=Xs.call(u,u.transformResponse,o),o.headers=Me.from(o.headers),o},function(o){return Cy(o)||(zs(u),o&&o.response&&(o.response.data=Xs.call(u,u.transformResponse,o.response),o.response.headers=Me.from(o.response.headers))),Promise.reject(o)})}const jy="1.11.0",du={};["object","boolean","number","function","string","symbol"].forEach((u,l)=>{du[u]=function(o){return typeof o===u||"a"+(l<1?"n ":" ")+u}});const Od={};du.transitional=function(l,h,o){function d(c,s){return"[Axios v"+jy+"] Transitional option '"+c+"'"+s+(o?". "+o:"")}return(c,s,f)=>{if(l===!1)throw new Dt(d(s," has been removed"+(h?" in "+h:"")),Dt.ERR_DEPRECATED);return h&&!Od[s]&&(Od[s]=!0,console.warn(d(s," has been deprecated since v"+h+" and will be removed in the near future"))),l?l(c,s,f):!0}};du.spelling=function(l){return(h,o)=>(console.warn(`${o} is likely a misspelling of ${l}`),!0)};function K1(u,l,h){if(typeof u!="object")throw new Dt("options must be an object",Dt.ERR_BAD_OPTION_VALUE);const o=Object.keys(u);let d=o.length;for(;d-- >0;){const c=o[d],s=l[c];if(s){const f=u[c],g=f===void 0||s(f,c,u);if(g!==!0)throw new Dt("option "+c+" must be "+g,Dt.ERR_BAD_OPTION_VALUE);continue}if(h!==!0)throw new Dt("Unknown option "+c,Dt.ERR_BAD_OPTION)}}const Ml={assertOptions:K1,validators:du},rn=Ml.validators;let pi=class{constructor(l){this.defaults=l||{},this.interceptors={request:new md,response:new md}}async request(l,h){try{return await this._request(l,h)}catch(o){if(o instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const c=d.stack?d.stack.replace(/^.+\n/,""):"";try{o.stack?c&&!String(o.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+c):o.stack=c}catch{}}throw o}}_request(l,h){typeof l=="string"?(h=h||{},h.url=l):h=l||{},h=di(this.defaults,h);const{transitional:o,paramsSerializer:d,headers:c}=h;o!==void 0&&Ml.assertOptions(o,{silentJSONParsing:rn.transitional(rn.boolean),forcedJSONParsing:rn.transitional(rn.boolean),clarifyTimeoutError:rn.transitional(rn.boolean)},!1),d!=null&&(K.isFunction(d)?h.paramsSerializer={serialize:d}:Ml.assertOptions(d,{encode:rn.function,serialize:rn.function},!0)),h.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?h.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:h.allowAbsoluteUrls=!0),Ml.assertOptions(h,{baseUrl:rn.spelling("baseURL"),withXsrfToken:rn.spelling("withXSRFToken")},!0),h.method=(h.method||this.defaults.method||"get").toLowerCase();let s=c&&K.merge(c.common,c[h.method]);c&&K.forEach(["delete","get","head","post","put","patch","common"],x=>{delete c[x]}),h.headers=Me.concat(s,c);const f=[];let g=!0;this.interceptors.request.forEach(function(B){typeof B.runWhen=="function"&&B.runWhen(h)===!1||(g=g&&B.synchronous,f.unshift(B.fulfilled,B.rejected))});const b=[];this.interceptors.response.forEach(function(B){b.push(B.fulfilled,B.rejected)});let D,A=0,N;if(!g){const x=[Ad.bind(this),void 0];for(x.unshift(...f),x.push(...b),N=x.length,D=Promise.resolve(h);A<N;)D=D.then(x[A++],x[A++]);return D}N=f.length;let M=h;for(A=0;A<N;){const x=f[A++],B=f[A++];try{M=x(M)}catch(z){B.call(this,z);break}}try{D=Ad.call(this,M)}catch(x){return Promise.reject(x)}for(A=0,N=b.length;A<N;)D=D.then(b[A++],b[A++]);return D}getUri(l){l=di(this.defaults,l);const h=Uy(l.baseURL,l.url,l.allowAbsoluteUrls);return My(h,l.params,l.paramsSerializer)}};K.forEach(["delete","get","head","options"],function(l){pi.prototype[l]=function(h,o){return this.request(di(o||{},{method:l,url:h,data:(o||{}).data}))}});K.forEach(["post","put","patch"],function(l){function h(o){return function(c,s,f){return this.request(di(f||{},{method:l,headers:o?{"Content-Type":"multipart/form-data"}:{},url:c,data:s}))}}pi.prototype[l]=h(),pi.prototype[l+"Form"]=h(!0)});let P1=class Hy{constructor(l){if(typeof l!="function")throw new TypeError("executor must be a function.");let h;this.promise=new Promise(function(c){h=c});const o=this;this.promise.then(d=>{if(!o._listeners)return;let c=o._listeners.length;for(;c-- >0;)o._listeners[c](d);o._listeners=null}),this.promise.then=d=>{let c;const s=new Promise(f=>{o.subscribe(f),c=f}).then(d);return s.cancel=function(){o.unsubscribe(c)},s},l(function(c,s,f){o.reason||(o.reason=new er(c,s,f),h(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(l){if(this.reason){l(this.reason);return}this._listeners?this._listeners.push(l):this._listeners=[l]}unsubscribe(l){if(!this._listeners)return;const h=this._listeners.indexOf(l);h!==-1&&this._listeners.splice(h,1)}toAbortSignal(){const l=new AbortController,h=o=>{l.abort(o)};return this.subscribe(h),l.signal.unsubscribe=()=>this.unsubscribe(h),l.signal}static source(){let l;return{token:new Hy(function(d){l=d}),cancel:l}}};function J1(u){return function(h){return u.apply(null,h)}}function k1(u){return K.isObject(u)&&u.isAxiosError===!0}const Qs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qs).forEach(([u,l])=>{Qs[l]=u});function Yy(u){const l=new pi(u),h=gy(pi.prototype.request,l);return K.extend(h,pi.prototype,l,{allOwnKeys:!0}),K.extend(h,l,null,{allOwnKeys:!0}),h.create=function(d){return Yy(di(u,d))},h}const te=Yy(la);te.Axios=pi;te.CanceledError=er;te.CancelToken=P1;te.isCancel=Cy;te.VERSION=jy;te.toFormData=hu;te.AxiosError=Dt;te.Cancel=te.CanceledError;te.all=function(l){return Promise.all(l)};te.spread=J1;te.isAxiosError=k1;te.mergeConfig=di;te.AxiosHeaders=Me;te.formToJSON=u=>Ry(K.isHTMLForm(u)?new FormData(u):u);te.getAdapter=qy.getAdapter;te.HttpStatusCode=Qs;te.default=te;const{Axios:nv,AxiosError:iv,CanceledError:rv,isCancel:av,CancelToken:lv,VERSION:uv,all:ov,Cancel:sv,isAxiosError:cv,spread:fv,toFormData:hv,AxiosHeaders:pv,HttpStatusCode:dv,formToJSON:yv,getAdapter:mv,mergeConfig:gv}=te,W1=({onConnectionChange:u})=>{const[l,h]=Te.useState("connecting"),[o,d]=Te.useState(new Date),[c,s]=Te.useState(null),[f,g]=Te.useState(null),b="localhost",D=9e3,A=`http://${b}:${D}`,N=async()=>{try{h("connecting"),s(null);const z=await te.post(A,`
        <ENVELOPE>
          <HEADER>
            <TALLYREQUEST>Export Data</TALLYREQUEST>
          </HEADER>
          <BODY>
            <EXPORTDATA>
              <REQUESTDESC>
                <REPORTNAME>List of Companies</REPORTNAME>
              </REQUESTDESC>
            </EXPORTDATA>
          </BODY>
        </ENVELOPE>
      `,{headers:{"Content-Type":"application/xml"},timeout:5e3});if(z.status===200)h("online"),g({host:b,port:D,responseTime:Date.now()-startTime}),u&&u(!0);else throw new Error(`HTTP ${z.status}`)}catch(B){h("offline"),s(B.message),g(null),u&&u(!1)}finally{d(new Date)}};Te.useEffect(()=>{const B=()=>{N()};B();const z=setInterval(B,1e4);return()=>{clearInterval(z)}},[]);const M=()=>{switch(l){case"online":return"✅";case"offline":return"❌";case"connecting":return"🔄";default:return"❓"}},x=()=>{switch(l){case"online":return"Connected to Tally Prime";case"offline":return"Tally Prime not accessible";case"connecting":return"Checking connection...";default:return"Unknown status"}};return ft.jsxs("div",{className:"status-container",children:[ft.jsx("div",{className:"status-header",children:ft.jsx("h3",{children:"Tally Prime Connection"})}),ft.jsxs("div",{className:`status-indicator ${l}`,children:[ft.jsx("div",{className:"status-icon",children:M()}),ft.jsxs("div",{className:"status-details",children:[ft.jsx("div",{className:"status-text",children:x()}),ft.jsxs("div",{className:"status-timestamp",children:["Last checked: ",o.toLocaleTimeString()]}),f&&ft.jsxs("div",{className:"status-timestamp",children:[b,":",D," (",f.responseTime,"ms)"]}),c&&ft.jsxs("div",{className:"status-error",children:["Error: ",c]})]})]}),ft.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:["Make sure Tally Prime is running with Gateway enabled on port ",D]})]})};var an={},bl={},wd;function Ws(){return wd||(wd=1,(function(){bl.defaults={"0.1":{explicitCharkey:!1,trim:!0,normalize:!0,normalizeTags:!1,attrkey:"@",charkey:"#",explicitArray:!1,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!1,validator:null,xmlns:!1,explicitChildren:!1,childkey:"@@",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,emptyTag:""},"0.2":{explicitCharkey:!1,trim:!1,normalize:!1,normalizeTags:!1,attrkey:"$",charkey:"_",explicitArray:!0,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!0,validator:null,xmlns:!1,explicitChildren:!1,preserveChildrenOrder:!1,childkey:"$$",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,rootName:"root",xmldec:{version:"1.0",encoding:"UTF-8",standalone:!0},doctype:null,renderOpts:{pretty:!0,indent:"  ",newline:`
`},headless:!1,chunkSize:1e4,emptyTag:"",cdata:!1}}}).call(bl)),bl}var Sl={},ln={},un={},Nd;function Dn(){return Nd||(Nd=1,(function(){var u,l,h,o,d,c,s,f=[].slice,g={}.hasOwnProperty;u=function(){var b,D,A,N,M,x;if(x=arguments[0],M=2<=arguments.length?f.call(arguments,1):[],d(Object.assign))Object.assign.apply(null,arguments);else for(b=0,A=M.length;b<A;b++)if(N=M[b],N!=null)for(D in N)g.call(N,D)&&(x[D]=N[D]);return x},d=function(b){return!!b&&Object.prototype.toString.call(b)==="[object Function]"},c=function(b){var D;return!!b&&((D=typeof b)=="function"||D==="object")},h=function(b){return d(Array.isArray)?Array.isArray(b):Object.prototype.toString.call(b)==="[object Array]"},o=function(b){var D;if(h(b))return!b.length;for(D in b)if(g.call(b,D))return!1;return!0},s=function(b){var D,A;return c(b)&&(A=Object.getPrototypeOf(b))&&(D=A.constructor)&&typeof D=="function"&&D instanceof D&&Function.prototype.toString.call(D)===Function.prototype.toString.call(Object)},l=function(b){return d(b.valueOf)?b.valueOf():b},un.assign=u,un.isFunction=d,un.isObject=c,un.isArray=h,un.isEmpty=o,un.isPlainObject=s,un.getValue=l}).call(un)),un}var _l={exports:{}},$1=_l.exports,xd;function Vy(){return xd||(xd=1,(function(){_l.exports=(function(){function u(){}return u.prototype.hasFeature=function(l,h){return!0},u.prototype.createDocumentType=function(l,h,o){throw new Error("This DOM method is not implemented.")},u.prototype.createDocument=function(l,h,o){throw new Error("This DOM method is not implemented.")},u.prototype.createHTMLDocument=function(l){throw new Error("This DOM method is not implemented.")},u.prototype.getFeature=function(l,h){throw new Error("This DOM method is not implemented.")},u})()}).call($1)),_l.exports}var Rl={exports:{}},Cl={exports:{}},Ll={exports:{}},tE=Ll.exports,Md;function eE(){return Md||(Md=1,(function(){Ll.exports=(function(){function u(){}return u.prototype.handleError=function(l){throw new Error(l)},u})()}).call(tE)),Ll.exports}var Ul={exports:{}},nE=Ul.exports,_d;function iE(){return _d||(_d=1,(function(){Ul.exports=(function(){function u(l){this.arr=l||[]}return Object.defineProperty(u.prototype,"length",{get:function(){return this.arr.length}}),u.prototype.item=function(l){return this.arr[l]||null},u.prototype.contains=function(l){return this.arr.indexOf(l)!==-1},u})()}).call(nE)),Ul.exports}var rE=Cl.exports,Rd;function aE(){return Rd||(Rd=1,(function(){var u,l;u=eE(),l=iE(),Cl.exports=(function(){function h(){this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new u,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Object.create(this.defaultParams)}return Object.defineProperty(h.prototype,"parameterNames",{get:function(){return new l(Object.keys(this.defaultParams))}}),h.prototype.getParameter=function(o){return this.params.hasOwnProperty(o)?this.params[o]:null},h.prototype.canSetParameter=function(o,d){return!0},h.prototype.setParameter=function(o,d){return d!=null?this.params[o]=d:delete this.params[o]},h})()}).call(rE)),Cl.exports}var Bl={exports:{}},Xl={exports:{}},zl={exports:{}},lE=zl.exports,Cd;function ae(){return Cd||(Cd=1,(function(){zl.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(lE)),zl.exports}var ql={exports:{}},uE=ql.exports,Ld;function Gy(){return Ld||(Ld=1,(function(){var u;u=ae(),Ze(),ql.exports=(function(){function l(h,o,d){if(this.parent=h,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),o==null)throw new Error("Missing attribute name. "+this.debugInfo(o));this.name=this.stringify.name(o),this.value=this.stringify.attValue(d),this.type=u.Attribute,this.isId=!1,this.schemaTypeInfo=null}return Object.defineProperty(l.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(l.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(l.prototype,"textContent",{get:function(){return this.value},set:function(h){return this.value=h||""}}),Object.defineProperty(l.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(l.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(l.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(l.prototype,"specified",{get:function(){return!0}}),l.prototype.clone=function(){return Object.create(this)},l.prototype.toString=function(h){return this.options.writer.attribute(this,this.options.writer.filterOptions(h))},l.prototype.debugInfo=function(h){return h=h||this.name,h==null?"parent: <"+this.parent.name+">":"attribute: {"+h+"}, parent: <"+this.parent.name+">"},l.prototype.isEqualNode=function(h){return!(h.namespaceURI!==this.namespaceURI||h.prefix!==this.prefix||h.localName!==this.localName||h.value!==this.value)},l})()}).call(uE)),ql.exports}var jl={exports:{}},oE=jl.exports,Ud;function $s(){return Ud||(Ud=1,(function(){jl.exports=(function(){function u(l){this.nodes=l}return Object.defineProperty(u.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),u.prototype.clone=function(){return this.nodes=null},u.prototype.getNamedItem=function(l){return this.nodes[l]},u.prototype.setNamedItem=function(l){var h;return h=this.nodes[l.nodeName],this.nodes[l.nodeName]=l,h||null},u.prototype.removeNamedItem=function(l){var h;return h=this.nodes[l],delete this.nodes[l],h||null},u.prototype.item=function(l){return this.nodes[Object.keys(this.nodes)[l]]||null},u.prototype.getNamedItemNS=function(l,h){throw new Error("This DOM method is not implemented.")},u.prototype.setNamedItemNS=function(l){throw new Error("This DOM method is not implemented.")},u.prototype.removeNamedItemNS=function(l,h){throw new Error("This DOM method is not implemented.")},u})()}).call(oE)),jl.exports}var sE=Xl.exports,Bd;function tc(){return Bd||(Bd=1,(function(){var u,l,h,o,d,c,s,f,g=function(D,A){for(var N in A)b.call(A,N)&&(D[N]=A[N]);function M(){this.constructor=D}return M.prototype=A.prototype,D.prototype=new M,D.__super__=A.prototype,D},b={}.hasOwnProperty;f=Dn(),s=f.isObject,c=f.isFunction,d=f.getValue,o=Ze(),u=ae(),l=Gy(),h=$s(),Xl.exports=(function(D){g(A,D);function A(N,M,x){var B,z,C,_;if(A.__super__.constructor.call(this,N),M==null)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(M),this.type=u.Element,this.attribs={},this.schemaTypeInfo=null,x!=null&&this.attribute(x),N.type===u.Document&&(this.isRoot=!0,this.documentObject=N,N.rootObject=this,N.children)){for(_=N.children,z=0,C=_.length;z<C;z++)if(B=_[z],B.type===u.DocType){B.name=this.name;break}}}return Object.defineProperty(A.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(A.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(A.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(A.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(A.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"attributes",{get:function(){return(!this.attributeMap||!this.attributeMap.nodes)&&(this.attributeMap=new h(this.attribs)),this.attributeMap}}),A.prototype.clone=function(){var N,M,x,B;x=Object.create(this),x.isRoot&&(x.documentObject=null),x.attribs={},B=this.attribs;for(M in B)b.call(B,M)&&(N=B[M],x.attribs[M]=N.clone());return x.children=[],this.children.forEach(function(z){var C;return C=z.clone(),C.parent=x,x.children.push(C)}),x},A.prototype.attribute=function(N,M){var x,B;if(N!=null&&(N=d(N)),s(N))for(x in N)b.call(N,x)&&(B=N[x],this.attribute(x,B));else c(M)&&(M=M.apply()),this.options.keepNullAttributes&&M==null?this.attribs[N]=new l(this,N,""):M!=null&&(this.attribs[N]=new l(this,N,M));return this},A.prototype.removeAttribute=function(N){var M,x,B;if(N==null)throw new Error("Missing attribute name. "+this.debugInfo());if(N=d(N),Array.isArray(N))for(x=0,B=N.length;x<B;x++)M=N[x],delete this.attribs[M];else delete this.attribs[N];return this},A.prototype.toString=function(N){return this.options.writer.element(this,this.options.writer.filterOptions(N))},A.prototype.att=function(N,M){return this.attribute(N,M)},A.prototype.a=function(N,M){return this.attribute(N,M)},A.prototype.getAttribute=function(N){return this.attribs.hasOwnProperty(N)?this.attribs[N].value:null},A.prototype.setAttribute=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNode=function(N){return this.attribs.hasOwnProperty(N)?this.attribs[N]:null},A.prototype.setAttributeNode=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.removeAttributeNode=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagName=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setAttributeNS=function(N,M,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.removeAttributeNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNodeNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setAttributeNodeNS=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagNameNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.hasAttribute=function(N){return this.attribs.hasOwnProperty(N)},A.prototype.hasAttributeNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setIdAttribute=function(N,M){return this.attribs.hasOwnProperty(N)?this.attribs[N].isId:M},A.prototype.setIdAttributeNS=function(N,M,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setIdAttributeNode=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagName=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagNameNS=function(N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByClassName=function(N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.isEqualNode=function(N){var M,x,B;if(!A.__super__.isEqualNode.apply(this,arguments).isEqualNode(N)||N.namespaceURI!==this.namespaceURI||N.prefix!==this.prefix||N.localName!==this.localName||N.attribs.length!==this.attribs.length)return!1;for(M=x=0,B=this.attribs.length-1;0<=B?x<=B:x>=B;M=0<=B?++x:--x)if(!this.attribs[M].isEqualNode(N.attribs[M]))return!1;return!0},A})(o)}).call(sE)),Xl.exports}var Hl={exports:{}},Yl={exports:{}},cE=Yl.exports,Xd;function yu(){return Xd||(Xd=1,(function(){var u,l=function(o,d){for(var c in d)h.call(d,c)&&(o[c]=d[c]);function s(){this.constructor=o}return s.prototype=d.prototype,o.prototype=new s,o.__super__=d.prototype,o},h={}.hasOwnProperty;u=Ze(),Yl.exports=(function(o){l(d,o);function d(c){d.__super__.constructor.call(this,c),this.value=""}return Object.defineProperty(d.prototype,"data",{get:function(){return this.value},set:function(c){return this.value=c||""}}),Object.defineProperty(d.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(d.prototype,"textContent",{get:function(){return this.value},set:function(c){return this.value=c||""}}),d.prototype.clone=function(){return Object.create(this)},d.prototype.substringData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.appendData=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.insertData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.deleteData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.replaceData=function(c,s,f){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.isEqualNode=function(c){return!(!d.__super__.isEqualNode.apply(this,arguments).isEqualNode(c)||c.data!==this.data)},d})(u)}).call(cE)),Yl.exports}var fE=Hl.exports,zd;function ec(){return zd||(zd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=ae(),l=yu(),Hl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=u.CData,this.value=this.stringify.cdata(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.cdata(this,this.options.writer.filterOptions(s))},c})(l)}).call(fE)),Hl.exports}var Vl={exports:{}},hE=Vl.exports,qd;function nc(){return qd||(qd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=ae(),l=yu(),Vl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=u.Comment,this.value=this.stringify.comment(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.comment(this,this.options.writer.filterOptions(s))},c})(l)}).call(hE)),Vl.exports}var Gl={exports:{}},pE=Gl.exports,jd;function ic(){return jd||(jd=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;h=Dn().isObject,l=Ze(),u=ae(),Gl.exports=(function(c){o(s,c);function s(f,g,b,D){var A;s.__super__.constructor.call(this,f),h(g)&&(A=g,g=A.version,b=A.encoding,D=A.standalone),g||(g="1.0"),this.type=u.Declaration,this.version=this.stringify.xmlVersion(g),b!=null&&(this.encoding=this.stringify.xmlEncoding(b)),D!=null&&(this.standalone=this.stringify.xmlStandalone(D))}return s.prototype.toString=function(f){return this.options.writer.declaration(this,this.options.writer.filterOptions(f))},s})(l)}).call(pE)),Gl.exports}var Fl={exports:{}},Il={exports:{}},dE=Il.exports,Hd;function rc(){return Hd||(Hd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=ae(),Il.exports=(function(d){h(c,d);function c(s,f,g,b,D,A){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD element name. "+this.debugInfo());if(g==null)throw new Error("Missing DTD attribute name. "+this.debugInfo(f));if(!b)throw new Error("Missing DTD attribute type. "+this.debugInfo(f));if(!D)throw new Error("Missing DTD attribute default. "+this.debugInfo(f));if(D.indexOf("#")!==0&&(D="#"+D),!D.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(f));if(A&&!D.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(f));this.elementName=this.stringify.name(f),this.type=u.AttributeDeclaration,this.attributeName=this.stringify.name(g),this.attributeType=this.stringify.dtdAttType(b),A&&(this.defaultValue=this.stringify.dtdAttDefault(A)),this.defaultValueType=D}return c.prototype.toString=function(s){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(s))},c})(l)}).call(dE)),Il.exports}var Ql={exports:{}},yE=Ql.exports,Yd;function ac(){return Yd||(Yd=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;h=Dn().isObject,l=Ze(),u=ae(),Ql.exports=(function(c){o(s,c);function s(f,g,b,D){if(s.__super__.constructor.call(this,f),b==null)throw new Error("Missing DTD entity name. "+this.debugInfo(b));if(D==null)throw new Error("Missing DTD entity value. "+this.debugInfo(b));if(this.pe=!!g,this.name=this.stringify.name(b),this.type=u.EntityDeclaration,!h(D))this.value=this.stringify.dtdEntityValue(D),this.internal=!0;else{if(!D.pubID&&!D.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(b));if(D.pubID&&!D.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(b));if(this.internal=!1,D.pubID!=null&&(this.pubID=this.stringify.dtdPubID(D.pubID)),D.sysID!=null&&(this.sysID=this.stringify.dtdSysID(D.sysID)),D.nData!=null&&(this.nData=this.stringify.dtdNData(D.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(b))}}return Object.defineProperty(s.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(s.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(s.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(s.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(s.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(s.prototype,"xmlVersion",{get:function(){return null}}),s.prototype.toString=function(f){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(f))},s})(l)}).call(yE)),Ql.exports}var Zl={exports:{}},mE=Zl.exports,Vd;function lc(){return Vd||(Vd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=ae(),Zl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD element name. "+this.debugInfo());g||(g="(#PCDATA)"),Array.isArray(g)&&(g="("+g.join(",")+")"),this.name=this.stringify.name(f),this.type=u.ElementDeclaration,this.value=this.stringify.dtdElementValue(g)}return c.prototype.toString=function(s){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(s))},c})(l)}).call(mE)),Zl.exports}var Kl={exports:{}},gE=Kl.exports,Gd;function uc(){return Gd||(Gd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=ae(),Kl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD notation name. "+this.debugInfo(f));if(!g.pubID&&!g.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(f));this.name=this.stringify.name(f),this.type=u.NotationDeclaration,g.pubID!=null&&(this.pubID=this.stringify.dtdPubID(g.pubID)),g.sysID!=null&&(this.sysID=this.stringify.dtdSysID(g.sysID))}return Object.defineProperty(c.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(c.prototype,"systemId",{get:function(){return this.sysID}}),c.prototype.toString=function(s){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(s))},c})(l)}).call(gE)),Kl.exports}var EE=Fl.exports,Fd;function oc(){return Fd||(Fd=1,(function(){var u,l,h,o,d,c,s,f,g=function(D,A){for(var N in A)b.call(A,N)&&(D[N]=A[N]);function M(){this.constructor=D}return M.prototype=A.prototype,D.prototype=new M,D.__super__=A.prototype,D},b={}.hasOwnProperty;f=Dn().isObject,s=Ze(),u=ae(),l=rc(),o=ac(),h=lc(),d=uc(),c=$s(),Fl.exports=(function(D){g(A,D);function A(N,M,x){var B,z,C,_,v,U;if(A.__super__.constructor.call(this,N),this.type=u.DocType,N.children){for(_=N.children,z=0,C=_.length;z<C;z++)if(B=_[z],B.type===u.Element){this.name=B.name;break}}this.documentObject=N,f(M)&&(v=M,M=v.pubID,x=v.sysID),x==null&&(U=[M,x],x=U[0],M=U[1]),M!=null&&(this.pubID=this.stringify.dtdPubID(M)),x!=null&&(this.sysID=this.stringify.dtdSysID(x))}return Object.defineProperty(A.prototype,"entities",{get:function(){var N,M,x,B,z;for(B={},z=this.children,M=0,x=z.length;M<x;M++)N=z[M],N.type===u.EntityDeclaration&&!N.pe&&(B[N.name]=N);return new c(B)}}),Object.defineProperty(A.prototype,"notations",{get:function(){var N,M,x,B,z;for(B={},z=this.children,M=0,x=z.length;M<x;M++)N=z[M],N.type===u.NotationDeclaration&&(B[N.name]=N);return new c(B)}}),Object.defineProperty(A.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(A.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(A.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),A.prototype.element=function(N,M){var x;return x=new h(this,N,M),this.children.push(x),this},A.prototype.attList=function(N,M,x,B,z){var C;return C=new l(this,N,M,x,B,z),this.children.push(C),this},A.prototype.entity=function(N,M){var x;return x=new o(this,!1,N,M),this.children.push(x),this},A.prototype.pEntity=function(N,M){var x;return x=new o(this,!0,N,M),this.children.push(x),this},A.prototype.notation=function(N,M){var x;return x=new d(this,N,M),this.children.push(x),this},A.prototype.toString=function(N){return this.options.writer.docType(this,this.options.writer.filterOptions(N))},A.prototype.ele=function(N,M){return this.element(N,M)},A.prototype.att=function(N,M,x,B,z){return this.attList(N,M,x,B,z)},A.prototype.ent=function(N,M){return this.entity(N,M)},A.prototype.pent=function(N,M){return this.pEntity(N,M)},A.prototype.not=function(N,M){return this.notation(N,M)},A.prototype.up=function(){return this.root()||this.documentObject},A.prototype.isEqualNode=function(N){return!(!A.__super__.isEqualNode.apply(this,arguments).isEqualNode(N)||N.name!==this.name||N.publicId!==this.publicId||N.systemId!==this.systemId)},A})(s)}).call(EE)),Fl.exports}var Pl={exports:{}},vE=Pl.exports,Id;function sc(){return Id||(Id=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=ae(),l=Ze(),Pl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing raw text. "+this.debugInfo());this.type=u.Raw,this.value=this.stringify.raw(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.raw(this,this.options.writer.filterOptions(s))},c})(l)}).call(vE)),Pl.exports}var Jl={exports:{}},TE=Jl.exports,Qd;function cc(){return Qd||(Qd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=ae(),l=yu(),Jl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=u.Text,this.value=this.stringify.text(f)}return Object.defineProperty(c.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(c.prototype,"wholeText",{get:function(){var s,f,g;for(g="",f=this.previousSibling;f;)g=f.data+g,f=f.previousSibling;for(g+=this.data,s=this.nextSibling;s;)g=g+s.data,s=s.nextSibling;return g}}),c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.text(this,this.options.writer.filterOptions(s))},c.prototype.splitText=function(s){throw new Error("This DOM method is not implemented."+this.debugInfo())},c.prototype.replaceWholeText=function(s){throw new Error("This DOM method is not implemented."+this.debugInfo())},c})(l)}).call(TE)),Jl.exports}var kl={exports:{}},bE=kl.exports,Zd;function fc(){return Zd||(Zd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=ae(),l=yu(),kl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing instruction target. "+this.debugInfo());this.type=u.ProcessingInstruction,this.target=this.stringify.insTarget(f),this.name=this.target,g&&(this.value=this.stringify.insValue(g))}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(s))},c.prototype.isEqualNode=function(s){return!(!c.__super__.isEqualNode.apply(this,arguments).isEqualNode(s)||s.target!==this.target)},c})(l)}).call(bE)),kl.exports}var Wl={exports:{}},SE=Wl.exports,Kd;function Fy(){return Kd||(Kd=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=ae(),Wl.exports=(function(d){h(c,d);function c(s){c.__super__.constructor.call(this,s),this.type=u.Dummy}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return""},c})(l)}).call(SE)),Wl.exports}var $l={exports:{}},DE=$l.exports,Pd;function AE(){return Pd||(Pd=1,(function(){$l.exports=(function(){function u(l){this.nodes=l}return Object.defineProperty(u.prototype,"length",{get:function(){return this.nodes.length||0}}),u.prototype.clone=function(){return this.nodes=null},u.prototype.item=function(l){return this.nodes[l]||null},u})()}).call(DE)),$l.exports}var tu={exports:{}},OE=tu.exports,Jd;function wE(){return Jd||(Jd=1,(function(){tu.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(OE)),tu.exports}var NE=Bl.exports,kd;function Ze(){return kd||(kd=1,(function(){var u,l,h,o,d,c,s,f,g,b,D,A,N,M,x,B,z,C={}.hasOwnProperty;z=Dn(),B=z.isObject,x=z.isFunction,M=z.isEmpty,N=z.getValue,f=null,h=null,o=null,d=null,c=null,D=null,A=null,b=null,s=null,l=null,g=null,u=null,Bl.exports=(function(){function _(v){this.parent=v,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.value=null,this.children=[],this.baseURI=null,f||(f=tc(),h=ec(),o=nc(),d=ic(),c=oc(),D=sc(),A=cc(),b=fc(),s=Fy(),l=ae(),g=AE(),$s(),u=wE())}return Object.defineProperty(_.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(_.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(_.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(_.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(_.prototype,"childNodes",{get:function(){return(!this.childNodeList||!this.childNodeList.nodes)&&(this.childNodeList=new g(this.children)),this.childNodeList}}),Object.defineProperty(_.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(_.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(_.prototype,"previousSibling",{get:function(){var v;return v=this.parent.children.indexOf(this),this.parent.children[v-1]||null}}),Object.defineProperty(_.prototype,"nextSibling",{get:function(){var v;return v=this.parent.children.indexOf(this),this.parent.children[v+1]||null}}),Object.defineProperty(_.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(_.prototype,"textContent",{get:function(){var v,U,X,W,lt;if(this.nodeType===l.Element||this.nodeType===l.DocumentFragment){for(lt="",W=this.children,U=0,X=W.length;U<X;U++)v=W[U],v.textContent&&(lt+=v.textContent);return lt}else return null},set:function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),_.prototype.setParent=function(v){var U,X,W,lt,$;for(this.parent=v,v&&(this.options=v.options,this.stringify=v.stringify),lt=this.children,$=[],X=0,W=lt.length;X<W;X++)U=lt[X],$.push(U.setParent(this));return $},_.prototype.element=function(v,U,X){var W,lt,$,E,J,it,ot,Tt,bt,St,I;if(it=null,U===null&&X==null&&(bt=[{},null],U=bt[0],X=bt[1]),U==null&&(U={}),U=N(U),B(U)||(St=[U,X],X=St[0],U=St[1]),v!=null&&(v=N(v)),Array.isArray(v))for($=0,ot=v.length;$<ot;$++)lt=v[$],it=this.element(lt);else if(x(v))it=this.element(v.apply());else if(B(v)){for(J in v)if(C.call(v,J))if(I=v[J],x(I)&&(I=I.apply()),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&J.indexOf(this.stringify.convertAttKey)===0)it=this.attribute(J.substr(this.stringify.convertAttKey.length),I);else if(!this.options.separateArrayItems&&Array.isArray(I)&&M(I))it=this.dummy();else if(B(I)&&M(I))it=this.element(J);else if(!this.options.keepNullNodes&&I==null)it=this.dummy();else if(!this.options.separateArrayItems&&Array.isArray(I))for(E=0,Tt=I.length;E<Tt;E++)lt=I[E],W={},W[J]=lt,it=this.element(W);else B(I)?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&J.indexOf(this.stringify.convertTextKey)===0?it=this.element(I):(it=this.element(J),it.element(I)):it=this.element(J,I)}else!this.options.keepNullNodes&&X===null?it=this.dummy():!this.options.ignoreDecorators&&this.stringify.convertTextKey&&v.indexOf(this.stringify.convertTextKey)===0?it=this.text(X):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&v.indexOf(this.stringify.convertCDataKey)===0?it=this.cdata(X):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&v.indexOf(this.stringify.convertCommentKey)===0?it=this.comment(X):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&v.indexOf(this.stringify.convertRawKey)===0?it=this.raw(X):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&v.indexOf(this.stringify.convertPIKey)===0?it=this.instruction(v.substr(this.stringify.convertPIKey.length),X):it=this.node(v,U,X);if(it==null)throw new Error("Could not create any elements with: "+v+". "+this.debugInfo());return it},_.prototype.insertBefore=function(v,U,X){var W,lt,$,E,J;if(v?.type)return $=v,E=U,$.setParent(this),E?(lt=children.indexOf(E),J=children.splice(lt),children.push($),Array.prototype.push.apply(children,J)):children.push($),$;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(v));return lt=this.parent.children.indexOf(this),J=this.parent.children.splice(lt),W=this.parent.element(v,U,X),Array.prototype.push.apply(this.parent.children,J),W},_.prototype.insertAfter=function(v,U,X){var W,lt,$;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(v));return lt=this.parent.children.indexOf(this),$=this.parent.children.splice(lt+1),W=this.parent.element(v,U,X),Array.prototype.push.apply(this.parent.children,$),W},_.prototype.remove=function(){var v;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return v=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[v,v-v+1].concat([])),this.parent},_.prototype.node=function(v,U,X){var W,lt;return v!=null&&(v=N(v)),U||(U={}),U=N(U),B(U)||(lt=[U,X],X=lt[0],U=lt[1]),W=new f(this,v,U),X!=null&&W.text(X),this.children.push(W),W},_.prototype.text=function(v){var U;return B(v)&&this.element(v),U=new A(this,v),this.children.push(U),this},_.prototype.cdata=function(v){var U;return U=new h(this,v),this.children.push(U),this},_.prototype.comment=function(v){var U;return U=new o(this,v),this.children.push(U),this},_.prototype.commentBefore=function(v){var U,X;return U=this.parent.children.indexOf(this),X=this.parent.children.splice(U),this.parent.comment(v),Array.prototype.push.apply(this.parent.children,X),this},_.prototype.commentAfter=function(v){var U,X;return U=this.parent.children.indexOf(this),X=this.parent.children.splice(U+1),this.parent.comment(v),Array.prototype.push.apply(this.parent.children,X),this},_.prototype.raw=function(v){var U;return U=new D(this,v),this.children.push(U),this},_.prototype.dummy=function(){var v;return v=new s(this),v},_.prototype.instruction=function(v,U){var X,W,lt,$,E;if(v!=null&&(v=N(v)),U!=null&&(U=N(U)),Array.isArray(v))for($=0,E=v.length;$<E;$++)X=v[$],this.instruction(X);else if(B(v))for(X in v)C.call(v,X)&&(W=v[X],this.instruction(X,W));else x(U)&&(U=U.apply()),lt=new b(this,v,U),this.children.push(lt);return this},_.prototype.instructionBefore=function(v,U){var X,W;return X=this.parent.children.indexOf(this),W=this.parent.children.splice(X),this.parent.instruction(v,U),Array.prototype.push.apply(this.parent.children,W),this},_.prototype.instructionAfter=function(v,U){var X,W;return X=this.parent.children.indexOf(this),W=this.parent.children.splice(X+1),this.parent.instruction(v,U),Array.prototype.push.apply(this.parent.children,W),this},_.prototype.declaration=function(v,U,X){var W,lt;return W=this.document(),lt=new d(W,v,U,X),W.children.length===0?W.children.unshift(lt):W.children[0].type===l.Declaration?W.children[0]=lt:W.children.unshift(lt),W.root()||W},_.prototype.dtd=function(v,U){var X,W,lt,$,E,J,it,ot,Tt,bt;for(W=this.document(),lt=new c(W,v,U),Tt=W.children,$=E=0,it=Tt.length;E<it;$=++E)if(X=Tt[$],X.type===l.DocType)return W.children[$]=lt,lt;for(bt=W.children,$=J=0,ot=bt.length;J<ot;$=++J)if(X=bt[$],X.isRoot)return W.children.splice($,0,lt),lt;return W.children.push(lt),lt},_.prototype.up=function(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent},_.prototype.root=function(){var v;for(v=this;v;){if(v.type===l.Document)return v.rootObject;if(v.isRoot)return v;v=v.parent}},_.prototype.document=function(){var v;for(v=this;v;){if(v.type===l.Document)return v;v=v.parent}},_.prototype.end=function(v){return this.document().end(v)},_.prototype.prev=function(){var v;if(v=this.parent.children.indexOf(this),v<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[v-1]},_.prototype.next=function(){var v;if(v=this.parent.children.indexOf(this),v===-1||v===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[v+1]},_.prototype.importDocument=function(v){var U;return U=v.root().clone(),U.parent=this,U.isRoot=!1,this.children.push(U),this},_.prototype.debugInfo=function(v){var U,X;return v=v||this.name,v==null&&!((U=this.parent)!=null&&U.name)?"":v==null?"parent: <"+this.parent.name+">":(X=this.parent)!=null&&X.name?"node: <"+v+">, parent: <"+this.parent.name+">":"node: <"+v+">"},_.prototype.ele=function(v,U,X){return this.element(v,U,X)},_.prototype.nod=function(v,U,X){return this.node(v,U,X)},_.prototype.txt=function(v){return this.text(v)},_.prototype.dat=function(v){return this.cdata(v)},_.prototype.com=function(v){return this.comment(v)},_.prototype.ins=function(v,U){return this.instruction(v,U)},_.prototype.doc=function(){return this.document()},_.prototype.dec=function(v,U,X){return this.declaration(v,U,X)},_.prototype.e=function(v,U,X){return this.element(v,U,X)},_.prototype.n=function(v,U,X){return this.node(v,U,X)},_.prototype.t=function(v){return this.text(v)},_.prototype.d=function(v){return this.cdata(v)},_.prototype.c=function(v){return this.comment(v)},_.prototype.r=function(v){return this.raw(v)},_.prototype.i=function(v,U){return this.instruction(v,U)},_.prototype.u=function(){return this.up()},_.prototype.importXMLBuilder=function(v){return this.importDocument(v)},_.prototype.replaceChild=function(v,U){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.removeChild=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.appendChild=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.hasChildNodes=function(){return this.children.length!==0},_.prototype.cloneNode=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.normalize=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isSupported=function(v,U){return!0},_.prototype.hasAttributes=function(){return this.attribs.length!==0},_.prototype.compareDocumentPosition=function(v){var U,X;return U=this,U===v?0:this.document()!==v.document()?(X=u.Disconnected|u.ImplementationSpecific,Math.random()<.5?X|=u.Preceding:X|=u.Following,X):U.isAncestor(v)?u.Contains|u.Preceding:U.isDescendant(v)?u.Contains|u.Following:U.isPreceding(v)?u.Preceding:u.Following},_.prototype.isSameNode=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.lookupPrefix=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isDefaultNamespace=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.lookupNamespaceURI=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isEqualNode=function(v){var U,X,W;if(v.nodeType!==this.nodeType||v.children.length!==this.children.length)return!1;for(U=X=0,W=this.children.length-1;0<=W?X<=W:X>=W;U=0<=W?++X:--X)if(!this.children[U].isEqualNode(v.children[U]))return!1;return!0},_.prototype.getFeature=function(v,U){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.setUserData=function(v,U,X){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.getUserData=function(v){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.contains=function(v){return v?v===this||this.isDescendant(v):!1},_.prototype.isDescendant=function(v){var U,X,W,lt,$;for($=this.children,W=0,lt=$.length;W<lt;W++)if(U=$[W],v===U||(X=U.isDescendant(v),X))return!0;return!1},_.prototype.isAncestor=function(v){return v.isDescendant(this)},_.prototype.isPreceding=function(v){var U,X;return U=this.treePosition(v),X=this.treePosition(this),U===-1||X===-1?!1:U<X},_.prototype.isFollowing=function(v){var U,X;return U=this.treePosition(v),X=this.treePosition(this),U===-1||X===-1?!1:U>X},_.prototype.treePosition=function(v){var U,X;return X=0,U=!1,this.foreachTreeNode(this.document(),function(W){if(X++,!U&&W===v)return U=!0}),U?X:-1},_.prototype.foreachTreeNode=function(v,U){var X,W,lt,$,E;for(v||(v=this.document()),$=v.children,W=0,lt=$.length;W<lt;W++){if(X=$[W],E=U(X))return E;if(E=this.foreachTreeNode(X,U),E)return E}},_})()}).call(NE)),Bl.exports}var eu={exports:{}},xE=eu.exports,Wd;function Iy(){return Wd||(Wd=1,(function(){var u=function(h,o){return function(){return h.apply(o,arguments)}},l={}.hasOwnProperty;eu.exports=(function(){function h(o){this.assertLegalName=u(this.assertLegalName,this),this.assertLegalChar=u(this.assertLegalChar,this);var d,c,s;o||(o={}),this.options=o,this.options.version||(this.options.version="1.0"),c=o.stringify||{};for(d in c)l.call(c,d)&&(s=c[d],this[d]=s)}return h.prototype.name=function(o){return this.options.noValidation?o:this.assertLegalName(""+o||"")},h.prototype.text=function(o){return this.options.noValidation?o:this.assertLegalChar(this.textEscape(""+o||""))},h.prototype.cdata=function(o){return this.options.noValidation?o:(o=""+o||"",o=o.replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(o))},h.prototype.comment=function(o){if(this.options.noValidation)return o;if(o=""+o||"",o.match(/--/))throw new Error("Comment text cannot contain double-hypen: "+o);return this.assertLegalChar(o)},h.prototype.raw=function(o){return this.options.noValidation?o:""+o||""},h.prototype.attValue=function(o){return this.options.noValidation?o:this.assertLegalChar(this.attEscape(o=""+o||""))},h.prototype.insTarget=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.insValue=function(o){if(this.options.noValidation)return o;if(o=""+o||"",o.match(/\?>/))throw new Error("Invalid processing instruction value: "+o);return this.assertLegalChar(o)},h.prototype.xmlVersion=function(o){if(this.options.noValidation)return o;if(o=""+o||"",!o.match(/1\.[0-9]+/))throw new Error("Invalid version number: "+o);return o},h.prototype.xmlEncoding=function(o){if(this.options.noValidation)return o;if(o=""+o||"",!o.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+o);return this.assertLegalChar(o)},h.prototype.xmlStandalone=function(o){return this.options.noValidation?o:o?"yes":"no"},h.prototype.dtdPubID=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdSysID=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdElementValue=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdAttType=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdAttDefault=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdEntityValue=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdNData=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.convertAttKey="@",h.prototype.convertPIKey="?",h.prototype.convertTextKey="#text",h.prototype.convertCDataKey="#cdata",h.prototype.convertCommentKey="#comment",h.prototype.convertRawKey="#raw",h.prototype.assertLegalChar=function(o){var d,c;if(this.options.noValidation)return o;if(d="",this.options.version==="1.0"){if(d=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,c=o.match(d))throw new Error("Invalid character in string: "+o+" at index "+c.index)}else if(this.options.version==="1.1"&&(d=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,c=o.match(d)))throw new Error("Invalid character in string: "+o+" at index "+c.index);return o},h.prototype.assertLegalName=function(o){var d;if(this.options.noValidation)return o;if(this.assertLegalChar(o),d=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!o.match(d))throw new Error("Invalid character in name");return o},h.prototype.textEscape=function(o){var d;return this.options.noValidation?o:(d=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,o.replace(d,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;"))},h.prototype.attEscape=function(o){var d;return this.options.noValidation?o:(d=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,o.replace(d,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;"))},h})()}).call(xE)),eu.exports}var nu={exports:{}},iu={exports:{}},ru={exports:{}},ME=ru.exports,$d;function mu(){return $d||($d=1,(function(){ru.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(ME)),ru.exports}var _E=iu.exports,ty;function Qy(){return ty||(ty=1,(function(){var u,l,h,o={}.hasOwnProperty;h=Dn().assign,u=ae(),ic(),oc(),ec(),nc(),tc(),sc(),cc(),fc(),Fy(),rc(),lc(),ac(),uc(),l=mu(),iu.exports=(function(){function d(c){var s,f,g;c||(c={}),this.options=c,f=c.writer||{};for(s in f)o.call(f,s)&&(g=f[s],this["_"+s]=this[s],this[s]=g)}return d.prototype.filterOptions=function(c){var s,f,g,b,D,A,N,M;return c||(c={}),c=h({},this.options,c),s={writer:this},s.pretty=c.pretty||!1,s.allowEmpty=c.allowEmpty||!1,s.indent=(f=c.indent)!=null?f:"  ",s.newline=(g=c.newline)!=null?g:`
`,s.offset=(b=c.offset)!=null?b:0,s.dontPrettyTextNodes=(D=(A=c.dontPrettyTextNodes)!=null?A:c.dontprettytextnodes)!=null?D:0,s.spaceBeforeSlash=(N=(M=c.spaceBeforeSlash)!=null?M:c.spacebeforeslash)!=null?N:"",s.spaceBeforeSlash===!0&&(s.spaceBeforeSlash=" "),s.suppressPrettyCount=0,s.user={},s.state=l.None,s},d.prototype.indent=function(c,s,f){var g;return!s.pretty||s.suppressPrettyCount?"":s.pretty&&(g=(f||0)+s.offset+1,g>0)?new Array(g).join(s.indent):""},d.prototype.endline=function(c,s,f){return!s.pretty||s.suppressPrettyCount?"":s.newline},d.prototype.attribute=function(c,s,f){var g;return this.openAttribute(c,s,f),g=" "+c.name+'="'+c.value+'"',this.closeAttribute(c,s,f),g},d.prototype.cdata=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<![CDATA[",s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+="]]>"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.comment=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!-- ",s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=" -->"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.declaration=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<?xml",s.state=l.InsideTag,g+=' version="'+c.version+'"',c.encoding!=null&&(g+=' encoding="'+c.encoding+'"'),c.standalone!=null&&(g+=' standalone="'+c.standalone+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+"?>",g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.docType=function(c,s,f){var g,b,D,A,N;if(f||(f=0),this.openNode(c,s,f),s.state=l.OpenTag,A=this.indent(c,s,f),A+="<!DOCTYPE "+c.root().name,c.pubID&&c.sysID?A+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.sysID&&(A+=' SYSTEM "'+c.sysID+'"'),c.children.length>0){for(A+=" [",A+=this.endline(c,s,f),s.state=l.InsideTag,N=c.children,b=0,D=N.length;b<D;b++)g=N[b],A+=this.writeChildNode(g,s,f+1);s.state=l.CloseTag,A+="]"}return s.state=l.CloseTag,A+=s.spaceBeforeSlash+">",A+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),A},d.prototype.element=function(c,s,f){var g,b,D,A,N,M,x,B,z,C,_,v,U,X;f||(f=0),C=!1,_="",this.openNode(c,s,f),s.state=l.OpenTag,_+=this.indent(c,s,f)+"<"+c.name,v=c.attribs;for(z in v)o.call(v,z)&&(g=v[z],_+=this.attribute(g,s,f));if(D=c.children.length,A=D===0?null:c.children[0],D===0||c.children.every(function(W){return(W.type===u.Text||W.type===u.Raw)&&W.value===""}))s.allowEmpty?(_+=">",s.state=l.CloseTag,_+="</"+c.name+">"+this.endline(c,s,f)):(s.state=l.CloseTag,_+=s.spaceBeforeSlash+"/>"+this.endline(c,s,f));else if(s.pretty&&D===1&&(A.type===u.Text||A.type===u.Raw)&&A.value!=null)_+=">",s.state=l.InsideTag,s.suppressPrettyCount++,C=!0,_+=this.writeChildNode(A,s,f+1),s.suppressPrettyCount--,C=!1,s.state=l.CloseTag,_+="</"+c.name+">"+this.endline(c,s,f);else{if(s.dontPrettyTextNodes){for(U=c.children,N=0,x=U.length;N<x;N++)if(b=U[N],(b.type===u.Text||b.type===u.Raw)&&b.value!=null){s.suppressPrettyCount++,C=!0;break}}for(_+=">"+this.endline(c,s,f),s.state=l.InsideTag,X=c.children,M=0,B=X.length;M<B;M++)b=X[M],_+=this.writeChildNode(b,s,f+1);s.state=l.CloseTag,_+=this.indent(c,s,f)+"</"+c.name+">",C&&s.suppressPrettyCount--,_+=this.endline(c,s,f),s.state=l.None}return this.closeNode(c,s,f),_},d.prototype.writeChildNode=function(c,s,f){switch(c.type){case u.CData:return this.cdata(c,s,f);case u.Comment:return this.comment(c,s,f);case u.Element:return this.element(c,s,f);case u.Raw:return this.raw(c,s,f);case u.Text:return this.text(c,s,f);case u.ProcessingInstruction:return this.processingInstruction(c,s,f);case u.Dummy:return"";case u.Declaration:return this.declaration(c,s,f);case u.DocType:return this.docType(c,s,f);case u.AttributeDeclaration:return this.dtdAttList(c,s,f);case u.ElementDeclaration:return this.dtdElement(c,s,f);case u.EntityDeclaration:return this.dtdEntity(c,s,f);case u.NotationDeclaration:return this.dtdNotation(c,s,f);default:throw new Error("Unknown XML node type: "+c.constructor.name)}},d.prototype.processingInstruction=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<?",s.state=l.InsideTag,g+=c.target,c.value&&(g+=" "+c.value),s.state=l.CloseTag,g+=s.spaceBeforeSlash+"?>",g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.raw=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f),s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.text=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f),s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdAttList=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ATTLIST",s.state=l.InsideTag,g+=" "+c.elementName+" "+c.attributeName+" "+c.attributeType,c.defaultValueType!=="#DEFAULT"&&(g+=" "+c.defaultValueType),c.defaultValue&&(g+=' "'+c.defaultValue+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdElement=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ELEMENT",s.state=l.InsideTag,g+=" "+c.name+" "+c.value,s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdEntity=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ENTITY",s.state=l.InsideTag,c.pe&&(g+=" %"),g+=" "+c.name,c.value?g+=' "'+c.value+'"':(c.pubID&&c.sysID?g+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.sysID&&(g+=' SYSTEM "'+c.sysID+'"'),c.nData&&(g+=" NDATA "+c.nData)),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdNotation=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!NOTATION",s.state=l.InsideTag,g+=" "+c.name,c.pubID&&c.sysID?g+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.pubID?g+=' PUBLIC "'+c.pubID+'"':c.sysID&&(g+=' SYSTEM "'+c.sysID+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.openNode=function(c,s,f){},d.prototype.closeNode=function(c,s,f){},d.prototype.openAttribute=function(c,s,f){},d.prototype.closeAttribute=function(c,s,f){},d})()}).call(_E)),iu.exports}var RE=nu.exports,ey;function hc(){return ey||(ey=1,(function(){var u,l=function(o,d){for(var c in d)h.call(d,c)&&(o[c]=d[c]);function s(){this.constructor=o}return s.prototype=d.prototype,o.prototype=new s,o.__super__=d.prototype,o},h={}.hasOwnProperty;u=Qy(),nu.exports=(function(o){l(d,o);function d(c){d.__super__.constructor.call(this,c)}return d.prototype.document=function(c,s){var f,g,b,D,A;for(s=this.filterOptions(s),D="",A=c.children,g=0,b=A.length;g<b;g++)f=A[g],D+=this.writeChildNode(f,s,0);return s.pretty&&D.slice(-s.newline.length)===s.newline&&(D=D.slice(0,-s.newline.length)),D},d})(u)}).call(RE)),nu.exports}var CE=Rl.exports,ny;function Zy(){return ny||(ny=1,(function(){var u,l,h,o,d,c,s,f=function(b,D){for(var A in D)g.call(D,A)&&(b[A]=D[A]);function N(){this.constructor=b}return N.prototype=D.prototype,b.prototype=new N,b.__super__=D.prototype,b},g={}.hasOwnProperty;s=Dn().isPlainObject,h=Vy(),l=aE(),o=Ze(),u=ae(),c=Iy(),d=hc(),Rl.exports=(function(b){f(D,b);function D(A){D.__super__.constructor.call(this,null),this.name="#document",this.type=u.Document,this.documentURI=null,this.domConfig=new l,A||(A={}),A.writer||(A.writer=new d),this.options=A,this.stringify=new c(A)}return Object.defineProperty(D.prototype,"implementation",{value:new h}),Object.defineProperty(D.prototype,"doctype",{get:function(){var A,N,M,x;for(x=this.children,N=0,M=x.length;N<M;N++)if(A=x[N],A.type===u.DocType)return A;return null}}),Object.defineProperty(D.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(D.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(D.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(D.prototype,"xmlEncoding",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].encoding:null}}),Object.defineProperty(D.prototype,"xmlStandalone",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].standalone==="yes":!1}}),Object.defineProperty(D.prototype,"xmlVersion",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].version:"1.0"}}),Object.defineProperty(D.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(D.prototype,"origin",{get:function(){return null}}),Object.defineProperty(D.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(D.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(D.prototype,"contentType",{get:function(){return null}}),D.prototype.end=function(A){var N;return N={},A?s(A)&&(N=A,A=this.options.writer):A=this.options.writer,A.document(this,A.filterOptions(N))},D.prototype.toString=function(A){return this.options.writer.document(this,this.options.writer.filterOptions(A))},D.prototype.createElement=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createDocumentFragment=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createTextNode=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createComment=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createCDATASection=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createProcessingInstruction=function(A,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createAttribute=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createEntityReference=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.getElementsByTagName=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.importNode=function(A,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createElementNS=function(A,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createAttributeNS=function(A,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.getElementsByTagNameNS=function(A,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.getElementById=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.adoptNode=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.normalizeDocument=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.renameNode=function(A,N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.getElementsByClassName=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createEvent=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createRange=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createNodeIterator=function(A,N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},D.prototype.createTreeWalker=function(A,N,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},D})(o)}).call(CE)),Rl.exports}var au={exports:{}},LE=au.exports,iy;function UE(){return iy||(iy=1,(function(){var u,l,h,o,d,c,s,f,g,b,D,A,N,M,x,B,z,C,_,v,U,X,W,lt={}.hasOwnProperty;W=Dn(),U=W.isObject,v=W.isFunction,X=W.isPlainObject,_=W.getValue,u=ae(),A=Zy(),N=tc(),o=ec(),d=nc(),x=sc(),C=cc(),M=fc(),b=ic(),D=oc(),c=rc(),f=ac(),s=lc(),g=uc(),h=Gy(),z=Iy(),B=hc(),l=mu(),au.exports=(function(){function $(E,J,it){var ot;this.name="?xml",this.type=u.Document,E||(E={}),ot={},E.writer?X(E.writer)&&(ot=E.writer,E.writer=new B):E.writer=new B,this.options=E,this.writer=E.writer,this.writerOptions=this.writer.filterOptions(ot),this.stringify=new z(E),this.onDataCallback=J||function(){},this.onEndCallback=it||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}return $.prototype.createChildNode=function(E){var J,it,ot,Tt,bt,St,I,rt;switch(E.type){case u.CData:this.cdata(E.value);break;case u.Comment:this.comment(E.value);break;case u.Element:ot={},I=E.attribs;for(it in I)lt.call(I,it)&&(J=I[it],ot[it]=J.value);this.node(E.name,ot);break;case u.Dummy:this.dummy();break;case u.Raw:this.raw(E.value);break;case u.Text:this.text(E.value);break;case u.ProcessingInstruction:this.instruction(E.target,E.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+E.constructor.name)}for(rt=E.children,bt=0,St=rt.length;bt<St;bt++)Tt=rt[bt],this.createChildNode(Tt),Tt.type===u.Element&&this.up();return this},$.prototype.dummy=function(){return this},$.prototype.node=function(E,J,it){var ot;if(E==null)throw new Error("Missing node name.");if(this.root&&this.currentLevel===-1)throw new Error("Document can only have one root node. "+this.debugInfo(E));return this.openCurrent(),E=_(E),J==null&&(J={}),J=_(J),U(J)||(ot=[J,it],it=ot[0],J=ot[1]),this.currentNode=new N(this,E,J),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,it!=null&&this.text(it),this},$.prototype.element=function(E,J,it){var ot,Tt,bt,St,I,rt;if(this.currentNode&&this.currentNode.type===u.DocType)this.dtdElement.apply(this,arguments);else if(Array.isArray(E)||U(E)||v(E))for(St=this.options.noValidation,this.options.noValidation=!0,rt=new A(this.options).element("TEMP_ROOT"),rt.element(E),this.options.noValidation=St,I=rt.children,Tt=0,bt=I.length;Tt<bt;Tt++)ot=I[Tt],this.createChildNode(ot),ot.type===u.Element&&this.up();else this.node(E,J,it);return this},$.prototype.attribute=function(E,J){var it,ot;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(E));if(E!=null&&(E=_(E)),U(E))for(it in E)lt.call(E,it)&&(ot=E[it],this.attribute(it,ot));else v(J)&&(J=J.apply()),this.options.keepNullAttributes&&J==null?this.currentNode.attribs[E]=new h(this,E,""):J!=null&&(this.currentNode.attribs[E]=new h(this,E,J));return this},$.prototype.text=function(E){var J;return this.openCurrent(),J=new C(this,E),this.onData(this.writer.text(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.cdata=function(E){var J;return this.openCurrent(),J=new o(this,E),this.onData(this.writer.cdata(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.comment=function(E){var J;return this.openCurrent(),J=new d(this,E),this.onData(this.writer.comment(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.raw=function(E){var J;return this.openCurrent(),J=new x(this,E),this.onData(this.writer.raw(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.instruction=function(E,J){var it,ot,Tt,bt,St;if(this.openCurrent(),E!=null&&(E=_(E)),J!=null&&(J=_(J)),Array.isArray(E))for(it=0,bt=E.length;it<bt;it++)ot=E[it],this.instruction(ot);else if(U(E))for(ot in E)lt.call(E,ot)&&(Tt=E[ot],this.instruction(ot,Tt));else v(J)&&(J=J.apply()),St=new M(this,E,J),this.onData(this.writer.processingInstruction(St,this.writerOptions,this.currentLevel+1),this.currentLevel+1);return this},$.prototype.declaration=function(E,J,it){var ot;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return ot=new b(this,E,J,it),this.onData(this.writer.declaration(ot,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.doctype=function(E,J,it){if(this.openCurrent(),E==null)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new D(this,J,it),this.currentNode.rootNodeName=E,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this},$.prototype.dtdElement=function(E,J){var it;return this.openCurrent(),it=new s(this,E,J),this.onData(this.writer.dtdElement(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.attList=function(E,J,it,ot,Tt){var bt;return this.openCurrent(),bt=new c(this,E,J,it,ot,Tt),this.onData(this.writer.dtdAttList(bt,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.entity=function(E,J){var it;return this.openCurrent(),it=new f(this,!1,E,J),this.onData(this.writer.dtdEntity(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.pEntity=function(E,J){var it;return this.openCurrent(),it=new f(this,!0,E,J),this.onData(this.writer.dtdEntity(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.notation=function(E,J){var it;return this.openCurrent(),it=new g(this,E,J),this.onData(this.writer.dtdNotation(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.up=function(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this},$.prototype.end=function(){for(;this.currentLevel>=0;)this.up();return this.onEnd()},$.prototype.openCurrent=function(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)},$.prototype.openNode=function(E){var J,it,ot,Tt;if(!E.isOpen){if(!this.root&&this.currentLevel===0&&E.type===u.Element&&(this.root=E),it="",E.type===u.Element){this.writerOptions.state=l.OpenTag,it=this.writer.indent(E,this.writerOptions,this.currentLevel)+"<"+E.name,Tt=E.attribs;for(ot in Tt)lt.call(Tt,ot)&&(J=Tt[ot],it+=this.writer.attribute(J,this.writerOptions,this.currentLevel));it+=(E.children?">":"/>")+this.writer.endline(E,this.writerOptions,this.currentLevel),this.writerOptions.state=l.InsideTag}else this.writerOptions.state=l.OpenTag,it=this.writer.indent(E,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+E.rootNodeName,E.pubID&&E.sysID?it+=' PUBLIC "'+E.pubID+'" "'+E.sysID+'"':E.sysID&&(it+=' SYSTEM "'+E.sysID+'"'),E.children?(it+=" [",this.writerOptions.state=l.InsideTag):(this.writerOptions.state=l.CloseTag,it+=">"),it+=this.writer.endline(E,this.writerOptions,this.currentLevel);return this.onData(it,this.currentLevel),E.isOpen=!0}},$.prototype.closeNode=function(E){var J;if(!E.isClosed)return J="",this.writerOptions.state=l.CloseTag,E.type===u.Element?J=this.writer.indent(E,this.writerOptions,this.currentLevel)+"</"+E.name+">"+this.writer.endline(E,this.writerOptions,this.currentLevel):J=this.writer.indent(E,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(E,this.writerOptions,this.currentLevel),this.writerOptions.state=l.None,this.onData(J,this.currentLevel),E.isClosed=!0},$.prototype.onData=function(E,J){return this.documentStarted=!0,this.onDataCallback(E,J+1)},$.prototype.onEnd=function(){return this.documentCompleted=!0,this.onEndCallback()},$.prototype.debugInfo=function(E){return E==null?"":"node: <"+E+">"},$.prototype.ele=function(){return this.element.apply(this,arguments)},$.prototype.nod=function(E,J,it){return this.node(E,J,it)},$.prototype.txt=function(E){return this.text(E)},$.prototype.dat=function(E){return this.cdata(E)},$.prototype.com=function(E){return this.comment(E)},$.prototype.ins=function(E,J){return this.instruction(E,J)},$.prototype.dec=function(E,J,it){return this.declaration(E,J,it)},$.prototype.dtd=function(E,J,it){return this.doctype(E,J,it)},$.prototype.e=function(E,J,it){return this.element(E,J,it)},$.prototype.n=function(E,J,it){return this.node(E,J,it)},$.prototype.t=function(E){return this.text(E)},$.prototype.d=function(E){return this.cdata(E)},$.prototype.c=function(E){return this.comment(E)},$.prototype.r=function(E){return this.raw(E)},$.prototype.i=function(E,J){return this.instruction(E,J)},$.prototype.att=function(){return this.currentNode&&this.currentNode.type===u.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},$.prototype.a=function(){return this.currentNode&&this.currentNode.type===u.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},$.prototype.ent=function(E,J){return this.entity(E,J)},$.prototype.pent=function(E,J){return this.pEntity(E,J)},$.prototype.not=function(E,J){return this.notation(E,J)},$})()}).call(LE)),au.exports}var lu={exports:{}},BE=lu.exports,ry;function XE(){return ry||(ry=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;u=ae(),h=Qy(),l=mu(),lu.exports=(function(c){o(s,c);function s(f,g){this.stream=f,s.__super__.constructor.call(this,g)}return s.prototype.endline=function(f,g,b){return f.isLastRootNode&&g.state===l.CloseTag?"":s.__super__.endline.call(this,f,g,b)},s.prototype.document=function(f,g){var b,D,A,N,M,x,B,z,C;for(B=f.children,D=A=0,M=B.length;A<M;D=++A)b=B[D],b.isLastRootNode=D===f.children.length-1;for(g=this.filterOptions(g),z=f.children,C=[],N=0,x=z.length;N<x;N++)b=z[N],C.push(this.writeChildNode(b,g,0));return C},s.prototype.attribute=function(f,g,b){return this.stream.write(s.__super__.attribute.call(this,f,g,b))},s.prototype.cdata=function(f,g,b){return this.stream.write(s.__super__.cdata.call(this,f,g,b))},s.prototype.comment=function(f,g,b){return this.stream.write(s.__super__.comment.call(this,f,g,b))},s.prototype.declaration=function(f,g,b){return this.stream.write(s.__super__.declaration.call(this,f,g,b))},s.prototype.docType=function(f,g,b){var D,A,N,M;if(b||(b=0),this.openNode(f,g,b),g.state=l.OpenTag,this.stream.write(this.indent(f,g,b)),this.stream.write("<!DOCTYPE "+f.root().name),f.pubID&&f.sysID?this.stream.write(' PUBLIC "'+f.pubID+'" "'+f.sysID+'"'):f.sysID&&this.stream.write(' SYSTEM "'+f.sysID+'"'),f.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(f,g,b)),g.state=l.InsideTag,M=f.children,A=0,N=M.length;A<N;A++)D=M[A],this.writeChildNode(D,g,b+1);g.state=l.CloseTag,this.stream.write("]")}return g.state=l.CloseTag,this.stream.write(g.spaceBeforeSlash+">"),this.stream.write(this.endline(f,g,b)),g.state=l.None,this.closeNode(f,g,b)},s.prototype.element=function(f,g,b){var D,A,N,M,x,B,z,C,_;b||(b=0),this.openNode(f,g,b),g.state=l.OpenTag,this.stream.write(this.indent(f,g,b)+"<"+f.name),C=f.attribs;for(z in C)d.call(C,z)&&(D=C[z],this.attribute(D,g,b));if(N=f.children.length,M=N===0?null:f.children[0],N===0||f.children.every(function(v){return(v.type===u.Text||v.type===u.Raw)&&v.value===""}))g.allowEmpty?(this.stream.write(">"),g.state=l.CloseTag,this.stream.write("</"+f.name+">")):(g.state=l.CloseTag,this.stream.write(g.spaceBeforeSlash+"/>"));else if(g.pretty&&N===1&&(M.type===u.Text||M.type===u.Raw)&&M.value!=null)this.stream.write(">"),g.state=l.InsideTag,g.suppressPrettyCount++,this.writeChildNode(M,g,b+1),g.suppressPrettyCount--,g.state=l.CloseTag,this.stream.write("</"+f.name+">");else{for(this.stream.write(">"+this.endline(f,g,b)),g.state=l.InsideTag,_=f.children,x=0,B=_.length;x<B;x++)A=_[x],this.writeChildNode(A,g,b+1);g.state=l.CloseTag,this.stream.write(this.indent(f,g,b)+"</"+f.name+">")}return this.stream.write(this.endline(f,g,b)),g.state=l.None,this.closeNode(f,g,b)},s.prototype.processingInstruction=function(f,g,b){return this.stream.write(s.__super__.processingInstruction.call(this,f,g,b))},s.prototype.raw=function(f,g,b){return this.stream.write(s.__super__.raw.call(this,f,g,b))},s.prototype.text=function(f,g,b){return this.stream.write(s.__super__.text.call(this,f,g,b))},s.prototype.dtdAttList=function(f,g,b){return this.stream.write(s.__super__.dtdAttList.call(this,f,g,b))},s.prototype.dtdElement=function(f,g,b){return this.stream.write(s.__super__.dtdElement.call(this,f,g,b))},s.prototype.dtdEntity=function(f,g,b){return this.stream.write(s.__super__.dtdEntity.call(this,f,g,b))},s.prototype.dtdNotation=function(f,g,b){return this.stream.write(s.__super__.dtdNotation.call(this,f,g,b))},s})(h)}).call(BE)),lu.exports}var ay;function zE(){return ay||(ay=1,(function(){var u,l,h,o,d,c,s,f,g,b;b=Dn(),f=b.assign,g=b.isFunction,h=Vy(),o=Zy(),d=UE(),s=hc(),c=XE(),u=ae(),l=mu(),ln.create=function(D,A,N,M){var x,B;if(D==null)throw new Error("Root element needs a name.");return M=f({},A,N,M),x=new o(M),B=x.element(D),M.headless||(x.declaration(M),(M.pubID!=null||M.sysID!=null)&&x.dtd(M)),B},ln.begin=function(D,A,N){var M;return g(D)&&(M=[D,A],A=M[0],N=M[1],D={}),A?new d(D,A,N):new o(D)},ln.stringWriter=function(D){return new s(D)},ln.streamWriter=function(D,A){return new c(D,A)},ln.implementation=new h,ln.nodeType=u,ln.writerState=l}).call(ln)),ln}var ly;function qE(){return ly||(ly=1,(function(){var u,l,h,o,d,c={}.hasOwnProperty;u=zE(),l=Ws().defaults,o=function(s){return typeof s=="string"&&(s.indexOf("&")>=0||s.indexOf(">")>=0||s.indexOf("<")>=0)},d=function(s){return"<![CDATA["+h(s)+"]]>"},h=function(s){return s.replace("]]>","]]]]><![CDATA[>")},Sl.Builder=(function(){function s(f){var g,b,D;this.options={},b=l["0.2"];for(g in b)c.call(b,g)&&(D=b[g],this.options[g]=D);for(g in f)c.call(f,g)&&(D=f[g],this.options[g]=D)}return s.prototype.buildObject=function(f){var g,b,D,A,N;return g=this.options.attrkey,b=this.options.charkey,Object.keys(f).length===1&&this.options.rootName===l["0.2"].rootName?(N=Object.keys(f)[0],f=f[N]):N=this.options.rootName,D=(function(M){return function(x,B){var z,C,_,v,U,X;if(typeof B!="object")M.options.cdata&&o(B)?x.raw(d(B)):x.txt(B);else if(Array.isArray(B)){for(v in B)if(c.call(B,v)){C=B[v];for(U in C)_=C[U],x=D(x.ele(U),_).up()}}else for(U in B)if(c.call(B,U))if(C=B[U],U===g){if(typeof C=="object")for(z in C)X=C[z],x=x.att(z,X)}else if(U===b)M.options.cdata&&o(C)?x=x.raw(d(C)):x=x.txt(C);else if(Array.isArray(C))for(v in C)c.call(C,v)&&(_=C[v],typeof _=="string"?M.options.cdata&&o(_)?x=x.ele(U).raw(d(_)).up():x=x.ele(U,_).up():x=D(x.ele(U),_).up());else typeof C=="object"?x=D(x.ele(U),C).up():typeof C=="string"&&M.options.cdata&&o(C)?x=x.ele(U).raw(d(C)).up():(C==null&&(C=""),x=x.ele(U,C.toString()).up());return x}})(this),A=u.create(N,this.options.xmldec,this.options.doctype,{headless:this.options.headless,allowSurrogateChars:this.options.allowSurrogateChars}),D(A,f).end(this.options.renderOpts)},s})()}).call(Sl)),Sl}var Dl={},qs={};const jE={},HE=Object.freeze(Object.defineProperty({__proto__:null,default:jE},Symbol.toStringTag,{value:"Module"})),Zs=cg(HE);var js={},Al={exports:{}},Hs={},ea={},uy;function YE(){if(uy)return ea;uy=1,ea.byteLength=f,ea.toByteArray=b,ea.fromByteArray=N;for(var u=[],l=[],h=typeof Uint8Array<"u"?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,c=o.length;d<c;++d)u[d]=o[d],l[o.charCodeAt(d)]=d;l[45]=62,l[95]=63;function s(M){var x=M.length;if(x%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var B=M.indexOf("=");B===-1&&(B=x);var z=B===x?0:4-B%4;return[B,z]}function f(M){var x=s(M),B=x[0],z=x[1];return(B+z)*3/4-z}function g(M,x,B){return(x+B)*3/4-B}function b(M){var x,B=s(M),z=B[0],C=B[1],_=new h(g(M,z,C)),v=0,U=C>0?z-4:z,X;for(X=0;X<U;X+=4)x=l[M.charCodeAt(X)]<<18|l[M.charCodeAt(X+1)]<<12|l[M.charCodeAt(X+2)]<<6|l[M.charCodeAt(X+3)],_[v++]=x>>16&255,_[v++]=x>>8&255,_[v++]=x&255;return C===2&&(x=l[M.charCodeAt(X)]<<2|l[M.charCodeAt(X+1)]>>4,_[v++]=x&255),C===1&&(x=l[M.charCodeAt(X)]<<10|l[M.charCodeAt(X+1)]<<4|l[M.charCodeAt(X+2)]>>2,_[v++]=x>>8&255,_[v++]=x&255),_}function D(M){return u[M>>18&63]+u[M>>12&63]+u[M>>6&63]+u[M&63]}function A(M,x,B){for(var z,C=[],_=x;_<B;_+=3)z=(M[_]<<16&16711680)+(M[_+1]<<8&65280)+(M[_+2]&255),C.push(D(z));return C.join("")}function N(M){for(var x,B=M.length,z=B%3,C=[],_=16383,v=0,U=B-z;v<U;v+=_)C.push(A(M,v,v+_>U?U:v+_));return z===1?(x=M[B-1],C.push(u[x>>2]+u[x<<4&63]+"==")):z===2&&(x=(M[B-2]<<8)+M[B-1],C.push(u[x>>10]+u[x>>4&63]+u[x<<2&63]+"=")),C.join("")}return ea}var Ol={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */var oy;function VE(){return oy||(oy=1,Ol.read=function(u,l,h,o,d){var c,s,f=d*8-o-1,g=(1<<f)-1,b=g>>1,D=-7,A=h?d-1:0,N=h?-1:1,M=u[l+A];for(A+=N,c=M&(1<<-D)-1,M>>=-D,D+=f;D>0;c=c*256+u[l+A],A+=N,D-=8);for(s=c&(1<<-D)-1,c>>=-D,D+=o;D>0;s=s*256+u[l+A],A+=N,D-=8);if(c===0)c=1-b;else{if(c===g)return s?NaN:(M?-1:1)*(1/0);s=s+Math.pow(2,o),c=c-b}return(M?-1:1)*s*Math.pow(2,c-o)},Ol.write=function(u,l,h,o,d,c){var s,f,g,b=c*8-d-1,D=(1<<b)-1,A=D>>1,N=d===23?Math.pow(2,-24)-Math.pow(2,-77):0,M=o?0:c-1,x=o?1:-1,B=l<0||l===0&&1/l<0?1:0;for(l=Math.abs(l),isNaN(l)||l===1/0?(f=isNaN(l)?1:0,s=D):(s=Math.floor(Math.log(l)/Math.LN2),l*(g=Math.pow(2,-s))<1&&(s--,g*=2),s+A>=1?l+=N/g:l+=N*Math.pow(2,1-A),l*g>=2&&(s++,g/=2),s+A>=D?(f=0,s=D):s+A>=1?(f=(l*g-1)*Math.pow(2,d),s=s+A):(f=l*Math.pow(2,A-1)*Math.pow(2,d),s=0));d>=8;u[h+M]=f&255,M+=x,f/=256,d-=8);for(s=s<<d|f,b+=d;b>0;u[h+M]=s&255,M+=x,s/=256,b-=8);u[h+M-x]|=B*128}),Ol}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var sy;function GE(){return sy||(sy=1,(function(u){var l=YE(),h=VE(),o=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;u.Buffer=f,u.SlowBuffer=_,u.INSPECT_MAX_BYTES=50;var d=**********;u.kMaxLength=d,f.TYPED_ARRAY_SUPPORT=c(),!f.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function c(){try{var w=new Uint8Array(1),p={foo:function(){return 42}};return Object.setPrototypeOf(p,Uint8Array.prototype),Object.setPrototypeOf(w,p),w.foo()===42}catch{return!1}}Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}});function s(w){if(w>d)throw new RangeError('The value "'+w+'" is invalid for option "size"');var p=new Uint8Array(w);return Object.setPrototypeOf(p,f.prototype),p}function f(w,p,y){if(typeof w=="number"){if(typeof p=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return A(w)}return g(w,p,y)}f.poolSize=8192;function g(w,p,y){if(typeof w=="string")return N(w,p);if(ArrayBuffer.isView(w))return x(w);if(w==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof w);if(Lt(w,ArrayBuffer)||w&&Lt(w.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Lt(w,SharedArrayBuffer)||w&&Lt(w.buffer,SharedArrayBuffer)))return B(w,p,y);if(typeof w=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var R=w.valueOf&&w.valueOf();if(R!=null&&R!==w)return f.from(R,p,y);var V=z(w);if(V)return V;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof w[Symbol.toPrimitive]=="function")return f.from(w[Symbol.toPrimitive]("string"),p,y);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof w)}f.from=function(w,p,y){return g(w,p,y)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array);function b(w){if(typeof w!="number")throw new TypeError('"size" argument must be of type number');if(w<0)throw new RangeError('The value "'+w+'" is invalid for option "size"')}function D(w,p,y){return b(w),w<=0?s(w):p!==void 0?typeof y=="string"?s(w).fill(p,y):s(w).fill(p):s(w)}f.alloc=function(w,p,y){return D(w,p,y)};function A(w){return b(w),s(w<0?0:C(w)|0)}f.allocUnsafe=function(w){return A(w)},f.allocUnsafeSlow=function(w){return A(w)};function N(w,p){if((typeof p!="string"||p==="")&&(p="utf8"),!f.isEncoding(p))throw new TypeError("Unknown encoding: "+p);var y=v(w,p)|0,R=s(y),V=R.write(w,p);return V!==y&&(R=R.slice(0,V)),R}function M(w){for(var p=w.length<0?0:C(w.length)|0,y=s(p),R=0;R<p;R+=1)y[R]=w[R]&255;return y}function x(w){if(Lt(w,Uint8Array)){var p=new Uint8Array(w);return B(p.buffer,p.byteOffset,p.byteLength)}return M(w)}function B(w,p,y){if(p<0||w.byteLength<p)throw new RangeError('"offset" is outside of buffer bounds');if(w.byteLength<p+(y||0))throw new RangeError('"length" is outside of buffer bounds');var R;return p===void 0&&y===void 0?R=new Uint8Array(w):y===void 0?R=new Uint8Array(w,p):R=new Uint8Array(w,p,y),Object.setPrototypeOf(R,f.prototype),R}function z(w){if(f.isBuffer(w)){var p=C(w.length)|0,y=s(p);return y.length===0||w.copy(y,0,0,p),y}if(w.length!==void 0)return typeof w.length!="number"||Jt(w.length)?s(0):M(w);if(w.type==="Buffer"&&Array.isArray(w.data))return M(w.data)}function C(w){if(w>=d)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+d.toString(16)+" bytes");return w|0}function _(w){return+w!=w&&(w=0),f.alloc(+w)}f.isBuffer=function(p){return p!=null&&p._isBuffer===!0&&p!==f.prototype},f.compare=function(p,y){if(Lt(p,Uint8Array)&&(p=f.from(p,p.offset,p.byteLength)),Lt(y,Uint8Array)&&(y=f.from(y,y.offset,y.byteLength)),!f.isBuffer(p)||!f.isBuffer(y))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(p===y)return 0;for(var R=p.length,V=y.length,at=0,ut=Math.min(R,V);at<ut;++at)if(p[at]!==y[at]){R=p[at],V=y[at];break}return R<V?-1:V<R?1:0},f.isEncoding=function(p){switch(String(p).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(p,y){if(!Array.isArray(p))throw new TypeError('"list" argument must be an Array of Buffers');if(p.length===0)return f.alloc(0);var R;if(y===void 0)for(y=0,R=0;R<p.length;++R)y+=p[R].length;var V=f.allocUnsafe(y),at=0;for(R=0;R<p.length;++R){var ut=p[R];if(Lt(ut,Uint8Array))at+ut.length>V.length?f.from(ut).copy(V,at):Uint8Array.prototype.set.call(V,ut,at);else if(f.isBuffer(ut))ut.copy(V,at);else throw new TypeError('"list" argument must be an Array of Buffers');at+=ut.length}return V};function v(w,p){if(f.isBuffer(w))return w.length;if(ArrayBuffer.isView(w)||Lt(w,ArrayBuffer))return w.byteLength;if(typeof w!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof w);var y=w.length,R=arguments.length>2&&arguments[2]===!0;if(!R&&y===0)return 0;for(var V=!1;;)switch(p){case"ascii":case"latin1":case"binary":return y;case"utf8":case"utf-8":return T(w).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return y*2;case"hex":return y>>>1;case"base64":return xt(w).length;default:if(V)return R?-1:T(w).length;p=(""+p).toLowerCase(),V=!0}}f.byteLength=v;function U(w,p,y){var R=!1;if((p===void 0||p<0)&&(p=0),p>this.length||((y===void 0||y>this.length)&&(y=this.length),y<=0)||(y>>>=0,p>>>=0,y<=p))return"";for(w||(w="utf8");;)switch(w){case"hex":return Bt(this,p,y);case"utf8":case"utf-8":return bt(this,p,y);case"ascii":return rt(this,p,y);case"latin1":case"binary":return yt(this,p,y);case"base64":return Tt(this,p,y);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,p,y);default:if(R)throw new TypeError("Unknown encoding: "+w);w=(w+"").toLowerCase(),R=!0}}f.prototype._isBuffer=!0;function X(w,p,y){var R=w[p];w[p]=w[y],w[y]=R}f.prototype.swap16=function(){var p=this.length;if(p%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var y=0;y<p;y+=2)X(this,y,y+1);return this},f.prototype.swap32=function(){var p=this.length;if(p%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var y=0;y<p;y+=4)X(this,y,y+3),X(this,y+1,y+2);return this},f.prototype.swap64=function(){var p=this.length;if(p%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var y=0;y<p;y+=8)X(this,y,y+7),X(this,y+1,y+6),X(this,y+2,y+5),X(this,y+3,y+4);return this},f.prototype.toString=function(){var p=this.length;return p===0?"":arguments.length===0?bt(this,0,p):U.apply(this,arguments)},f.prototype.toLocaleString=f.prototype.toString,f.prototype.equals=function(p){if(!f.isBuffer(p))throw new TypeError("Argument must be a Buffer");return this===p?!0:f.compare(this,p)===0},f.prototype.inspect=function(){var p="",y=u.INSPECT_MAX_BYTES;return p=this.toString("hex",0,y).replace(/(.{2})/g,"$1 ").trim(),this.length>y&&(p+=" ... "),"<Buffer "+p+">"},o&&(f.prototype[o]=f.prototype.inspect),f.prototype.compare=function(p,y,R,V,at){if(Lt(p,Uint8Array)&&(p=f.from(p,p.offset,p.byteLength)),!f.isBuffer(p))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof p);if(y===void 0&&(y=0),R===void 0&&(R=p?p.length:0),V===void 0&&(V=0),at===void 0&&(at=this.length),y<0||R>p.length||V<0||at>this.length)throw new RangeError("out of range index");if(V>=at&&y>=R)return 0;if(V>=at)return-1;if(y>=R)return 1;if(y>>>=0,R>>>=0,V>>>=0,at>>>=0,this===p)return 0;for(var ut=at-V,Rt=R-y,qt=Math.min(ut,Rt),Qt=this.slice(V,at),kt=p.slice(y,R),Ct=0;Ct<qt;++Ct)if(Qt[Ct]!==kt[Ct]){ut=Qt[Ct],Rt=kt[Ct];break}return ut<Rt?-1:Rt<ut?1:0};function W(w,p,y,R,V){if(w.length===0)return-1;if(typeof y=="string"?(R=y,y=0):y>**********?y=**********:y<-2147483648&&(y=-2147483648),y=+y,Jt(y)&&(y=V?0:w.length-1),y<0&&(y=w.length+y),y>=w.length){if(V)return-1;y=w.length-1}else if(y<0)if(V)y=0;else return-1;if(typeof p=="string"&&(p=f.from(p,R)),f.isBuffer(p))return p.length===0?-1:lt(w,p,y,R,V);if(typeof p=="number")return p=p&255,typeof Uint8Array.prototype.indexOf=="function"?V?Uint8Array.prototype.indexOf.call(w,p,y):Uint8Array.prototype.lastIndexOf.call(w,p,y):lt(w,[p],y,R,V);throw new TypeError("val must be string, number or Buffer")}function lt(w,p,y,R,V){var at=1,ut=w.length,Rt=p.length;if(R!==void 0&&(R=String(R).toLowerCase(),R==="ucs2"||R==="ucs-2"||R==="utf16le"||R==="utf-16le")){if(w.length<2||p.length<2)return-1;at=2,ut/=2,Rt/=2,y/=2}function qt(me,ua){return at===1?me[ua]:me.readUInt16BE(ua*at)}var Qt;if(V){var kt=-1;for(Qt=y;Qt<ut;Qt++)if(qt(w,Qt)===qt(p,kt===-1?0:Qt-kt)){if(kt===-1&&(kt=Qt),Qt-kt+1===Rt)return kt*at}else kt!==-1&&(Qt-=Qt-kt),kt=-1}else for(y+Rt>ut&&(y=ut-Rt),Qt=y;Qt>=0;Qt--){for(var Ct=!0,_e=0;_e<Rt;_e++)if(qt(w,Qt+_e)!==qt(p,_e)){Ct=!1;break}if(Ct)return Qt}return-1}f.prototype.includes=function(p,y,R){return this.indexOf(p,y,R)!==-1},f.prototype.indexOf=function(p,y,R){return W(this,p,y,R,!0)},f.prototype.lastIndexOf=function(p,y,R){return W(this,p,y,R,!1)};function $(w,p,y,R){y=Number(y)||0;var V=w.length-y;R?(R=Number(R),R>V&&(R=V)):R=V;var at=p.length;R>at/2&&(R=at/2);for(var ut=0;ut<R;++ut){var Rt=parseInt(p.substr(ut*2,2),16);if(Jt(Rt))return ut;w[y+ut]=Rt}return ut}function E(w,p,y,R){return Yt(T(p,w.length-y),w,y,R)}function J(w,p,y,R){return Yt(tt(p),w,y,R)}function it(w,p,y,R){return Yt(xt(p),w,y,R)}function ot(w,p,y,R){return Yt(Y(p,w.length-y),w,y,R)}f.prototype.write=function(p,y,R,V){if(y===void 0)V="utf8",R=this.length,y=0;else if(R===void 0&&typeof y=="string")V=y,R=this.length,y=0;else if(isFinite(y))y=y>>>0,isFinite(R)?(R=R>>>0,V===void 0&&(V="utf8")):(V=R,R=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var at=this.length-y;if((R===void 0||R>at)&&(R=at),p.length>0&&(R<0||y<0)||y>this.length)throw new RangeError("Attempt to write outside buffer bounds");V||(V="utf8");for(var ut=!1;;)switch(V){case"hex":return $(this,p,y,R);case"utf8":case"utf-8":return E(this,p,y,R);case"ascii":case"latin1":case"binary":return J(this,p,y,R);case"base64":return it(this,p,y,R);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ot(this,p,y,R);default:if(ut)throw new TypeError("Unknown encoding: "+V);V=(""+V).toLowerCase(),ut=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Tt(w,p,y){return p===0&&y===w.length?l.fromByteArray(w):l.fromByteArray(w.slice(p,y))}function bt(w,p,y){y=Math.min(w.length,y);for(var R=[],V=p;V<y;){var at=w[V],ut=null,Rt=at>239?4:at>223?3:at>191?2:1;if(V+Rt<=y){var qt,Qt,kt,Ct;switch(Rt){case 1:at<128&&(ut=at);break;case 2:qt=w[V+1],(qt&192)===128&&(Ct=(at&31)<<6|qt&63,Ct>127&&(ut=Ct));break;case 3:qt=w[V+1],Qt=w[V+2],(qt&192)===128&&(Qt&192)===128&&(Ct=(at&15)<<12|(qt&63)<<6|Qt&63,Ct>2047&&(Ct<55296||Ct>57343)&&(ut=Ct));break;case 4:qt=w[V+1],Qt=w[V+2],kt=w[V+3],(qt&192)===128&&(Qt&192)===128&&(kt&192)===128&&(Ct=(at&15)<<18|(qt&63)<<12|(Qt&63)<<6|kt&63,Ct>65535&&Ct<1114112&&(ut=Ct))}}ut===null?(ut=65533,Rt=1):ut>65535&&(ut-=65536,R.push(ut>>>10&1023|55296),ut=56320|ut&1023),R.push(ut),V+=Rt}return I(R)}var St=4096;function I(w){var p=w.length;if(p<=St)return String.fromCharCode.apply(String,w);for(var y="",R=0;R<p;)y+=String.fromCharCode.apply(String,w.slice(R,R+=St));return y}function rt(w,p,y){var R="";y=Math.min(w.length,y);for(var V=p;V<y;++V)R+=String.fromCharCode(w[V]&127);return R}function yt(w,p,y){var R="";y=Math.min(w.length,y);for(var V=p;V<y;++V)R+=String.fromCharCode(w[V]);return R}function Bt(w,p,y){var R=w.length;(!p||p<0)&&(p=0),(!y||y<0||y>R)&&(y=R);for(var V="",at=p;at<y;++at)V+=oe[w[at]];return V}function q(w,p,y){for(var R=w.slice(p,y),V="",at=0;at<R.length-1;at+=2)V+=String.fromCharCode(R[at]+R[at+1]*256);return V}f.prototype.slice=function(p,y){var R=this.length;p=~~p,y=y===void 0?R:~~y,p<0?(p+=R,p<0&&(p=0)):p>R&&(p=R),y<0?(y+=R,y<0&&(y=0)):y>R&&(y=R),y<p&&(y=p);var V=this.subarray(p,y);return Object.setPrototypeOf(V,f.prototype),V};function P(w,p,y){if(w%1!==0||w<0)throw new RangeError("offset is not uint");if(w+p>y)throw new RangeError("Trying to access beyond buffer length")}f.prototype.readUintLE=f.prototype.readUIntLE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p],at=1,ut=0;++ut<y&&(at*=256);)V+=this[p+ut]*at;return V},f.prototype.readUintBE=f.prototype.readUIntBE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p+--y],at=1;y>0&&(at*=256);)V+=this[p+--y]*at;return V},f.prototype.readUint8=f.prototype.readUInt8=function(p,y){return p=p>>>0,y||P(p,1,this.length),this[p]},f.prototype.readUint16LE=f.prototype.readUInt16LE=function(p,y){return p=p>>>0,y||P(p,2,this.length),this[p]|this[p+1]<<8},f.prototype.readUint16BE=f.prototype.readUInt16BE=function(p,y){return p=p>>>0,y||P(p,2,this.length),this[p]<<8|this[p+1]},f.prototype.readUint32LE=f.prototype.readUInt32LE=function(p,y){return p=p>>>0,y||P(p,4,this.length),(this[p]|this[p+1]<<8|this[p+2]<<16)+this[p+3]*16777216},f.prototype.readUint32BE=f.prototype.readUInt32BE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]*16777216+(this[p+1]<<16|this[p+2]<<8|this[p+3])},f.prototype.readIntLE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p],at=1,ut=0;++ut<y&&(at*=256);)V+=this[p+ut]*at;return at*=128,V>=at&&(V-=Math.pow(2,8*y)),V},f.prototype.readIntBE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=y,at=1,ut=this[p+--V];V>0&&(at*=256);)ut+=this[p+--V]*at;return at*=128,ut>=at&&(ut-=Math.pow(2,8*y)),ut},f.prototype.readInt8=function(p,y){return p=p>>>0,y||P(p,1,this.length),this[p]&128?(255-this[p]+1)*-1:this[p]},f.prototype.readInt16LE=function(p,y){p=p>>>0,y||P(p,2,this.length);var R=this[p]|this[p+1]<<8;return R&32768?R|4294901760:R},f.prototype.readInt16BE=function(p,y){p=p>>>0,y||P(p,2,this.length);var R=this[p+1]|this[p]<<8;return R&32768?R|4294901760:R},f.prototype.readInt32LE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]|this[p+1]<<8|this[p+2]<<16|this[p+3]<<24},f.prototype.readInt32BE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]<<24|this[p+1]<<16|this[p+2]<<8|this[p+3]},f.prototype.readFloatLE=function(p,y){return p=p>>>0,y||P(p,4,this.length),h.read(this,p,!0,23,4)},f.prototype.readFloatBE=function(p,y){return p=p>>>0,y||P(p,4,this.length),h.read(this,p,!1,23,4)},f.prototype.readDoubleLE=function(p,y){return p=p>>>0,y||P(p,8,this.length),h.read(this,p,!0,52,8)},f.prototype.readDoubleBE=function(p,y){return p=p>>>0,y||P(p,8,this.length),h.read(this,p,!1,52,8)};function st(w,p,y,R,V,at){if(!f.isBuffer(w))throw new TypeError('"buffer" argument must be a Buffer instance');if(p>V||p<at)throw new RangeError('"value" argument is out of bounds');if(y+R>w.length)throw new RangeError("Index out of range")}f.prototype.writeUintLE=f.prototype.writeUIntLE=function(p,y,R,V){if(p=+p,y=y>>>0,R=R>>>0,!V){var at=Math.pow(2,8*R)-1;st(this,p,y,R,at,0)}var ut=1,Rt=0;for(this[y]=p&255;++Rt<R&&(ut*=256);)this[y+Rt]=p/ut&255;return y+R},f.prototype.writeUintBE=f.prototype.writeUIntBE=function(p,y,R,V){if(p=+p,y=y>>>0,R=R>>>0,!V){var at=Math.pow(2,8*R)-1;st(this,p,y,R,at,0)}var ut=R-1,Rt=1;for(this[y+ut]=p&255;--ut>=0&&(Rt*=256);)this[y+ut]=p/Rt&255;return y+R},f.prototype.writeUint8=f.prototype.writeUInt8=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,1,255,0),this[y]=p&255,y+1},f.prototype.writeUint16LE=f.prototype.writeUInt16LE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,2,65535,0),this[y]=p&255,this[y+1]=p>>>8,y+2},f.prototype.writeUint16BE=f.prototype.writeUInt16BE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,2,65535,0),this[y]=p>>>8,this[y+1]=p&255,y+2},f.prototype.writeUint32LE=f.prototype.writeUInt32LE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,4,4294967295,0),this[y+3]=p>>>24,this[y+2]=p>>>16,this[y+1]=p>>>8,this[y]=p&255,y+4},f.prototype.writeUint32BE=f.prototype.writeUInt32BE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,4,4294967295,0),this[y]=p>>>24,this[y+1]=p>>>16,this[y+2]=p>>>8,this[y+3]=p&255,y+4},f.prototype.writeIntLE=function(p,y,R,V){if(p=+p,y=y>>>0,!V){var at=Math.pow(2,8*R-1);st(this,p,y,R,at-1,-at)}var ut=0,Rt=1,qt=0;for(this[y]=p&255;++ut<R&&(Rt*=256);)p<0&&qt===0&&this[y+ut-1]!==0&&(qt=1),this[y+ut]=(p/Rt>>0)-qt&255;return y+R},f.prototype.writeIntBE=function(p,y,R,V){if(p=+p,y=y>>>0,!V){var at=Math.pow(2,8*R-1);st(this,p,y,R,at-1,-at)}var ut=R-1,Rt=1,qt=0;for(this[y+ut]=p&255;--ut>=0&&(Rt*=256);)p<0&&qt===0&&this[y+ut+1]!==0&&(qt=1),this[y+ut]=(p/Rt>>0)-qt&255;return y+R},f.prototype.writeInt8=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,1,127,-128),p<0&&(p=255+p+1),this[y]=p&255,y+1},f.prototype.writeInt16LE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,2,32767,-32768),this[y]=p&255,this[y+1]=p>>>8,y+2},f.prototype.writeInt16BE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,2,32767,-32768),this[y]=p>>>8,this[y+1]=p&255,y+2},f.prototype.writeInt32LE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,4,**********,-2147483648),this[y]=p&255,this[y+1]=p>>>8,this[y+2]=p>>>16,this[y+3]=p>>>24,y+4},f.prototype.writeInt32BE=function(p,y,R){return p=+p,y=y>>>0,R||st(this,p,y,4,**********,-2147483648),p<0&&(p=4294967295+p+1),this[y]=p>>>24,this[y+1]=p>>>16,this[y+2]=p>>>8,this[y+3]=p&255,y+4};function ct(w,p,y,R,V,at){if(y+R>w.length)throw new RangeError("Index out of range");if(y<0)throw new RangeError("Index out of range")}function pt(w,p,y,R,V){return p=+p,y=y>>>0,V||ct(w,p,y,4),h.write(w,p,y,R,23,4),y+4}f.prototype.writeFloatLE=function(p,y,R){return pt(this,p,y,!0,R)},f.prototype.writeFloatBE=function(p,y,R){return pt(this,p,y,!1,R)};function wt(w,p,y,R,V){return p=+p,y=y>>>0,V||ct(w,p,y,8),h.write(w,p,y,R,52,8),y+8}f.prototype.writeDoubleLE=function(p,y,R){return wt(this,p,y,!0,R)},f.prototype.writeDoubleBE=function(p,y,R){return wt(this,p,y,!1,R)},f.prototype.copy=function(p,y,R,V){if(!f.isBuffer(p))throw new TypeError("argument should be a Buffer");if(R||(R=0),!V&&V!==0&&(V=this.length),y>=p.length&&(y=p.length),y||(y=0),V>0&&V<R&&(V=R),V===R||p.length===0||this.length===0)return 0;if(y<0)throw new RangeError("targetStart out of bounds");if(R<0||R>=this.length)throw new RangeError("Index out of range");if(V<0)throw new RangeError("sourceEnd out of bounds");V>this.length&&(V=this.length),p.length-y<V-R&&(V=p.length-y+R);var at=V-R;return this===p&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(y,R,V):Uint8Array.prototype.set.call(p,this.subarray(R,V),y),at},f.prototype.fill=function(p,y,R,V){if(typeof p=="string"){if(typeof y=="string"?(V=y,y=0,R=this.length):typeof R=="string"&&(V=R,R=this.length),V!==void 0&&typeof V!="string")throw new TypeError("encoding must be a string");if(typeof V=="string"&&!f.isEncoding(V))throw new TypeError("Unknown encoding: "+V);if(p.length===1){var at=p.charCodeAt(0);(V==="utf8"&&at<128||V==="latin1")&&(p=at)}}else typeof p=="number"?p=p&255:typeof p=="boolean"&&(p=Number(p));if(y<0||this.length<y||this.length<R)throw new RangeError("Out of range index");if(R<=y)return this;y=y>>>0,R=R===void 0?this.length:R>>>0,p||(p=0);var ut;if(typeof p=="number")for(ut=y;ut<R;++ut)this[ut]=p;else{var Rt=f.isBuffer(p)?p:f.from(p,V),qt=Rt.length;if(qt===0)throw new TypeError('The value "'+p+'" is invalid for argument "value"');for(ut=0;ut<R-y;++ut)this[ut+y]=Rt[ut%qt]}return this};var Et=/[^+/0-9A-Za-z-_]/g;function O(w){if(w=w.split("=")[0],w=w.trim().replace(Et,""),w.length<2)return"";for(;w.length%4!==0;)w=w+"=";return w}function T(w,p){p=p||1/0;for(var y,R=w.length,V=null,at=[],ut=0;ut<R;++ut){if(y=w.charCodeAt(ut),y>55295&&y<57344){if(!V){if(y>56319){(p-=3)>-1&&at.push(239,191,189);continue}else if(ut+1===R){(p-=3)>-1&&at.push(239,191,189);continue}V=y;continue}if(y<56320){(p-=3)>-1&&at.push(239,191,189),V=y;continue}y=(V-55296<<10|y-56320)+65536}else V&&(p-=3)>-1&&at.push(239,191,189);if(V=null,y<128){if((p-=1)<0)break;at.push(y)}else if(y<2048){if((p-=2)<0)break;at.push(y>>6|192,y&63|128)}else if(y<65536){if((p-=3)<0)break;at.push(y>>12|224,y>>6&63|128,y&63|128)}else if(y<1114112){if((p-=4)<0)break;at.push(y>>18|240,y>>12&63|128,y>>6&63|128,y&63|128)}else throw new Error("Invalid code point")}return at}function tt(w){for(var p=[],y=0;y<w.length;++y)p.push(w.charCodeAt(y)&255);return p}function Y(w,p){for(var y,R,V,at=[],ut=0;ut<w.length&&!((p-=2)<0);++ut)y=w.charCodeAt(ut),R=y>>8,V=y%256,at.push(V),at.push(R);return at}function xt(w){return l.toByteArray(O(w))}function Yt(w,p,y,R){for(var V=0;V<R&&!(V+y>=p.length||V>=w.length);++V)p[V+y]=w[V];return V}function Lt(w,p){return w instanceof p||w!=null&&w.constructor!=null&&w.constructor.name!=null&&w.constructor.name===p.name}function Jt(w){return w!==w}var oe=(function(){for(var w="0123456789abcdef",p=new Array(256),y=0;y<16;++y)for(var R=y*16,V=0;V<16;++V)p[R+V]=w[y]+w[V];return p})()})(Hs)),Hs}/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var cy;function FE(){return cy||(cy=1,(function(u,l){var h=GE(),o=h.Buffer;function d(s,f){for(var g in s)f[g]=s[g]}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?u.exports=h:(d(h,l),l.Buffer=c);function c(s,f,g){return o(s,f,g)}c.prototype=Object.create(o.prototype),d(o,c),c.from=function(s,f,g){if(typeof s=="number")throw new TypeError("Argument must not be a number");return o(s,f,g)},c.alloc=function(s,f,g){if(typeof s!="number")throw new TypeError("Argument must be a number");var b=o(s);return f!==void 0?typeof g=="string"?b.fill(f,g):b.fill(f):b.fill(0),b},c.allocUnsafe=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return o(s)},c.allocUnsafeSlow=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return h.SlowBuffer(s)}})(Al,Al.exports)),Al.exports}var fy;function IE(){if(fy)return js;fy=1;var u=FE().Buffer,l=u.isEncoding||function(C){switch(C=""+C,C&&C.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function h(C){if(!C)return"utf8";for(var _;;)switch(C){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return C;default:if(_)return;C=(""+C).toLowerCase(),_=!0}}function o(C){var _=h(C);if(typeof _!="string"&&(u.isEncoding===l||!l(C)))throw new Error("Unknown encoding: "+C);return _||C}js.StringDecoder=d;function d(C){this.encoding=o(C);var _;switch(this.encoding){case"utf16le":this.text=A,this.end=N,_=4;break;case"utf8":this.fillLast=g,_=4;break;case"base64":this.text=M,this.end=x,_=3;break;default:this.write=B,this.end=z;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=u.allocUnsafe(_)}d.prototype.write=function(C){if(C.length===0)return"";var _,v;if(this.lastNeed){if(_=this.fillLast(C),_===void 0)return"";v=this.lastNeed,this.lastNeed=0}else v=0;return v<C.length?_?_+this.text(C,v):this.text(C,v):_||""},d.prototype.end=D,d.prototype.text=b,d.prototype.fillLast=function(C){if(this.lastNeed<=C.length)return C.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);C.copy(this.lastChar,this.lastTotal-this.lastNeed,0,C.length),this.lastNeed-=C.length};function c(C){return C<=127?0:C>>5===6?2:C>>4===14?3:C>>3===30?4:C>>6===2?-1:-2}function s(C,_,v){var U=_.length-1;if(U<v)return 0;var X=c(_[U]);return X>=0?(X>0&&(C.lastNeed=X-1),X):--U<v||X===-2?0:(X=c(_[U]),X>=0?(X>0&&(C.lastNeed=X-2),X):--U<v||X===-2?0:(X=c(_[U]),X>=0?(X>0&&(X===2?X=0:C.lastNeed=X-3),X):0))}function f(C,_,v){if((_[0]&192)!==128)return C.lastNeed=0,"�";if(C.lastNeed>1&&_.length>1){if((_[1]&192)!==128)return C.lastNeed=1,"�";if(C.lastNeed>2&&_.length>2&&(_[2]&192)!==128)return C.lastNeed=2,"�"}}function g(C){var _=this.lastTotal-this.lastNeed,v=f(this,C);if(v!==void 0)return v;if(this.lastNeed<=C.length)return C.copy(this.lastChar,_,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);C.copy(this.lastChar,_,0,C.length),this.lastNeed-=C.length}function b(C,_){var v=s(this,C,_);if(!this.lastNeed)return C.toString("utf8",_);this.lastTotal=v;var U=C.length-(v-this.lastNeed);return C.copy(this.lastChar,0,U),C.toString("utf8",_,U)}function D(C){var _=C&&C.length?this.write(C):"";return this.lastNeed?_+"�":_}function A(C,_){if((C.length-_)%2===0){var v=C.toString("utf16le",_);if(v){var U=v.charCodeAt(v.length-1);if(U>=55296&&U<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=C[C.length-2],this.lastChar[1]=C[C.length-1],v.slice(0,-1)}return v}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=C[C.length-1],C.toString("utf16le",_,C.length-1)}function N(C){var _=C&&C.length?this.write(C):"";if(this.lastNeed){var v=this.lastTotal-this.lastNeed;return _+this.lastChar.toString("utf16le",0,v)}return _}function M(C,_){var v=(C.length-_)%3;return v===0?C.toString("base64",_):(this.lastNeed=3-v,this.lastTotal=3,v===1?this.lastChar[0]=C[C.length-1]:(this.lastChar[0]=C[C.length-2],this.lastChar[1]=C[C.length-1]),C.toString("base64",_,C.length-v))}function x(C){var _=C&&C.length?this.write(C):"";return this.lastNeed?_+this.lastChar.toString("base64",0,3-this.lastNeed):_}function B(C){return C.toString(this.encoding)}function z(C){return C&&C.length?this.write(C):""}return js}var hy;function QE(){return hy||(hy=1,(function(u){(function(l){l.parser=function(O,T){return new o(O,T)},l.SAXParser=o,l.SAXStream=D,l.createStream=b,l.MAX_BUFFER_LENGTH=64*1024;var h=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];l.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function o(O,T){if(!(this instanceof o))return new o(O,T);var tt=this;c(tt),tt.q=tt.c="",tt.bufferCheckPosition=l.MAX_BUFFER_LENGTH,tt.opt=T||{},tt.opt.lowercase=tt.opt.lowercase||tt.opt.lowercasetags,tt.looseCase=tt.opt.lowercase?"toLowerCase":"toUpperCase",tt.tags=[],tt.closed=tt.closedRoot=tt.sawRoot=!1,tt.tag=tt.error=null,tt.strict=!!O,tt.noscript=!!(O||tt.opt.noscript),tt.state=E.BEGIN,tt.strictEntities=tt.opt.strictEntities,tt.ENTITIES=tt.strictEntities?Object.create(l.XML_ENTITIES):Object.create(l.ENTITIES),tt.attribList=[],tt.opt.xmlns&&(tt.ns=Object.create(B)),tt.opt.unquotedAttributeValues===void 0&&(tt.opt.unquotedAttributeValues=!O),tt.trackPosition=tt.opt.position!==!1,tt.trackPosition&&(tt.position=tt.line=tt.column=0),it(tt,"onready")}Object.create||(Object.create=function(O){function T(){}T.prototype=O;var tt=new T;return tt}),Object.keys||(Object.keys=function(O){var T=[];for(var tt in O)O.hasOwnProperty(tt)&&T.push(tt);return T});function d(O){for(var T=Math.max(l.MAX_BUFFER_LENGTH,10),tt=0,Y=0,xt=h.length;Y<xt;Y++){var Yt=O[h[Y]].length;if(Yt>T)switch(h[Y]){case"textNode":Tt(O);break;case"cdata":ot(O,"oncdata",O.cdata),O.cdata="";break;case"script":ot(O,"onscript",O.script),O.script="";break;default:St(O,"Max buffer length exceeded: "+h[Y])}tt=Math.max(tt,Yt)}var Lt=l.MAX_BUFFER_LENGTH-tt;O.bufferCheckPosition=Lt+O.position}function c(O){for(var T=0,tt=h.length;T<tt;T++)O[h[T]]=""}function s(O){Tt(O),O.cdata!==""&&(ot(O,"oncdata",O.cdata),O.cdata=""),O.script!==""&&(ot(O,"onscript",O.script),O.script="")}o.prototype={end:function(){I(this)},write:Et,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){s(this)}};var f;try{f=Zs.Stream}catch{f=function(){}}f||(f=function(){});var g=l.EVENTS.filter(function(O){return O!=="error"&&O!=="end"});function b(O,T){return new D(O,T)}function D(O,T){if(!(this instanceof D))return new D(O,T);f.apply(this),this._parser=new o(O,T),this.writable=!0,this.readable=!0;var tt=this;this._parser.onend=function(){tt.emit("end")},this._parser.onerror=function(Y){tt.emit("error",Y),tt._parser.error=null},this._decoder=null,g.forEach(function(Y){Object.defineProperty(tt,"on"+Y,{get:function(){return tt._parser["on"+Y]},set:function(xt){if(!xt)return tt.removeAllListeners(Y),tt._parser["on"+Y]=xt,xt;tt.on(Y,xt)},enumerable:!0,configurable:!1})})}D.prototype=Object.create(f.prototype,{constructor:{value:D}}),D.prototype.write=function(O){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(O)){if(!this._decoder){var T=IE().StringDecoder;this._decoder=new T("utf8")}O=this._decoder.write(O)}return this._parser.write(O.toString()),this.emit("data",O),!0},D.prototype.end=function(O){return O&&O.length&&this.write(O),this._parser.end(),!0},D.prototype.on=function(O,T){var tt=this;return!tt._parser["on"+O]&&g.indexOf(O)!==-1&&(tt._parser["on"+O]=function(){var Y=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);Y.splice(0,0,O),tt.emit.apply(tt,Y)}),f.prototype.on.call(tt,O,T)};var A="[CDATA[",N="DOCTYPE",M="http://www.w3.org/XML/1998/namespace",x="http://www.w3.org/2000/xmlns/",B={xml:M,xmlns:x},z=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,C=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,_=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,v=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function U(O){return O===" "||O===`
`||O==="\r"||O==="	"}function X(O){return O==='"'||O==="'"}function W(O){return O===">"||U(O)}function lt(O,T){return O.test(T)}function $(O,T){return!lt(O,T)}var E=0;l.STATE={BEGIN:E++,BEGIN_WHITESPACE:E++,TEXT:E++,TEXT_ENTITY:E++,OPEN_WAKA:E++,SGML_DECL:E++,SGML_DECL_QUOTED:E++,DOCTYPE:E++,DOCTYPE_QUOTED:E++,DOCTYPE_DTD:E++,DOCTYPE_DTD_QUOTED:E++,COMMENT_STARTING:E++,COMMENT:E++,COMMENT_ENDING:E++,COMMENT_ENDED:E++,CDATA:E++,CDATA_ENDING:E++,CDATA_ENDING_2:E++,PROC_INST:E++,PROC_INST_BODY:E++,PROC_INST_ENDING:E++,OPEN_TAG:E++,OPEN_TAG_SLASH:E++,ATTRIB:E++,ATTRIB_NAME:E++,ATTRIB_NAME_SAW_WHITE:E++,ATTRIB_VALUE:E++,ATTRIB_VALUE_QUOTED:E++,ATTRIB_VALUE_CLOSED:E++,ATTRIB_VALUE_UNQUOTED:E++,ATTRIB_VALUE_ENTITY_Q:E++,ATTRIB_VALUE_ENTITY_U:E++,CLOSE_TAG:E++,CLOSE_TAG_SAW_WHITE:E++,SCRIPT:E++,SCRIPT_ENDING:E++},l.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},l.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(l.ENTITIES).forEach(function(O){var T=l.ENTITIES[O],tt=typeof T=="number"?String.fromCharCode(T):T;l.ENTITIES[O]=tt});for(var J in l.STATE)l.STATE[l.STATE[J]]=J;E=l.STATE;function it(O,T,tt){O[T]&&O[T](tt)}function ot(O,T,tt){O.textNode&&Tt(O),it(O,T,tt)}function Tt(O){O.textNode=bt(O.opt,O.textNode),O.textNode&&it(O,"ontext",O.textNode),O.textNode=""}function bt(O,T){return O.trim&&(T=T.trim()),O.normalize&&(T=T.replace(/\s+/g," ")),T}function St(O,T){return Tt(O),O.trackPosition&&(T+=`
Line: `+O.line+`
Column: `+O.column+`
Char: `+O.c),T=new Error(T),O.error=T,it(O,"onerror",T),O}function I(O){return O.sawRoot&&!O.closedRoot&&rt(O,"Unclosed root tag"),O.state!==E.BEGIN&&O.state!==E.BEGIN_WHITESPACE&&O.state!==E.TEXT&&St(O,"Unexpected end"),Tt(O),O.c="",O.closed=!0,it(O,"onend"),o.call(O,O.strict,O.opt),O}function rt(O,T){if(typeof O!="object"||!(O instanceof o))throw new Error("bad call to strictFail");O.strict&&St(O,T)}function yt(O){O.strict||(O.tagName=O.tagName[O.looseCase]());var T=O.tags[O.tags.length-1]||O,tt=O.tag={name:O.tagName,attributes:{}};O.opt.xmlns&&(tt.ns=T.ns),O.attribList.length=0,ot(O,"onopentagstart",tt)}function Bt(O,T){var tt=O.indexOf(":"),Y=tt<0?["",O]:O.split(":"),xt=Y[0],Yt=Y[1];return T&&O==="xmlns"&&(xt="xmlns",Yt=""),{prefix:xt,local:Yt}}function q(O){if(O.strict||(O.attribName=O.attribName[O.looseCase]()),O.attribList.indexOf(O.attribName)!==-1||O.tag.attributes.hasOwnProperty(O.attribName)){O.attribName=O.attribValue="";return}if(O.opt.xmlns){var T=Bt(O.attribName,!0),tt=T.prefix,Y=T.local;if(tt==="xmlns")if(Y==="xml"&&O.attribValue!==M)rt(O,"xml: prefix must be bound to "+M+`
Actual: `+O.attribValue);else if(Y==="xmlns"&&O.attribValue!==x)rt(O,"xmlns: prefix must be bound to "+x+`
Actual: `+O.attribValue);else{var xt=O.tag,Yt=O.tags[O.tags.length-1]||O;xt.ns===Yt.ns&&(xt.ns=Object.create(Yt.ns)),xt.ns[Y]=O.attribValue}O.attribList.push([O.attribName,O.attribValue])}else O.tag.attributes[O.attribName]=O.attribValue,ot(O,"onattribute",{name:O.attribName,value:O.attribValue});O.attribName=O.attribValue=""}function P(O,T){if(O.opt.xmlns){var tt=O.tag,Y=Bt(O.tagName);tt.prefix=Y.prefix,tt.local=Y.local,tt.uri=tt.ns[Y.prefix]||"",tt.prefix&&!tt.uri&&(rt(O,"Unbound namespace prefix: "+JSON.stringify(O.tagName)),tt.uri=Y.prefix);var xt=O.tags[O.tags.length-1]||O;tt.ns&&xt.ns!==tt.ns&&Object.keys(tt.ns).forEach(function(ut){ot(O,"onopennamespace",{prefix:ut,uri:tt.ns[ut]})});for(var Yt=0,Lt=O.attribList.length;Yt<Lt;Yt++){var Jt=O.attribList[Yt],oe=Jt[0],w=Jt[1],p=Bt(oe,!0),y=p.prefix,R=p.local,V=y===""?"":tt.ns[y]||"",at={name:oe,value:w,prefix:y,local:R,uri:V};y&&y!=="xmlns"&&!V&&(rt(O,"Unbound namespace prefix: "+JSON.stringify(y)),at.uri=y),O.tag.attributes[oe]=at,ot(O,"onattribute",at)}O.attribList.length=0}O.tag.isSelfClosing=!!T,O.sawRoot=!0,O.tags.push(O.tag),ot(O,"onopentag",O.tag),T||(!O.noscript&&O.tagName.toLowerCase()==="script"?O.state=E.SCRIPT:O.state=E.TEXT,O.tag=null,O.tagName=""),O.attribName=O.attribValue="",O.attribList.length=0}function st(O){if(!O.tagName){rt(O,"Weird empty close tag."),O.textNode+="</>",O.state=E.TEXT;return}if(O.script){if(O.tagName!=="script"){O.script+="</"+O.tagName+">",O.tagName="",O.state=E.SCRIPT;return}ot(O,"onscript",O.script),O.script=""}var T=O.tags.length,tt=O.tagName;O.strict||(tt=tt[O.looseCase]());for(var Y=tt;T--;){var xt=O.tags[T];if(xt.name!==Y)rt(O,"Unexpected close tag");else break}if(T<0){rt(O,"Unmatched closing tag: "+O.tagName),O.textNode+="</"+O.tagName+">",O.state=E.TEXT;return}O.tagName=tt;for(var Yt=O.tags.length;Yt-- >T;){var Lt=O.tag=O.tags.pop();O.tagName=O.tag.name,ot(O,"onclosetag",O.tagName);var Jt={};for(var oe in Lt.ns)Jt[oe]=Lt.ns[oe];var w=O.tags[O.tags.length-1]||O;O.opt.xmlns&&Lt.ns!==w.ns&&Object.keys(Lt.ns).forEach(function(p){var y=Lt.ns[p];ot(O,"onclosenamespace",{prefix:p,uri:y})})}T===0&&(O.closedRoot=!0),O.tagName=O.attribValue=O.attribName="",O.attribList.length=0,O.state=E.TEXT}function ct(O){var T=O.entity,tt=T.toLowerCase(),Y,xt="";return O.ENTITIES[T]?O.ENTITIES[T]:O.ENTITIES[tt]?O.ENTITIES[tt]:(T=tt,T.charAt(0)==="#"&&(T.charAt(1)==="x"?(T=T.slice(2),Y=parseInt(T,16),xt=Y.toString(16)):(T=T.slice(1),Y=parseInt(T,10),xt=Y.toString(10))),T=T.replace(/^0+/,""),isNaN(Y)||xt.toLowerCase()!==T?(rt(O,"Invalid character entity"),"&"+O.entity+";"):String.fromCodePoint(Y))}function pt(O,T){T==="<"?(O.state=E.OPEN_WAKA,O.startTagPosition=O.position):U(T)||(rt(O,"Non-whitespace before first tag."),O.textNode=T,O.state=E.TEXT)}function wt(O,T){var tt="";return T<O.length&&(tt=O.charAt(T)),tt}function Et(O){var T=this;if(this.error)throw this.error;if(T.closed)return St(T,"Cannot write after close. Assign an onready handler.");if(O===null)return I(T);typeof O=="object"&&(O=O.toString());for(var tt=0,Y="";Y=wt(O,tt++),T.c=Y,!!Y;)switch(T.trackPosition&&(T.position++,Y===`
`?(T.line++,T.column=0):T.column++),T.state){case E.BEGIN:if(T.state=E.BEGIN_WHITESPACE,Y==="\uFEFF")continue;pt(T,Y);continue;case E.BEGIN_WHITESPACE:pt(T,Y);continue;case E.TEXT:if(T.sawRoot&&!T.closedRoot){for(var xt=tt-1;Y&&Y!=="<"&&Y!=="&";)Y=wt(O,tt++),Y&&T.trackPosition&&(T.position++,Y===`
`?(T.line++,T.column=0):T.column++);T.textNode+=O.substring(xt,tt-1)}Y==="<"&&!(T.sawRoot&&T.closedRoot&&!T.strict)?(T.state=E.OPEN_WAKA,T.startTagPosition=T.position):(!U(Y)&&(!T.sawRoot||T.closedRoot)&&rt(T,"Text data outside of root node."),Y==="&"?T.state=E.TEXT_ENTITY:T.textNode+=Y);continue;case E.SCRIPT:Y==="<"?T.state=E.SCRIPT_ENDING:T.script+=Y;continue;case E.SCRIPT_ENDING:Y==="/"?T.state=E.CLOSE_TAG:(T.script+="<"+Y,T.state=E.SCRIPT);continue;case E.OPEN_WAKA:if(Y==="!")T.state=E.SGML_DECL,T.sgmlDecl="";else if(!U(Y))if(lt(z,Y))T.state=E.OPEN_TAG,T.tagName=Y;else if(Y==="/")T.state=E.CLOSE_TAG,T.tagName="";else if(Y==="?")T.state=E.PROC_INST,T.procInstName=T.procInstBody="";else{if(rt(T,"Unencoded <"),T.startTagPosition+1<T.position){var Yt=T.position-T.startTagPosition;Y=new Array(Yt).join(" ")+Y}T.textNode+="<"+Y,T.state=E.TEXT}continue;case E.SGML_DECL:if(T.sgmlDecl+Y==="--"){T.state=E.COMMENT,T.comment="",T.sgmlDecl="";continue}T.doctype&&T.doctype!==!0&&T.sgmlDecl?(T.state=E.DOCTYPE_DTD,T.doctype+="<!"+T.sgmlDecl+Y,T.sgmlDecl=""):(T.sgmlDecl+Y).toUpperCase()===A?(ot(T,"onopencdata"),T.state=E.CDATA,T.sgmlDecl="",T.cdata=""):(T.sgmlDecl+Y).toUpperCase()===N?(T.state=E.DOCTYPE,(T.doctype||T.sawRoot)&&rt(T,"Inappropriately located doctype declaration"),T.doctype="",T.sgmlDecl=""):Y===">"?(ot(T,"onsgmldeclaration",T.sgmlDecl),T.sgmlDecl="",T.state=E.TEXT):(X(Y)&&(T.state=E.SGML_DECL_QUOTED),T.sgmlDecl+=Y);continue;case E.SGML_DECL_QUOTED:Y===T.q&&(T.state=E.SGML_DECL,T.q=""),T.sgmlDecl+=Y;continue;case E.DOCTYPE:Y===">"?(T.state=E.TEXT,ot(T,"ondoctype",T.doctype),T.doctype=!0):(T.doctype+=Y,Y==="["?T.state=E.DOCTYPE_DTD:X(Y)&&(T.state=E.DOCTYPE_QUOTED,T.q=Y));continue;case E.DOCTYPE_QUOTED:T.doctype+=Y,Y===T.q&&(T.q="",T.state=E.DOCTYPE);continue;case E.DOCTYPE_DTD:Y==="]"?(T.doctype+=Y,T.state=E.DOCTYPE):Y==="<"?(T.state=E.OPEN_WAKA,T.startTagPosition=T.position):X(Y)?(T.doctype+=Y,T.state=E.DOCTYPE_DTD_QUOTED,T.q=Y):T.doctype+=Y;continue;case E.DOCTYPE_DTD_QUOTED:T.doctype+=Y,Y===T.q&&(T.state=E.DOCTYPE_DTD,T.q="");continue;case E.COMMENT:Y==="-"?T.state=E.COMMENT_ENDING:T.comment+=Y;continue;case E.COMMENT_ENDING:Y==="-"?(T.state=E.COMMENT_ENDED,T.comment=bt(T.opt,T.comment),T.comment&&ot(T,"oncomment",T.comment),T.comment=""):(T.comment+="-"+Y,T.state=E.COMMENT);continue;case E.COMMENT_ENDED:Y!==">"?(rt(T,"Malformed comment"),T.comment+="--"+Y,T.state=E.COMMENT):T.doctype&&T.doctype!==!0?T.state=E.DOCTYPE_DTD:T.state=E.TEXT;continue;case E.CDATA:Y==="]"?T.state=E.CDATA_ENDING:T.cdata+=Y;continue;case E.CDATA_ENDING:Y==="]"?T.state=E.CDATA_ENDING_2:(T.cdata+="]"+Y,T.state=E.CDATA);continue;case E.CDATA_ENDING_2:Y===">"?(T.cdata&&ot(T,"oncdata",T.cdata),ot(T,"onclosecdata"),T.cdata="",T.state=E.TEXT):Y==="]"?T.cdata+="]":(T.cdata+="]]"+Y,T.state=E.CDATA);continue;case E.PROC_INST:Y==="?"?T.state=E.PROC_INST_ENDING:U(Y)?T.state=E.PROC_INST_BODY:T.procInstName+=Y;continue;case E.PROC_INST_BODY:if(!T.procInstBody&&U(Y))continue;Y==="?"?T.state=E.PROC_INST_ENDING:T.procInstBody+=Y;continue;case E.PROC_INST_ENDING:Y===">"?(ot(T,"onprocessinginstruction",{name:T.procInstName,body:T.procInstBody}),T.procInstName=T.procInstBody="",T.state=E.TEXT):(T.procInstBody+="?"+Y,T.state=E.PROC_INST_BODY);continue;case E.OPEN_TAG:lt(C,Y)?T.tagName+=Y:(yt(T),Y===">"?P(T):Y==="/"?T.state=E.OPEN_TAG_SLASH:(U(Y)||rt(T,"Invalid character in tag name"),T.state=E.ATTRIB));continue;case E.OPEN_TAG_SLASH:Y===">"?(P(T,!0),st(T)):(rt(T,"Forward-slash in opening tag not followed by >"),T.state=E.ATTRIB);continue;case E.ATTRIB:if(U(Y))continue;Y===">"?P(T):Y==="/"?T.state=E.OPEN_TAG_SLASH:lt(z,Y)?(T.attribName=Y,T.attribValue="",T.state=E.ATTRIB_NAME):rt(T,"Invalid attribute name");continue;case E.ATTRIB_NAME:Y==="="?T.state=E.ATTRIB_VALUE:Y===">"?(rt(T,"Attribute without value"),T.attribValue=T.attribName,q(T),P(T)):U(Y)?T.state=E.ATTRIB_NAME_SAW_WHITE:lt(C,Y)?T.attribName+=Y:rt(T,"Invalid attribute name");continue;case E.ATTRIB_NAME_SAW_WHITE:if(Y==="=")T.state=E.ATTRIB_VALUE;else{if(U(Y))continue;rt(T,"Attribute without value"),T.tag.attributes[T.attribName]="",T.attribValue="",ot(T,"onattribute",{name:T.attribName,value:""}),T.attribName="",Y===">"?P(T):lt(z,Y)?(T.attribName=Y,T.state=E.ATTRIB_NAME):(rt(T,"Invalid attribute name"),T.state=E.ATTRIB)}continue;case E.ATTRIB_VALUE:if(U(Y))continue;X(Y)?(T.q=Y,T.state=E.ATTRIB_VALUE_QUOTED):(T.opt.unquotedAttributeValues||St(T,"Unquoted attribute value"),T.state=E.ATTRIB_VALUE_UNQUOTED,T.attribValue=Y);continue;case E.ATTRIB_VALUE_QUOTED:if(Y!==T.q){Y==="&"?T.state=E.ATTRIB_VALUE_ENTITY_Q:T.attribValue+=Y;continue}q(T),T.q="",T.state=E.ATTRIB_VALUE_CLOSED;continue;case E.ATTRIB_VALUE_CLOSED:U(Y)?T.state=E.ATTRIB:Y===">"?P(T):Y==="/"?T.state=E.OPEN_TAG_SLASH:lt(z,Y)?(rt(T,"No whitespace between attributes"),T.attribName=Y,T.attribValue="",T.state=E.ATTRIB_NAME):rt(T,"Invalid attribute name");continue;case E.ATTRIB_VALUE_UNQUOTED:if(!W(Y)){Y==="&"?T.state=E.ATTRIB_VALUE_ENTITY_U:T.attribValue+=Y;continue}q(T),Y===">"?P(T):T.state=E.ATTRIB;continue;case E.CLOSE_TAG:if(T.tagName)Y===">"?st(T):lt(C,Y)?T.tagName+=Y:T.script?(T.script+="</"+T.tagName,T.tagName="",T.state=E.SCRIPT):(U(Y)||rt(T,"Invalid tagname in closing tag"),T.state=E.CLOSE_TAG_SAW_WHITE);else{if(U(Y))continue;$(z,Y)?T.script?(T.script+="</"+Y,T.state=E.SCRIPT):rt(T,"Invalid tagname in closing tag."):T.tagName=Y}continue;case E.CLOSE_TAG_SAW_WHITE:if(U(Y))continue;Y===">"?st(T):rt(T,"Invalid characters in closing tag");continue;case E.TEXT_ENTITY:case E.ATTRIB_VALUE_ENTITY_Q:case E.ATTRIB_VALUE_ENTITY_U:var Lt,Jt;switch(T.state){case E.TEXT_ENTITY:Lt=E.TEXT,Jt="textNode";break;case E.ATTRIB_VALUE_ENTITY_Q:Lt=E.ATTRIB_VALUE_QUOTED,Jt="attribValue";break;case E.ATTRIB_VALUE_ENTITY_U:Lt=E.ATTRIB_VALUE_UNQUOTED,Jt="attribValue";break}if(Y===";"){var oe=ct(T);T.opt.unparsedEntities&&!Object.values(l.XML_ENTITIES).includes(oe)?(T.entity="",T.state=Lt,T.write(oe)):(T[Jt]+=oe,T.entity="",T.state=Lt)}else lt(T.entity.length?v:_,Y)?T.entity+=Y:(rt(T,"Invalid character in entity name"),T[Jt]+="&"+T.entity+Y,T.entity="",T.state=Lt);continue;default:throw new Error(T,"Unknown state: "+T.state)}return T.position>=T.bufferCheckPosition&&d(T),T}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||(function(){var O=String.fromCharCode,T=Math.floor,tt=function(){var Y=16384,xt=[],Yt,Lt,Jt=-1,oe=arguments.length;if(!oe)return"";for(var w="";++Jt<oe;){var p=Number(arguments[Jt]);if(!isFinite(p)||p<0||p>1114111||T(p)!==p)throw RangeError("Invalid code point: "+p);p<=65535?xt.push(p):(p-=65536,Yt=(p>>10)+55296,Lt=p%1024+56320,xt.push(Yt,Lt)),(Jt+1===oe||xt.length>Y)&&(w+=O.apply(null,xt),xt.length=0)}return w};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:tt,configurable:!0,writable:!0}):String.fromCodePoint=tt})()})(u)})(qs)),qs}var wl={},py;function ZE(){return py||(py=1,(function(){wl.stripBOM=function(u){return u[0]==="\uFEFF"?u.substring(1):u}}).call(wl)),wl}var Zn={},dy;function Ky(){return dy||(dy=1,(function(){var u;u=new RegExp(/(?!xmlns)^.*:/),Zn.normalize=function(l){return l.toLowerCase()},Zn.firstCharLowerCase=function(l){return l.charAt(0).toLowerCase()+l.slice(1)},Zn.stripPrefix=function(l){return l.replace(u,"")},Zn.parseNumbers=function(l){return isNaN(l)||(l=l%1===0?parseInt(l,10):parseFloat(l)),l},Zn.parseBooleans=function(l){return/^(?:true|false)$/i.test(l)&&(l=l.toLowerCase()==="true"),l}}).call(Zn)),Zn}var yy;function KE(){return yy||(yy=1,(function(u){(function(){var l,h,o,d,c,s,f,g,b,D=function(M,x){return function(){return M.apply(x,arguments)}},A=function(M,x){for(var B in x)N.call(x,B)&&(M[B]=x[B]);function z(){this.constructor=M}return z.prototype=x.prototype,M.prototype=new z,M.__super__=x.prototype,M},N={}.hasOwnProperty;g=QE(),d=Zs,l=ZE(),f=Ky(),b=Zs.setImmediate,h=Ws().defaults,c=function(M){return typeof M=="object"&&M!=null&&Object.keys(M).length===0},s=function(M,x,B){var z,C,_;for(z=0,C=M.length;z<C;z++)_=M[z],x=_(x,B);return x},o=function(M,x,B){var z;return z=Object.create(null),z.value=B,z.writable=!0,z.enumerable=!0,z.configurable=!0,Object.defineProperty(M,x,z)},u.Parser=(function(M){A(x,M);function x(B){this.parseStringPromise=D(this.parseStringPromise,this),this.parseString=D(this.parseString,this),this.reset=D(this.reset,this),this.assignOrPush=D(this.assignOrPush,this),this.processAsync=D(this.processAsync,this);var z,C,_;if(!(this instanceof u.Parser))return new u.Parser(B);this.options={},C=h["0.2"];for(z in C)N.call(C,z)&&(_=C[z],this.options[z]=_);for(z in B)N.call(B,z)&&(_=B[z],this.options[z]=_);this.options.xmlns&&(this.options.xmlnskey=this.options.attrkey+"ns"),this.options.normalizeTags&&(this.options.tagNameProcessors||(this.options.tagNameProcessors=[]),this.options.tagNameProcessors.unshift(f.normalize)),this.reset()}return x.prototype.processAsync=function(){var B,z;try{return this.remaining.length<=this.options.chunkSize?(B=this.remaining,this.remaining="",this.saxParser=this.saxParser.write(B),this.saxParser.close()):(B=this.remaining.substr(0,this.options.chunkSize),this.remaining=this.remaining.substr(this.options.chunkSize,this.remaining.length),this.saxParser=this.saxParser.write(B),b(this.processAsync))}catch(C){if(z=C,!this.saxParser.errThrown)return this.saxParser.errThrown=!0,this.emit(z)}},x.prototype.assignOrPush=function(B,z,C){return z in B?(B[z]instanceof Array||o(B,z,[B[z]]),B[z].push(C)):this.options.explicitArray?o(B,z,[C]):o(B,z,C)},x.prototype.reset=function(){var B,z,C,_;return this.removeAllListeners(),this.saxParser=g.parser(this.options.strict,{trim:!1,normalize:!1,xmlns:this.options.xmlns}),this.saxParser.errThrown=!1,this.saxParser.onerror=(function(v){return function(U){if(v.saxParser.resume(),!v.saxParser.errThrown)return v.saxParser.errThrown=!0,v.emit("error",U)}})(this),this.saxParser.onend=(function(v){return function(){if(!v.saxParser.ended)return v.saxParser.ended=!0,v.emit("end",v.resultObject)}})(this),this.saxParser.ended=!1,this.EXPLICIT_CHARKEY=this.options.explicitCharkey,this.resultObject=null,_=[],B=this.options.attrkey,z=this.options.charkey,this.saxParser.onopentag=(function(v){return function(U){var X,W,lt,$,E;if(lt={},lt[z]="",!v.options.ignoreAttrs){E=U.attributes;for(X in E)N.call(E,X)&&(!(B in lt)&&!v.options.mergeAttrs&&(lt[B]={}),W=v.options.attrValueProcessors?s(v.options.attrValueProcessors,U.attributes[X],X):U.attributes[X],$=v.options.attrNameProcessors?s(v.options.attrNameProcessors,X):X,v.options.mergeAttrs?v.assignOrPush(lt,$,W):o(lt[B],$,W))}return lt["#name"]=v.options.tagNameProcessors?s(v.options.tagNameProcessors,U.name):U.name,v.options.xmlns&&(lt[v.options.xmlnskey]={uri:U.uri,local:U.local}),_.push(lt)}})(this),this.saxParser.onclosetag=(function(v){return function(){var U,X,W,lt,$,E,J,it,ot,Tt;if(E=_.pop(),$=E["#name"],(!v.options.explicitChildren||!v.options.preserveChildrenOrder)&&delete E["#name"],E.cdata===!0&&(U=E.cdata,delete E.cdata),ot=_[_.length-1],E[z].match(/^\s*$/)&&!U?(X=E[z],delete E[z]):(v.options.trim&&(E[z]=E[z].trim()),v.options.normalize&&(E[z]=E[z].replace(/\s{2,}/g," ").trim()),E[z]=v.options.valueProcessors?s(v.options.valueProcessors,E[z],$):E[z],Object.keys(E).length===1&&z in E&&!v.EXPLICIT_CHARKEY&&(E=E[z])),c(E)&&(typeof v.options.emptyTag=="function"?E=v.options.emptyTag():E=v.options.emptyTag!==""?v.options.emptyTag:X),v.options.validator!=null&&(Tt="/"+(function(){var bt,St,I;for(I=[],bt=0,St=_.length;bt<St;bt++)lt=_[bt],I.push(lt["#name"]);return I})().concat($).join("/"),(function(){var bt;try{return E=v.options.validator(Tt,ot&&ot[$],E)}catch(St){return bt=St,v.emit("error",bt)}})()),v.options.explicitChildren&&!v.options.mergeAttrs&&typeof E=="object"){if(!v.options.preserveChildrenOrder)lt={},v.options.attrkey in E&&(lt[v.options.attrkey]=E[v.options.attrkey],delete E[v.options.attrkey]),!v.options.charsAsChildren&&v.options.charkey in E&&(lt[v.options.charkey]=E[v.options.charkey],delete E[v.options.charkey]),Object.getOwnPropertyNames(E).length>0&&(lt[v.options.childkey]=E),E=lt;else if(ot){ot[v.options.childkey]=ot[v.options.childkey]||[],J={};for(W in E)N.call(E,W)&&o(J,W,E[W]);ot[v.options.childkey].push(J),delete E["#name"],Object.keys(E).length===1&&z in E&&!v.EXPLICIT_CHARKEY&&(E=E[z])}}return _.length>0?v.assignOrPush(ot,$,E):(v.options.explicitRoot&&(it=E,E={},o(E,$,it)),v.resultObject=E,v.saxParser.ended=!0,v.emit("end",v.resultObject))}})(this),C=(function(v){return function(U){var X,W;if(W=_[_.length-1],W)return W[z]+=U,v.options.explicitChildren&&v.options.preserveChildrenOrder&&v.options.charsAsChildren&&(v.options.includeWhiteChars||U.replace(/\\n/g,"").trim()!=="")&&(W[v.options.childkey]=W[v.options.childkey]||[],X={"#name":"__text__"},X[z]=U,v.options.normalize&&(X[z]=X[z].replace(/\s{2,}/g," ").trim()),W[v.options.childkey].push(X)),W}})(this),this.saxParser.ontext=C,this.saxParser.oncdata=(function(v){return function(U){var X;if(X=C(U),X)return X.cdata=!0}})()},x.prototype.parseString=function(B,z){var C;z!=null&&typeof z=="function"&&(this.on("end",function(_){return this.reset(),z(null,_)}),this.on("error",function(_){return this.reset(),z(_)}));try{return B=B.toString(),B.trim()===""?(this.emit("end",null),!0):(B=l.stripBOM(B),this.options.async?(this.remaining=B,b(this.processAsync),this.saxParser):this.saxParser.write(B).close())}catch(_){if(C=_,this.saxParser.errThrown||this.saxParser.ended){if(this.saxParser.ended)throw C}else return this.emit("error",C),this.saxParser.errThrown=!0}},x.prototype.parseStringPromise=function(B){return new Promise((function(z){return function(C,_){return z.parseString(B,function(v,U){return v?_(v):C(U)})}})(this))},x})(d),u.parseString=function(M,x,B){var z,C,_;return B!=null?(typeof B=="function"&&(z=B),typeof x=="object"&&(C=x)):(typeof x=="function"&&(z=x),C={}),_=new u.Parser(C),_.parseString(M,z)},u.parseStringPromise=function(M,x){var B,z;return typeof x=="object"&&(B=x),z=new u.Parser(B),z.parseStringPromise(M)}}).call(Dl)})(Dl)),Dl}var my;function PE(){return my||(my=1,(function(){var u,l,h,o,d=function(s,f){for(var g in f)c.call(f,g)&&(s[g]=f[g]);function b(){this.constructor=s}return b.prototype=f.prototype,s.prototype=new b,s.__super__=f.prototype,s},c={}.hasOwnProperty;l=Ws(),u=qE(),h=KE(),o=Ky(),an.defaults=l.defaults,an.processors=o,an.ValidationError=(function(s){d(f,s);function f(g){this.message=g}return f})(Error),an.Builder=u.Builder,an.Parser=h.Parser,an.parseString=h.parseString,an.parseStringPromise=h.parseStringPromise}).call(an)),an}var JE=PE();class kE{constructor(l="localhost",h=9e3){this.host=l,this.port=h,this.baseURL=`http://${l}:${h}`}async sendXMLRequest(l){try{return(await te.post(this.baseURL,l,{headers:{"Content-Type":"application/xml"},timeout:1e4})).data}catch(h){throw new Error(`Tally request failed: ${h.message}`)}}parseXMLResponse(l){return new Promise((h,o)=>{JE.parseString(l,{explicitArray:!1},(d,c)=>{d?o(new Error(`XML parsing failed: ${d.message}`)):h(c)})})}async testConnection(){const l=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{return{success:!0,data:await this.sendXMLRequest(l)}}catch(h){return{success:!1,error:h.message}}}async getCompanyList(){const l=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{const h=await this.sendXMLRequest(l),o=await this.parseXMLResponse(h);return{success:!0,companies:this.extractCompanyData(o)}}catch(h){return{success:!1,error:h.message}}}extractCompanyData(l){try{const h=[];if(l&&l.ENVELOPE&&l.ENVELOPE.BODY){const o=l.ENVELOPE.BODY;if(o.IMPORTDATA&&o.IMPORTDATA.REQUESTDATA){const d=o.IMPORTDATA.REQUESTDATA;d.TALLYMESSAGE&&(Array.isArray(d.TALLYMESSAGE)?d.TALLYMESSAGE:[d.TALLYMESSAGE]).forEach(s=>{s.COMPANY&&(Array.isArray(s.COMPANY)?s.COMPANY:[s.COMPANY]).forEach(g=>{h.push({name:g.NAME||"Unknown Company",guid:g.GUID||"",remoteid:g.REMOTEID||"",alterid:g.ALTERID||"",masterid:g.MASTERID||""})})})}if(o.DATA&&o.DATA.COLLECTION){const d=o.DATA.COLLECTION;d.COMPANY&&(Array.isArray(d.COMPANY)?d.COMPANY:[d.COMPANY]).forEach(s=>{h.push({name:s.NAME||s.$||"Unknown Company",guid:s.GUID||"",remoteid:s.REMOTEID||"",alterid:s.ALTERID||"",masterid:s.MASTERID||""})})}}return h}catch(h){return console.error("Error extracting company data:",h),[]}}async getCompanyDetails(l){const h=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>Company</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                <SVCOMPANY>${l}</SVCOMPANY>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{const o=await this.sendXMLRequest(h);return{success:!0,data:await this.parseXMLResponse(o)}}catch(o){return{success:!1,error:o.message}}}}const WE=({tallyConnected:u})=>{const[l,h]=Te.useState([]),[o,d]=Te.useState(!1),[c,s]=Te.useState(null),[f,g]=Te.useState(null),[b,D]=Te.useState(null),A=new kE,N=async()=>{if(!u){s("Tally Prime is not connected");return}d(!0),s(null);try{const B=await A.getCompanyList();B.success?(h(B.companies),g(new Date),s(null)):(s(B.error),h([]))}catch(B){s(`Failed to fetch companies: ${B.message}`),h([])}finally{d(!1)}};Te.useEffect(()=>{u?N():(h([]),s(null))},[u]);const M=async B=>{D(B)},x=()=>{N()};return u?ft.jsxs("div",{className:"company-list-container",children:[ft.jsxs("div",{className:"company-list-header",children:[ft.jsx("h3",{children:"Company List"}),ft.jsxs("button",{onClick:x,disabled:o,className:"refresh-button",children:[o?"🔄":"↻"," Refresh"]})]}),c&&ft.jsx("div",{className:"error-message",children:ft.jsxs("p",{children:["❌ ",c]})}),o&&ft.jsx("div",{className:"loading-message",children:ft.jsx("p",{children:"🔄 Loading companies..."})}),f&&ft.jsxs("div",{className:"last-fetched",children:["Last updated: ",f.toLocaleString()]}),l.length>0&&ft.jsxs("div",{className:"company-list",children:[ft.jsxs("div",{className:"company-count",children:["Found ",l.length," companies"]}),ft.jsxs("div",{className:"company-table",children:[ft.jsxs("div",{className:"company-table-header",children:[ft.jsx("div",{className:"company-header-cell",children:"Company Name"}),ft.jsx("div",{className:"company-header-cell",children:"GUID"}),ft.jsx("div",{className:"company-header-cell",children:"Actions"})]}),l.map((B,z)=>ft.jsxs("div",{className:`company-row ${b?.name===B.name?"selected":""}`,onClick:()=>M(B),children:[ft.jsx("div",{className:"company-cell company-name",children:B.name}),ft.jsx("div",{className:"company-cell company-guid",children:B.guid||"N/A"}),ft.jsx("div",{className:"company-cell company-actions",children:ft.jsx("button",{className:"select-button",onClick:C=>{C.stopPropagation(),M(B)},children:"Select"})})]},B.guid||z))]})]}),l.length===0&&!o&&!c&&u&&ft.jsx("div",{className:"no-companies-message",children:ft.jsx("p",{children:"No companies found. Make sure Tally Prime has companies configured."})}),b&&ft.jsxs("div",{className:"selected-company-details",children:[ft.jsx("h4",{children:"Selected Company Details"}),ft.jsxs("div",{className:"company-details",children:[ft.jsxs("p",{children:[ft.jsx("strong",{children:"Name:"})," ",b.name]}),b.guid&&ft.jsxs("p",{children:[ft.jsx("strong",{children:"GUID:"})," ",b.guid]}),b.remoteid&&ft.jsxs("p",{children:[ft.jsx("strong",{children:"Remote ID:"})," ",b.remoteid]}),b.alterid&&ft.jsxs("p",{children:[ft.jsx("strong",{children:"Alter ID:"})," ",b.alterid]}),b.masterid&&ft.jsxs("p",{children:[ft.jsx("strong",{children:"Master ID:"})," ",b.masterid]})]})]})]}):ft.jsxs("div",{className:"company-list-container",children:[ft.jsx("div",{className:"company-list-header",children:ft.jsx("h3",{children:"Company List"})}),ft.jsx("div",{className:"company-list-message",children:ft.jsx("p",{children:"Please ensure Tally Prime is connected to view companies."})})]})};function $E(){const[u,l]=Te.useState(!1),h=o=>{l(o)};return ft.jsxs("div",{className:"app",children:[ft.jsxs("header",{className:"app-header",children:[ft.jsx("h1",{children:"Tally Prime Middleware"}),ft.jsx("p",{children:"Monitor connections and manage Tally Prime data"})]}),ft.jsxs("main",{className:"app-main",children:[ft.jsxs("div",{className:"status-section",children:[ft.jsx(bg,{}),ft.jsx(W1,{onConnectionChange:h})]}),ft.jsx("div",{className:"content-section",children:ft.jsx(WE,{tallyConnected:u})})]}),ft.jsx("footer",{className:"app-footer",children:ft.jsx("p",{children:"Tally Prime Middleware v1.0.0 - Built with React & Electron"})})]})}Tg.createRoot(document.getElementById("root")).render(ft.jsx(Te.StrictMode,{children:ft.jsx($E,{})}));
