{"name": "tally-middleware", "private": true, "version": "0.0.0", "type": "module", "main": "public/electron.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "concurrently": "^9.2.1", "electron": "^38.0.0", "electron-builder": "^26.0.12", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2", "wait-on": "^8.0.4"}, "build": {"appId": "com.tallyware.middleware", "productName": "Tally Middleware", "directories": {"output": "dist"}, "files": ["dist/**/*", "public/electron.cjs", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}