import { useState, useEffect } from 'react';
import axios from 'axios';
import './StatusIndicator.css';

const TallyStatus = ({ onConnectionChange }) => {
  const [connectionStatus, setConnectionStatus] = useState('connecting');
  const [lastChecked, setLastChecked] = useState(new Date());
  const [error, setError] = useState(null);
  const [tallyInfo, setTallyInfo] = useState(null);

  const TALLY_HOST = 'localhost';
  const TALLY_PORT = 9000;
  const TALLY_URL = `http://${TALLY_HOST}:${TALLY_PORT}`;

  const checkTallyConnection = async () => {
    try {
      setConnectionStatus('connecting');
      setError(null);

      // Simple XML request to check if Tally is responding
      const testXML = `
        <ENVELOPE>
          <HEADER>
            <TALLYREQUEST>Export Data</TALLYREQUEST>
          </HEADER>
          <BODY>
            <EXPORTDATA>
              <REQUESTDESC>
                <REPORTNAME>List of Companies</REPORTNAME>
              </REQUESTDESC>
            </EXPORTDATA>
          </BODY>
        </ENVELOPE>
      `;

      const response = await axios.post(TALLY_URL, testXML, {
        headers: {
          'Content-Type': 'application/xml',
        },
        timeout: 5000, // 5 second timeout
      });

      if (response.status === 200) {
        setConnectionStatus('online');
        setTallyInfo({
          host: TALLY_HOST,
          port: TALLY_PORT,
          responseTime: Date.now() - startTime
        });
        
        // Notify parent component about connection status
        if (onConnectionChange) {
          onConnectionChange(true);
        }
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      setConnectionStatus('offline');
      setError(error.message);
      setTallyInfo(null);
      
      // Notify parent component about connection status
      if (onConnectionChange) {
        onConnectionChange(false);
      }
    } finally {
      setLastChecked(new Date());
    }
  };

  useEffect(() => {
    let startTime;
    
    const performCheck = () => {
      startTime = Date.now();
      checkTallyConnection();
    };

    // Initial check
    performCheck();

    // Check every 10 seconds
    const intervalId = setInterval(performCheck, 10000);

    // Cleanup
    return () => {
      clearInterval(intervalId);
    };
  }, []);

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'online':
        return '✅';
      case 'offline':
        return '❌';
      case 'connecting':
        return '🔄';
      default:
        return '❓';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'online':
        return 'Connected to Tally Prime';
      case 'offline':
        return 'Tally Prime not accessible';
      case 'connecting':
        return 'Checking connection...';
      default:
        return 'Unknown status';
    }
  };

  return (
    <div className="status-container">
      <div className="status-header">
        <h3>Tally Prime Connection</h3>
      </div>
      <div className={`status-indicator ${connectionStatus}`}>
        <div className="status-icon">
          {getStatusIcon()}
        </div>
        <div className="status-details">
          <div className="status-text">
            {getStatusText()}
          </div>
          <div className="status-timestamp">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
          {tallyInfo && (
            <div className="status-timestamp">
              {TALLY_HOST}:{TALLY_PORT} ({tallyInfo.responseTime}ms)
            </div>
          )}
          {error && (
            <div className="status-error">
              Error: {error}
            </div>
          )}
        </div>
      </div>
      <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
        Make sure Tally Prime is running with Gateway enabled on port {TALLY_PORT}
      </div>
    </div>
  );
};

export default TallyStatus;
