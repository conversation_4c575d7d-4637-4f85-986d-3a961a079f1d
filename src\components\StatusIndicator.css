.status-container {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 280px;
}

.status-header {
  margin-bottom: 12px;
}

.status-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.status-indicator.online {
  background-color: #e8f5e8;
  border: 1px solid #4caf50;
}

.status-indicator.offline {
  background-color: #ffeaea;
  border: 1px solid #f44336;
}

.status-indicator.connecting {
  background-color: #fff3e0;
  border: 1px solid #ff9800;
}

.status-icon {
  font-size: 24px;
  margin-right: 12px;
  min-width: 32px;
  text-align: center;
}

.status-details {
  flex: 1;
}

.status-text {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.status-indicator.online .status-text {
  color: #2e7d32;
}

.status-indicator.offline .status-text {
  color: #c62828;
}

.status-indicator.connecting .status-text {
  color: #ef6c00;
}

.status-timestamp {
  font-size: 12px;
  color: #666;
}

.status-error {
  font-size: 12px;
  color: #c62828;
  margin-top: 4px;
}

/* Pulse animation for connecting state */
.status-indicator.connecting .status-icon {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .status-container {
    margin: 4px;
    padding: 12px;
    min-width: 240px;
  }
  
  .status-icon {
    font-size: 20px;
    margin-right: 8px;
    min-width: 28px;
  }
}
