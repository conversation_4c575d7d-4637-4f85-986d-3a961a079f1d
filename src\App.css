#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: #f5f5f5;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

.app-header {
  background: #2c3e50;
  color: white;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-content > div {
  text-align: left;
}

.settings-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s ease;
  min-width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.app-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.status-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.content-section {
  display: flex;
  flex-direction: column;
}

.app-footer {
  background: #34495e;
  color: white;
  text-align: center;
  padding: 16px;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-main {
    padding: 12px;
  }

  .status-section {
    flex-direction: column;
    gap: 8px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .header-content > div {
    text-align: center;
  }

  .app-header h1 {
    font-size: 24px;
  }

  .app-header p {
    font-size: 14px;
  }
}
