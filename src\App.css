#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: #f5f5f5;
}

.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "<PERSON><PERSON>", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

.app-header {
  background: #2c3e50;
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.app-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.status-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.content-section {
  display: flex;
  flex-direction: column;
}

.app-footer {
  background: #34495e;
  color: white;
  text-align: center;
  padding: 16px;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-main {
    padding: 12px;
  }

  .status-section {
    flex-direction: column;
    gap: 8px;
  }

  .app-header h1 {
    font-size: 24px;
  }

  .app-header p {
    font-size: 14px;
  }
}
