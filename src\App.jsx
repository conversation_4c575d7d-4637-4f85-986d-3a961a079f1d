import { useState } from "react";
import InternetStatus from "./components/InternetStatus";
import TallyStatus from "./components/TallyStatus";
import CompanyList from "./components/CompanyList";
import Settings from "./components/Settings";
import { SettingsProvider, useSettings } from "./contexts/SettingsContext";
import "./App.css";

function AppContent() {
  const { tallySettings, updateTallySettings } = useSettings();
  const [tallyConnected, setTallyConnected] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const handleTallyConnectionChange = (isConnected) => {
    setTallyConnected(isConnected);
  };

  const handleSettingsChange = (newSettings) => {
    updateTallySettings(newSettings);
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <div>
            <h1>Tally Prime Middleware</h1>
            <p>Monitor connections and manage Tally Prime data</p>
          </div>
          <button
            className="settings-button"
            onClick={() => setShowSettings(true)}
            title="Settings"
          >
            ⚙️
          </button>
        </div>
      </header>

      <main className="app-main">
        <div className="status-section">
          <InternetStatus />
          <TallyStatus onConnectionChange={handleTallyConnectionChange} />
        </div>

        <div className="content-section">
          <CompanyList tallyConnected={tallyConnected} />
        </div>
      </main>

      <footer className="app-footer">
        <p>Tally Prime Middleware v1.0.0 - Built with React & Electron</p>
      </footer>

      <Settings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSettingsChange={handleSettingsChange}
        currentSettings={tallySettings}
      />
    </div>
  );
}

function App() {
  return (
    <SettingsProvider>
      <AppContent />
    </SettingsProvider>
  );
}

export default App;
