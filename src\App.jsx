import { useState } from "react";
import InternetStatus from "./components/InternetStatus";
import TallyStatus from "./components/TallyStatus";
import CompanyList from "./components/CompanyList";
import "./App.css";

function App() {
  const [tallyConnected, setTallyConnected] = useState(false);

  const handleTallyConnectionChange = (isConnected) => {
    setTallyConnected(isConnected);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>Tally Prime Middleware</h1>
        <p>Monitor connections and manage Tally Prime data</p>
      </header>

      <main className="app-main">
        <div className="status-section">
          <InternetStatus />
          <TallyStatus onConnectionChange={handleTallyConnectionChange} />
        </div>

        <div className="content-section">
          <CompanyList tallyConnected={tallyConnected} />
        </div>
      </main>

      <footer className="app-footer">
        <p>Tally Prime Middleware v1.0.0 - Built with React & Electron</p>
      </footer>
    </div>
  );
}

export default App;
