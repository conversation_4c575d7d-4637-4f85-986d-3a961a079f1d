import { useState, useEffect } from 'react';
import './StatusIndicator.css';

const InternetStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [lastChecked, setLastChecked] = useState(new Date());

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setLastChecked(new Date());
    };

    const handleOffline = () => {
      setIsOnline(false);
      setLastChecked(new Date());
    };

    // Additional check by trying to fetch a small resource
    const checkInternetConnection = async () => {
      try {
        const response = await fetch('https://www.google.com/favicon.ico', {
          method: 'HEAD',
          mode: 'no-cors',
          cache: 'no-cache'
        });
        setIsOnline(true);
      } catch (error) {
        setIsOnline(false);
      }
      setLastChecked(new Date());
    };

    // Listen to browser online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Periodic check every 30 seconds
    const intervalId = setInterval(checkInternetConnection, 30000);

    // Initial check
    checkInternetConnection();

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(intervalId);
    };
  }, []);

  return (
    <div className="status-container">
      <div className="status-header">
        <h3>Internet Connection</h3>
      </div>
      <div className={`status-indicator ${isOnline ? 'online' : 'offline'}`}>
        <div className="status-icon">
          {isOnline ? '🌐' : '❌'}
        </div>
        <div className="status-details">
          <div className="status-text">
            {isOnline ? 'Connected' : 'Disconnected'}
          </div>
          <div className="status-timestamp">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternetStatus;
