(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))o(d);new MutationObserver(d=>{for(const c of d)if(c.type==="childList")for(const s of c.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function h(d){const c={};return d.integrity&&(c.integrity=d.integrity),d.referrerPolicy&&(c.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?c.credentials="include":d.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function o(d){if(d.ep)return;d.ep=!0;const c=h(d);fetch(d.href,c)}})();function hg(u){if(Object.prototype.hasOwnProperty.call(u,"__esModule"))return u;var l=u.default;if(typeof l=="function"){var h=function o(){var d=!1;try{d=this instanceof o}catch{}return d?Reflect.construct(l,arguments,this.constructor):l.apply(this,arguments)};h.prototype=l.prototype}else h={};return Object.defineProperty(h,"__esModule",{value:!0}),Object.keys(u).forEach(function(o){var d=Object.getOwnPropertyDescriptor(u,o);Object.defineProperty(h,o,d.get?d:{enumerable:!0,get:function(){return u[o]}})}),h}var Ms={exports:{}},Wr={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rp;function dg(){if(rp)return Wr;rp=1;var u=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function h(o,d,c){var s=null;if(c!==void 0&&(s=""+c),d.key!==void 0&&(s=""+d.key),"key"in d){c={};for(var f in d)f!=="key"&&(c[f]=d[f])}else c=d;return d=c.ref,{$$typeof:u,type:o,key:s,ref:d!==void 0?d:null,props:c}}return Wr.Fragment=l,Wr.jsx=h,Wr.jsxs=h,Wr}var ap;function pg(){return ap||(ap=1,Ms.exports=dg()),Ms.exports}var lt=pg(),_s={exports:{}},At={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lp;function yg(){if(lp)return At;lp=1;var u=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),c=Symbol.for("react.consumer"),s=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),A=Symbol.iterator;function x(z){return z===null||typeof z!="object"?null:(z=A&&z[A]||z["@@iterator"],typeof z=="function"?z:null)}var M={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},N=Object.assign,X={};function B(z,P,ct){this.props=z,this.context=P,this.refs=X,this.updater=ct||M}B.prototype.isReactComponent={},B.prototype.setState=function(z,P){if(typeof z!="object"&&typeof z!="function"&&z!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,z,P,"setState")},B.prototype.forceUpdate=function(z){this.updater.enqueueForceUpdate(this,z,"forceUpdate")};function C(){}C.prototype=B.prototype;function _(z,P,ct){this.props=z,this.context=P,this.refs=X,this.updater=ct||M}var E=_.prototype=new C;E.constructor=_,N(E,B.prototype),E.isPureReactComponent=!0;var U=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function ut(z,P,ct,ft,dt,wt){return ct=wt.ref,{$$typeof:u,type:z,key:P,ref:ct!==void 0?ct:null,props:wt}}function $(z,P){return ut(z.type,P,void 0,void 0,void 0,z.props)}function v(z){return typeof z=="object"&&z!==null&&z.$$typeof===u}function J(z){var P={"=":"=0",":":"=2"};return"$"+z.replace(/[=:]/g,function(ct){return P[ct]})}var it=/\/+/g;function st(z,P){return typeof z=="object"&&z!==null&&z.key!=null?J(""+z.key):P.toString(36)}function Tt(){}function bt(z){switch(z.status){case"fulfilled":return z.value;case"rejected":throw z.reason;default:switch(typeof z.status=="string"?z.then(Tt,Tt):(z.status="pending",z.then(function(P){z.status==="pending"&&(z.status="fulfilled",z.value=P)},function(P){z.status==="pending"&&(z.status="rejected",z.reason=P)})),z.status){case"fulfilled":return z.value;case"rejected":throw z.reason}}throw z}function St(z,P,ct,ft,dt){var wt=typeof z;(wt==="undefined"||wt==="boolean")&&(z=null);var vt=!1;if(z===null)vt=!0;else switch(wt){case"bigint":case"string":case"number":vt=!0;break;case"object":switch(z.$$typeof){case u:case l:vt=!0;break;case S:return vt=z._init,St(vt(z._payload),P,ct,ft,dt)}}if(vt)return dt=dt(z),vt=ft===""?"."+st(z,0):ft,U(dt)?(ct="",vt!=null&&(ct=vt.replace(it,"$&/")+"/"),St(dt,P,ct,"",function(tt){return tt})):dt!=null&&(v(dt)&&(dt=$(dt,ct+(dt.key==null||z&&z.key===dt.key?"":(""+dt.key).replace(it,"$&/")+"/")+vt)),P.push(dt)),1;vt=0;var O=ft===""?".":ft+":";if(U(z))for(var T=0;T<z.length;T++)ft=z[T],wt=O+st(ft,T),vt+=St(ft,P,ct,wt,dt);else if(T=x(z),typeof T=="function")for(z=T.call(z),T=0;!(ft=z.next()).done;)ft=ft.value,wt=O+st(ft,T++),vt+=St(ft,P,ct,wt,dt);else if(wt==="object"){if(typeof z.then=="function")return St(bt(z),P,ct,ft,dt);throw P=String(z),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(z).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.")}return vt}function I(z,P,ct){if(z==null)return z;var ft=[],dt=0;return St(z,ft,"","",function(wt){return P.call(ct,wt,dt++)}),ft}function rt(z){if(z._status===-1){var P=z._result;P=P(),P.then(function(ct){(z._status===0||z._status===-1)&&(z._status=1,z._result=ct)},function(ct){(z._status===0||z._status===-1)&&(z._status=2,z._result=ct)}),z._status===-1&&(z._status=0,z._result=P)}if(z._status===1)return z._result.default;throw z._result}var yt=typeof reportError=="function"?reportError:function(z){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var P=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof z=="object"&&z!==null&&typeof z.message=="string"?String(z.message):String(z),error:z});if(!window.dispatchEvent(P))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",z);return}console.error(z)};function Bt(){}return At.Children={map:I,forEach:function(z,P,ct){I(z,function(){P.apply(this,arguments)},ct)},count:function(z){var P=0;return I(z,function(){P++}),P},toArray:function(z){return I(z,function(P){return P})||[]},only:function(z){if(!v(z))throw Error("React.Children.only expected to receive a single React element child.");return z}},At.Component=B,At.Fragment=h,At.Profiler=d,At.PureComponent=_,At.StrictMode=o,At.Suspense=g,At.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,At.__COMPILER_RUNTIME={__proto__:null,c:function(z){return j.H.useMemoCache(z)}},At.cache=function(z){return function(){return z.apply(null,arguments)}},At.cloneElement=function(z,P,ct){if(z==null)throw Error("The argument must be a React element, but you passed "+z+".");var ft=N({},z.props),dt=z.key,wt=void 0;if(P!=null)for(vt in P.ref!==void 0&&(wt=void 0),P.key!==void 0&&(dt=""+P.key),P)!W.call(P,vt)||vt==="key"||vt==="__self"||vt==="__source"||vt==="ref"&&P.ref===void 0||(ft[vt]=P[vt]);var vt=arguments.length-2;if(vt===1)ft.children=ct;else if(1<vt){for(var O=Array(vt),T=0;T<vt;T++)O[T]=arguments[T+2];ft.children=O}return ut(z.type,dt,void 0,void 0,wt,ft)},At.createContext=function(z){return z={$$typeof:s,_currentValue:z,_currentValue2:z,_threadCount:0,Provider:null,Consumer:null},z.Provider=z,z.Consumer={$$typeof:c,_context:z},z},At.createElement=function(z,P,ct){var ft,dt={},wt=null;if(P!=null)for(ft in P.key!==void 0&&(wt=""+P.key),P)W.call(P,ft)&&ft!=="key"&&ft!=="__self"&&ft!=="__source"&&(dt[ft]=P[ft]);var vt=arguments.length-2;if(vt===1)dt.children=ct;else if(1<vt){for(var O=Array(vt),T=0;T<vt;T++)O[T]=arguments[T+2];dt.children=O}if(z&&z.defaultProps)for(ft in vt=z.defaultProps,vt)dt[ft]===void 0&&(dt[ft]=vt[ft]);return ut(z,wt,void 0,void 0,null,dt)},At.createRef=function(){return{current:null}},At.forwardRef=function(z){return{$$typeof:f,render:z}},At.isValidElement=v,At.lazy=function(z){return{$$typeof:S,_payload:{_status:-1,_result:z},_init:rt}},At.memo=function(z,P){return{$$typeof:b,type:z,compare:P===void 0?null:P}},At.startTransition=function(z){var P=j.T,ct={};j.T=ct;try{var ft=z(),dt=j.S;dt!==null&&dt(ct,ft),typeof ft=="object"&&ft!==null&&typeof ft.then=="function"&&ft.then(Bt,yt)}catch(wt){yt(wt)}finally{j.T=P}},At.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},At.use=function(z){return j.H.use(z)},At.useActionState=function(z,P,ct){return j.H.useActionState(z,P,ct)},At.useCallback=function(z,P){return j.H.useCallback(z,P)},At.useContext=function(z){return j.H.useContext(z)},At.useDebugValue=function(){},At.useDeferredValue=function(z,P){return j.H.useDeferredValue(z,P)},At.useEffect=function(z,P,ct){var ft=j.H;if(typeof ct=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ft.useEffect(z,P)},At.useId=function(){return j.H.useId()},At.useImperativeHandle=function(z,P,ct){return j.H.useImperativeHandle(z,P,ct)},At.useInsertionEffect=function(z,P){return j.H.useInsertionEffect(z,P)},At.useLayoutEffect=function(z,P){return j.H.useLayoutEffect(z,P)},At.useMemo=function(z,P){return j.H.useMemo(z,P)},At.useOptimistic=function(z,P){return j.H.useOptimistic(z,P)},At.useReducer=function(z,P,ct){return j.H.useReducer(z,P,ct)},At.useRef=function(z){return j.H.useRef(z)},At.useState=function(z){return j.H.useState(z)},At.useSyncExternalStore=function(z,P,ct){return j.H.useSyncExternalStore(z,P,ct)},At.useTransition=function(){return j.H.useTransition()},At.version="19.1.1",At}var up;function Ks(){return up||(up=1,_s.exports=yg()),_s.exports}var Kt=Ks(),Rs={exports:{}},$r={},Cs={exports:{}},Ls={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var op;function mg(){return op||(op=1,(function(u){function l(I,rt){var yt=I.length;I.push(rt);t:for(;0<yt;){var Bt=yt-1>>>1,z=I[Bt];if(0<d(z,rt))I[Bt]=rt,I[yt]=z,yt=Bt;else break t}}function h(I){return I.length===0?null:I[0]}function o(I){if(I.length===0)return null;var rt=I[0],yt=I.pop();if(yt!==rt){I[0]=yt;t:for(var Bt=0,z=I.length,P=z>>>1;Bt<P;){var ct=2*(Bt+1)-1,ft=I[ct],dt=ct+1,wt=I[dt];if(0>d(ft,yt))dt<z&&0>d(wt,ft)?(I[Bt]=wt,I[dt]=yt,Bt=dt):(I[Bt]=ft,I[ct]=yt,Bt=ct);else if(dt<z&&0>d(wt,yt))I[Bt]=wt,I[dt]=yt,Bt=dt;else break t}}return rt}function d(I,rt){var yt=I.sortIndex-rt.sortIndex;return yt!==0?yt:I.id-rt.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var c=performance;u.unstable_now=function(){return c.now()}}else{var s=Date,f=s.now();u.unstable_now=function(){return s.now()-f}}var g=[],b=[],S=1,A=null,x=3,M=!1,N=!1,X=!1,B=!1,C=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,E=typeof setImmediate<"u"?setImmediate:null;function U(I){for(var rt=h(b);rt!==null;){if(rt.callback===null)o(b);else if(rt.startTime<=I)o(b),rt.sortIndex=rt.expirationTime,l(g,rt);else break;rt=h(b)}}function j(I){if(X=!1,U(I),!N)if(h(g)!==null)N=!0,W||(W=!0,st());else{var rt=h(b);rt!==null&&St(j,rt.startTime-I)}}var W=!1,ut=-1,$=5,v=-1;function J(){return B?!0:!(u.unstable_now()-v<$)}function it(){if(B=!1,W){var I=u.unstable_now();v=I;var rt=!0;try{t:{N=!1,X&&(X=!1,_(ut),ut=-1),M=!0;var yt=x;try{e:{for(U(I),A=h(g);A!==null&&!(A.expirationTime>I&&J());){var Bt=A.callback;if(typeof Bt=="function"){A.callback=null,x=A.priorityLevel;var z=Bt(A.expirationTime<=I);if(I=u.unstable_now(),typeof z=="function"){A.callback=z,U(I),rt=!0;break e}A===h(g)&&o(g),U(I)}else o(g);A=h(g)}if(A!==null)rt=!0;else{var P=h(b);P!==null&&St(j,P.startTime-I),rt=!1}}break t}finally{A=null,x=yt,M=!1}rt=void 0}}finally{rt?st():W=!1}}}var st;if(typeof E=="function")st=function(){E(it)};else if(typeof MessageChannel<"u"){var Tt=new MessageChannel,bt=Tt.port2;Tt.port1.onmessage=it,st=function(){bt.postMessage(null)}}else st=function(){C(it,0)};function St(I,rt){ut=C(function(){I(u.unstable_now())},rt)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(I){I.callback=null},u.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<I?Math.floor(1e3/I):5},u.unstable_getCurrentPriorityLevel=function(){return x},u.unstable_next=function(I){switch(x){case 1:case 2:case 3:var rt=3;break;default:rt=x}var yt=x;x=rt;try{return I()}finally{x=yt}},u.unstable_requestPaint=function(){B=!0},u.unstable_runWithPriority=function(I,rt){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var yt=x;x=I;try{return rt()}finally{x=yt}},u.unstable_scheduleCallback=function(I,rt,yt){var Bt=u.unstable_now();switch(typeof yt=="object"&&yt!==null?(yt=yt.delay,yt=typeof yt=="number"&&0<yt?Bt+yt:Bt):yt=Bt,I){case 1:var z=-1;break;case 2:z=250;break;case 5:z=1073741823;break;case 4:z=1e4;break;default:z=5e3}return z=yt+z,I={id:S++,callback:rt,priorityLevel:I,startTime:yt,expirationTime:z,sortIndex:-1},yt>Bt?(I.sortIndex=yt,l(b,I),h(g)===null&&I===h(b)&&(X?(_(ut),ut=-1):X=!0,St(j,yt-Bt))):(I.sortIndex=z,l(g,I),N||M||(N=!0,W||(W=!0,st()))),I},u.unstable_shouldYield=J,u.unstable_wrapCallback=function(I){var rt=x;return function(){var yt=x;x=rt;try{return I.apply(this,arguments)}finally{x=yt}}}})(Ls)),Ls}var sp;function gg(){return sp||(sp=1,Cs.exports=mg()),Cs.exports}var Us={exports:{}},Te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cp;function vg(){if(cp)return Te;cp=1;var u=Ks();function l(g){var b="https://react.dev/errors/"+g;if(1<arguments.length){b+="?args[]="+encodeURIComponent(arguments[1]);for(var S=2;S<arguments.length;S++)b+="&args[]="+encodeURIComponent(arguments[S])}return"Minified React error #"+g+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(){}var o={d:{f:h,r:function(){throw Error(l(522))},D:h,C:h,L:h,m:h,X:h,S:h,M:h},p:0,findDOMNode:null},d=Symbol.for("react.portal");function c(g,b,S){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:A==null?null:""+A,children:g,containerInfo:b,implementation:S}}var s=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function f(g,b){if(g==="font")return"";if(typeof b=="string")return b==="use-credentials"?b:""}return Te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Te.createPortal=function(g,b){var S=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!b||b.nodeType!==1&&b.nodeType!==9&&b.nodeType!==11)throw Error(l(299));return c(g,b,null,S)},Te.flushSync=function(g){var b=s.T,S=o.p;try{if(s.T=null,o.p=2,g)return g()}finally{s.T=b,o.p=S,o.d.f()}},Te.preconnect=function(g,b){typeof g=="string"&&(b?(b=b.crossOrigin,b=typeof b=="string"?b==="use-credentials"?b:"":void 0):b=null,o.d.C(g,b))},Te.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},Te.preinit=function(g,b){if(typeof g=="string"&&b&&typeof b.as=="string"){var S=b.as,A=f(S,b.crossOrigin),x=typeof b.integrity=="string"?b.integrity:void 0,M=typeof b.fetchPriority=="string"?b.fetchPriority:void 0;S==="style"?o.d.S(g,typeof b.precedence=="string"?b.precedence:void 0,{crossOrigin:A,integrity:x,fetchPriority:M}):S==="script"&&o.d.X(g,{crossOrigin:A,integrity:x,fetchPriority:M,nonce:typeof b.nonce=="string"?b.nonce:void 0})}},Te.preinitModule=function(g,b){if(typeof g=="string")if(typeof b=="object"&&b!==null){if(b.as==null||b.as==="script"){var S=f(b.as,b.crossOrigin);o.d.M(g,{crossOrigin:S,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0})}}else b==null&&o.d.M(g)},Te.preload=function(g,b){if(typeof g=="string"&&typeof b=="object"&&b!==null&&typeof b.as=="string"){var S=b.as,A=f(S,b.crossOrigin);o.d.L(g,S,{crossOrigin:A,integrity:typeof b.integrity=="string"?b.integrity:void 0,nonce:typeof b.nonce=="string"?b.nonce:void 0,type:typeof b.type=="string"?b.type:void 0,fetchPriority:typeof b.fetchPriority=="string"?b.fetchPriority:void 0,referrerPolicy:typeof b.referrerPolicy=="string"?b.referrerPolicy:void 0,imageSrcSet:typeof b.imageSrcSet=="string"?b.imageSrcSet:void 0,imageSizes:typeof b.imageSizes=="string"?b.imageSizes:void 0,media:typeof b.media=="string"?b.media:void 0})}},Te.preloadModule=function(g,b){if(typeof g=="string")if(b){var S=f(b.as,b.crossOrigin);o.d.m(g,{as:typeof b.as=="string"&&b.as!=="script"?b.as:void 0,crossOrigin:S,integrity:typeof b.integrity=="string"?b.integrity:void 0})}else o.d.m(g)},Te.requestFormReset=function(g){o.d.r(g)},Te.unstable_batchedUpdates=function(g,b){return g(b)},Te.useFormState=function(g,b,S){return s.H.useFormState(g,b,S)},Te.useFormStatus=function(){return s.H.useHostTransitionStatus()},Te.version="19.1.1",Te}var fp;function Eg(){if(fp)return Us.exports;fp=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(l){console.error(l)}}return u(),Us.exports=vg(),Us.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hp;function Tg(){if(hp)return $r;hp=1;var u=gg(),l=Ks(),h=Eg();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function c(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function s(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function f(t){if(c(t)!==t)throw Error(o(188))}function g(t){var e=t.alternate;if(!e){if(e=c(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,i=e;;){var r=n.return;if(r===null)break;var a=r.alternate;if(a===null){if(i=r.return,i!==null){n=i;continue}break}if(r.child===a.child){for(a=r.child;a;){if(a===n)return f(r),t;if(a===i)return f(r),e;a=a.sibling}throw Error(o(188))}if(n.return!==i.return)n=r,i=a;else{for(var m=!1,D=r.child;D;){if(D===n){m=!0,n=r,i=a;break}if(D===i){m=!0,i=r,n=a;break}D=D.sibling}if(!m){for(D=a.child;D;){if(D===n){m=!0,n=a,i=r;break}if(D===i){m=!0,i=a,n=r;break}D=D.sibling}if(!m)throw Error(o(189))}}if(n.alternate!==i)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function b(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=b(t),e!==null)return e;t=t.sibling}return null}var S=Object.assign,A=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),M=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),X=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),_=Symbol.for("react.consumer"),E=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),ut=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),v=Symbol.for("react.activity"),J=Symbol.for("react.memo_cache_sentinel"),it=Symbol.iterator;function st(t){return t===null||typeof t!="object"?null:(t=it&&t[it]||t["@@iterator"],typeof t=="function"?t:null)}var Tt=Symbol.for("react.client.reference");function bt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Tt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case N:return"Fragment";case B:return"Profiler";case X:return"StrictMode";case j:return"Suspense";case W:return"SuspenseList";case v:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case M:return"Portal";case E:return(t.displayName||"Context")+".Provider";case _:return(t._context.displayName||"Context")+".Consumer";case U:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ut:return e=t.displayName||null,e!==null?e:bt(t.type)||"Memo";case $:e=t._payload,t=t._init;try{return bt(t(e))}catch{}}return null}var St=Array.isArray,I=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,rt=h.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,yt={pending:!1,data:null,method:null,action:null},Bt=[],z=-1;function P(t){return{current:t}}function ct(t){0>z||(t.current=Bt[z],Bt[z]=null,z--)}function ft(t,e){z++,Bt[z]=t.current,t.current=e}var dt=P(null),wt=P(null),vt=P(null),O=P(null);function T(t,e){switch(ft(vt,e),ft(wt,t),ft(dt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Cd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Cd(e),t=Ld(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}ct(dt),ft(dt,t)}function tt(){ct(dt),ct(wt),ct(vt)}function Y(t){t.memoizedState!==null&&ft(O,t);var e=dt.current,n=Ld(e,t.type);e!==n&&(ft(wt,t),ft(dt,n))}function xt(t){wt.current===t&&(ct(dt),ct(wt)),O.current===t&&(ct(O),Zr._currentValue=yt)}var Yt=Object.prototype.hasOwnProperty,Lt=u.unstable_scheduleCallback,kt=u.unstable_cancelCallback,se=u.unstable_shouldYield,w=u.unstable_requestPaint,p=u.unstable_now,y=u.unstable_getCurrentPriorityLevel,R=u.unstable_ImmediatePriority,V=u.unstable_UserBlockingPriority,at=u.unstable_NormalPriority,ot=u.unstable_LowPriority,Rt=u.unstable_IdlePriority,zt=u.log,Qt=u.unstable_setDisableYieldValue,Wt=null,Ct=null;function _e(t){if(typeof zt=="function"&&Qt(t),Ct&&typeof Ct.setStrictMode=="function")try{Ct.setStrictMode(Wt,t)}catch{}}var ge=Math.clz32?Math.clz32:Wy,ua=Math.log,ky=Math.LN2;function Wy(t){return t>>>=0,t===0?32:31-(ua(t)/ky|0)|0}var oa=256,sa=4194304;function Kn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function ca(t,e,n){var i=t.pendingLanes;if(i===0)return 0;var r=0,a=t.suspendedLanes,m=t.pingedLanes;t=t.warmLanes;var D=i&134217727;return D!==0?(i=D&~a,i!==0?r=Kn(i):(m&=D,m!==0?r=Kn(m):n||(n=D&~t,n!==0&&(r=Kn(n))))):(D=i&~a,D!==0?r=Kn(D):m!==0?r=Kn(m):n||(n=i&~t,n!==0&&(r=Kn(n)))),r===0?0:e!==0&&e!==r&&(e&a)===0&&(a=r&-r,n=e&-e,a>=n||a===32&&(n&4194048)!==0)?e:r}function nr(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function $y(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pc(){var t=oa;return oa<<=1,(oa&4194048)===0&&(oa=256),t}function yc(){var t=sa;return sa<<=1,(sa&62914560)===0&&(sa=4194304),t}function gu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ir(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function tm(t,e,n,i,r,a){var m=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var D=t.entanglements,L=t.expirationTimes,G=t.hiddenUpdates;for(n=m&~n;0<n;){var k=31-ge(n),nt=1<<k;D[k]=0,L[k]=-1;var Q=G[k];if(Q!==null)for(G[k]=null,k=0;k<Q.length;k++){var Z=Q[k];Z!==null&&(Z.lane&=-536870913)}n&=~nt}i!==0&&mc(t,i,0),a!==0&&r===0&&t.tag!==0&&(t.suspendedLanes|=a&~(m&~e))}function mc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-ge(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|n&4194090}function gc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-ge(n),r=1<<i;r&e|t[i]&e&&(t[i]|=e),n&=~r}}function vu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Eu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function vc(){var t=rt.p;return t!==0?t:(t=window.event,t===void 0?32:Wd(t.type))}function em(t,e){var n=rt.p;try{return rt.p=t,e()}finally{rt.p=n}}var An=Math.random().toString(36).slice(2),ve="__reactFiber$"+An,De="__reactProps$"+An,yi="__reactContainer$"+An,Tu="__reactEvents$"+An,nm="__reactListeners$"+An,im="__reactHandles$"+An,Ec="__reactResources$"+An,rr="__reactMarker$"+An;function bu(t){delete t[ve],delete t[De],delete t[Tu],delete t[nm],delete t[im]}function mi(t){var e=t[ve];if(e)return e;for(var n=t.parentNode;n;){if(e=n[yi]||n[ve]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Xd(t);t!==null;){if(n=t[ve])return n;t=Xd(t)}return e}t=n,n=t.parentNode}return null}function gi(t){if(t=t[ve]||t[yi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ar(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function vi(t){var e=t[Ec];return e||(e=t[Ec]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ce(t){t[rr]=!0}var Tc=new Set,bc={};function Pn(t,e){Ei(t,e),Ei(t+"Capture",e)}function Ei(t,e){for(bc[t]=e,t=0;t<e.length;t++)Tc.add(e[t])}var rm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Sc={},Dc={};function am(t){return Yt.call(Dc,t)?!0:Yt.call(Sc,t)?!1:rm.test(t)?Dc[t]=!0:(Sc[t]=!0,!1)}function fa(t,e,n){if(am(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function ha(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function on(t,e,n,i){if(i===null)t.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+i)}}var Su,Ac;function Ti(t){if(Su===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Su=e&&e[1]||"",Ac=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Su+t+Ac}var Du=!1;function Au(t,e){if(!t||Du)return"";Du=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var nt=function(){throw Error()};if(Object.defineProperty(nt.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(nt,[])}catch(Z){var Q=Z}Reflect.construct(t,[],nt)}else{try{nt.call()}catch(Z){Q=Z}t.call(nt.prototype)}}else{try{throw Error()}catch(Z){Q=Z}(nt=t())&&typeof nt.catch=="function"&&nt.catch(function(){})}}catch(Z){if(Z&&Q&&typeof Z.stack=="string")return[Z.stack,Q.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=i.DetermineComponentFrameRoot(),m=a[0],D=a[1];if(m&&D){var L=m.split(`
`),G=D.split(`
`);for(r=i=0;i<L.length&&!L[i].includes("DetermineComponentFrameRoot");)i++;for(;r<G.length&&!G[r].includes("DetermineComponentFrameRoot");)r++;if(i===L.length||r===G.length)for(i=L.length-1,r=G.length-1;1<=i&&0<=r&&L[i]!==G[r];)r--;for(;1<=i&&0<=r;i--,r--)if(L[i]!==G[r]){if(i!==1||r!==1)do if(i--,r--,0>r||L[i]!==G[r]){var k=`
`+L[i].replace(" at new "," at ");return t.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",t.displayName)),k}while(1<=i&&0<=r);break}}}finally{Du=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Ti(n):""}function lm(t){switch(t.tag){case 26:case 27:case 5:return Ti(t.type);case 16:return Ti("Lazy");case 13:return Ti("Suspense");case 19:return Ti("SuspenseList");case 0:case 15:return Au(t.type,!1);case 11:return Au(t.type.render,!1);case 1:return Au(t.type,!0);case 31:return Ti("Activity");default:return""}}function Oc(t){try{var e="";do e+=lm(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function ze(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function wc(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function um(t){var e=wc(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,a=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return r.call(this)},set:function(m){i=""+m,a.call(this,m)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(m){i=""+m},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function da(t){t._valueTracker||(t._valueTracker=um(t))}function Nc(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=wc(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function pa(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var om=/[\n"\\]/g;function qe(t){return t.replace(om,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ou(t,e,n,i,r,a,m,D){t.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?t.type=m:t.removeAttribute("type"),e!=null?m==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ze(e)):t.value!==""+ze(e)&&(t.value=""+ze(e)):m!=="submit"&&m!=="reset"||t.removeAttribute("value"),e!=null?wu(t,m,ze(e)):n!=null?wu(t,m,ze(n)):i!=null&&t.removeAttribute("value"),r==null&&a!=null&&(t.defaultChecked=!!a),r!=null&&(t.checked=r&&typeof r!="function"&&typeof r!="symbol"),D!=null&&typeof D!="function"&&typeof D!="symbol"&&typeof D!="boolean"?t.name=""+ze(D):t.removeAttribute("name")}function xc(t,e,n,i,r,a,m,D){if(a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.type=a),e!=null||n!=null){if(!(a!=="submit"&&a!=="reset"||e!=null))return;n=n!=null?""+ze(n):"",e=e!=null?""+ze(e):n,D||e===t.value||(t.value=e),t.defaultValue=e}i=i??r,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=D?t.checked:!!i,t.defaultChecked=!!i,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(t.name=m)}function wu(t,e,n){e==="number"&&pa(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function bi(t,e,n,i){if(t=t.options,e){e={};for(var r=0;r<n.length;r++)e["$"+n[r]]=!0;for(n=0;n<t.length;n++)r=e.hasOwnProperty("$"+t[n].value),t[n].selected!==r&&(t[n].selected=r),r&&i&&(t[n].defaultSelected=!0)}else{for(n=""+ze(n),e=null,r=0;r<t.length;r++){if(t[r].value===n){t[r].selected=!0,i&&(t[r].defaultSelected=!0);return}e!==null||t[r].disabled||(e=t[r])}e!==null&&(e.selected=!0)}}function Mc(t,e,n){if(e!=null&&(e=""+ze(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+ze(n):""}function _c(t,e,n,i){if(e==null){if(i!=null){if(n!=null)throw Error(o(92));if(St(i)){if(1<i.length)throw Error(o(93));i=i[0]}n=i}n==null&&(n=""),e=n}n=ze(e),t.defaultValue=n,i=t.textContent,i===n&&i!==""&&i!==null&&(t.value=i)}function Si(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var sm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Rc(t,e,n){var i=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,n):typeof n!="number"||n===0||sm.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Cc(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var r in e)i=e[r],e.hasOwnProperty(r)&&n[r]!==i&&Rc(t,r,i)}else for(var a in e)e.hasOwnProperty(a)&&Rc(t,a,e[a])}function Nu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),fm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ya(t){return fm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var xu=null;function Mu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Di=null,Ai=null;function Lc(t){var e=gi(t);if(e&&(t=e.stateNode)){var n=t[De]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ou(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+qe(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var r=i[De]||null;if(!r)throw Error(o(90));Ou(i,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(e=0;e<n.length;e++)i=n[e],i.form===t.form&&Nc(i)}break t;case"textarea":Mc(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&bi(t,!!n.multiple,e,!1)}}}var _u=!1;function Uc(t,e,n){if(_u)return t(e,n);_u=!0;try{var i=t(e);return i}finally{if(_u=!1,(Di!==null||Ai!==null)&&(tl(),Di&&(e=Di,t=Ai,Ai=Di=null,Lc(e),t)))for(e=0;e<t.length;e++)Lc(t[e])}}function lr(t,e){var n=t.stateNode;if(n===null)return null;var i=n[De]||null;if(i===null)return null;n=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ru=!1;if(sn)try{var ur={};Object.defineProperty(ur,"passive",{get:function(){Ru=!0}}),window.addEventListener("test",ur,ur),window.removeEventListener("test",ur,ur)}catch{Ru=!1}var On=null,Cu=null,ma=null;function Bc(){if(ma)return ma;var t,e=Cu,n=e.length,i,r="value"in On?On.value:On.textContent,a=r.length;for(t=0;t<n&&e[t]===r[t];t++);var m=n-t;for(i=1;i<=m&&e[n-i]===r[a-i];i++);return ma=r.slice(t,1<i?1-i:void 0)}function ga(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function va(){return!0}function jc(){return!1}function Ae(t){function e(n,i,r,a,m){this._reactName=n,this._targetInst=r,this.type=i,this.nativeEvent=a,this.target=m,this.currentTarget=null;for(var D in t)t.hasOwnProperty(D)&&(n=t[D],this[D]=n?n(a):a[D]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?va:jc,this.isPropagationStopped=jc,this}return S(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=va)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=va)},persist:function(){},isPersistent:va}),e}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ea=Ae(Jn),or=S({},Jn,{view:0,detail:0}),hm=Ae(or),Lu,Uu,sr,Ta=S({},or,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ju,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==sr&&(sr&&t.type==="mousemove"?(Lu=t.screenX-sr.screenX,Uu=t.screenY-sr.screenY):Uu=Lu=0,sr=t),Lu)},movementY:function(t){return"movementY"in t?t.movementY:Uu}}),Xc=Ae(Ta),dm=S({},Ta,{dataTransfer:0}),pm=Ae(dm),ym=S({},or,{relatedTarget:0}),Bu=Ae(ym),mm=S({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),gm=Ae(mm),vm=S({},Jn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Em=Ae(vm),Tm=S({},Jn,{data:0}),zc=Ae(Tm),bm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Am(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Dm[t])?!!e[t]:!1}function ju(){return Am}var Om=S({},or,{key:function(t){if(t.key){var e=bm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ga(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Sm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ju,charCode:function(t){return t.type==="keypress"?ga(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ga(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),wm=Ae(Om),Nm=S({},Ta,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qc=Ae(Nm),xm=S({},or,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ju}),Mm=Ae(xm),_m=S({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Rm=Ae(_m),Cm=S({},Ta,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Lm=Ae(Cm),Um=S({},Jn,{newState:0,oldState:0}),Bm=Ae(Um),jm=[9,13,27,32],Xu=sn&&"CompositionEvent"in window,cr=null;sn&&"documentMode"in document&&(cr=document.documentMode);var Xm=sn&&"TextEvent"in window&&!cr,Hc=sn&&(!Xu||cr&&8<cr&&11>=cr),Yc=" ",Vc=!1;function Fc(t,e){switch(t){case"keyup":return jm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gc(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Oi=!1;function zm(t,e){switch(t){case"compositionend":return Gc(e);case"keypress":return e.which!==32?null:(Vc=!0,Yc);case"textInput":return t=e.data,t===Yc&&Vc?null:t;default:return null}}function qm(t,e){if(Oi)return t==="compositionend"||!Xu&&Fc(t,e)?(t=Bc(),ma=Cu=On=null,Oi=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Hc&&e.locale!=="ko"?null:e.data;default:return null}}var Hm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ic(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Hm[t.type]:e==="textarea"}function Qc(t,e,n,i){Di?Ai?Ai.push(i):Ai=[i]:Di=i,e=ll(e,"onChange"),0<e.length&&(n=new Ea("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var fr=null,hr=null;function Ym(t){Nd(t,0)}function ba(t){var e=ar(t);if(Nc(e))return t}function Zc(t,e){if(t==="change")return e}var Kc=!1;if(sn){var zu;if(sn){var qu="oninput"in document;if(!qu){var Pc=document.createElement("div");Pc.setAttribute("oninput","return;"),qu=typeof Pc.oninput=="function"}zu=qu}else zu=!1;Kc=zu&&(!document.documentMode||9<document.documentMode)}function Jc(){fr&&(fr.detachEvent("onpropertychange",kc),hr=fr=null)}function kc(t){if(t.propertyName==="value"&&ba(hr)){var e=[];Qc(e,hr,t,Mu(t)),Uc(Ym,e)}}function Vm(t,e,n){t==="focusin"?(Jc(),fr=e,hr=n,fr.attachEvent("onpropertychange",kc)):t==="focusout"&&Jc()}function Fm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ba(hr)}function Gm(t,e){if(t==="click")return ba(e)}function Im(t,e){if(t==="input"||t==="change")return ba(e)}function Qm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Re=typeof Object.is=="function"?Object.is:Qm;function dr(t,e){if(Re(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var r=n[i];if(!Yt.call(e,r)||!Re(t[r],e[r]))return!1}return!0}function Wc(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function $c(t,e){var n=Wc(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Wc(n)}}function tf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?tf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function ef(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=pa(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=pa(t.document)}return e}function Hu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Zm=sn&&"documentMode"in document&&11>=document.documentMode,wi=null,Yu=null,pr=null,Vu=!1;function nf(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Vu||wi==null||wi!==pa(i)||(i=wi,"selectionStart"in i&&Hu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),pr&&dr(pr,i)||(pr=i,i=ll(Yu,"onSelect"),0<i.length&&(e=new Ea("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=wi)))}function kn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ni={animationend:kn("Animation","AnimationEnd"),animationiteration:kn("Animation","AnimationIteration"),animationstart:kn("Animation","AnimationStart"),transitionrun:kn("Transition","TransitionRun"),transitionstart:kn("Transition","TransitionStart"),transitioncancel:kn("Transition","TransitionCancel"),transitionend:kn("Transition","TransitionEnd")},Fu={},rf={};sn&&(rf=document.createElement("div").style,"AnimationEvent"in window||(delete Ni.animationend.animation,delete Ni.animationiteration.animation,delete Ni.animationstart.animation),"TransitionEvent"in window||delete Ni.transitionend.transition);function Wn(t){if(Fu[t])return Fu[t];if(!Ni[t])return t;var e=Ni[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in rf)return Fu[t]=e[n];return t}var af=Wn("animationend"),lf=Wn("animationiteration"),uf=Wn("animationstart"),Km=Wn("transitionrun"),Pm=Wn("transitionstart"),Jm=Wn("transitioncancel"),of=Wn("transitionend"),sf=new Map,Gu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Gu.push("scrollEnd");function Ke(t,e){sf.set(t,e),Pn(e,[t])}var cf=new WeakMap;function He(t,e){if(typeof t=="object"&&t!==null){var n=cf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Oc(e)},cf.set(t,e),e)}return{value:t,source:e,stack:Oc(e)}}var Ye=[],xi=0,Iu=0;function Sa(){for(var t=xi,e=Iu=xi=0;e<t;){var n=Ye[e];Ye[e++]=null;var i=Ye[e];Ye[e++]=null;var r=Ye[e];Ye[e++]=null;var a=Ye[e];if(Ye[e++]=null,i!==null&&r!==null){var m=i.pending;m===null?r.next=r:(r.next=m.next,m.next=r),i.pending=r}a!==0&&ff(n,r,a)}}function Da(t,e,n,i){Ye[xi++]=t,Ye[xi++]=e,Ye[xi++]=n,Ye[xi++]=i,Iu|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function Qu(t,e,n,i){return Da(t,e,n,i),Aa(t)}function Mi(t,e){return Da(t,null,null,e),Aa(t)}function ff(t,e,n){t.lanes|=n;var i=t.alternate;i!==null&&(i.lanes|=n);for(var r=!1,a=t.return;a!==null;)a.childLanes|=n,i=a.alternate,i!==null&&(i.childLanes|=n),a.tag===22&&(t=a.stateNode,t===null||t._visibility&1||(r=!0)),t=a,a=a.return;return t.tag===3?(a=t.stateNode,r&&e!==null&&(r=31-ge(n),t=a.hiddenUpdates,i=t[r],i===null?t[r]=[e]:i.push(e),e.lane=n|536870912),a):null}function Aa(t){if(50<qr)throw qr=0,$o=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var _i={};function km(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ce(t,e,n,i){return new km(t,e,n,i)}function Zu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function cn(t,e){var n=t.alternate;return n===null?(n=Ce(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function hf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Oa(t,e,n,i,r,a){var m=0;if(i=t,typeof t=="function")Zu(t)&&(m=1);else if(typeof t=="string")m=$0(t,n,dt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case v:return t=Ce(31,n,e,r),t.elementType=v,t.lanes=a,t;case N:return $n(n.children,r,a,e);case X:m=8,r|=24;break;case B:return t=Ce(12,n,e,r|2),t.elementType=B,t.lanes=a,t;case j:return t=Ce(13,n,e,r),t.elementType=j,t.lanes=a,t;case W:return t=Ce(19,n,e,r),t.elementType=W,t.lanes=a,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case C:case E:m=10;break t;case _:m=9;break t;case U:m=11;break t;case ut:m=14;break t;case $:m=16,i=null;break t}m=29,n=Error(o(130,t===null?"null":typeof t,"")),i=null}return e=Ce(m,n,e,r),e.elementType=t,e.type=i,e.lanes=a,e}function $n(t,e,n,i){return t=Ce(7,t,i,e),t.lanes=n,t}function Ku(t,e,n){return t=Ce(6,t,null,e),t.lanes=n,t}function Pu(t,e,n){return e=Ce(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ri=[],Ci=0,wa=null,Na=0,Ve=[],Fe=0,ti=null,fn=1,hn="";function ei(t,e){Ri[Ci++]=Na,Ri[Ci++]=wa,wa=t,Na=e}function df(t,e,n){Ve[Fe++]=fn,Ve[Fe++]=hn,Ve[Fe++]=ti,ti=t;var i=fn;t=hn;var r=32-ge(i)-1;i&=~(1<<r),n+=1;var a=32-ge(e)+r;if(30<a){var m=r-r%5;a=(i&(1<<m)-1).toString(32),i>>=m,r-=m,fn=1<<32-ge(e)+r|n<<r|i,hn=a+t}else fn=1<<a|n<<r|i,hn=t}function Ju(t){t.return!==null&&(ei(t,1),df(t,1,0))}function ku(t){for(;t===wa;)wa=Ri[--Ci],Ri[Ci]=null,Na=Ri[--Ci],Ri[Ci]=null;for(;t===ti;)ti=Ve[--Fe],Ve[Fe]=null,hn=Ve[--Fe],Ve[Fe]=null,fn=Ve[--Fe],Ve[Fe]=null}var Se=null,$t=null,Xt=!1,ni=null,We=!1,Wu=Error(o(519));function ii(t){var e=Error(o(418,""));throw gr(He(e,t)),Wu}function pf(t){var e=t.stateNode,n=t.type,i=t.memoizedProps;switch(e[ve]=t,e[De]=i,n){case"dialog":_t("cancel",e),_t("close",e);break;case"iframe":case"object":case"embed":_t("load",e);break;case"video":case"audio":for(n=0;n<Yr.length;n++)_t(Yr[n],e);break;case"source":_t("error",e);break;case"img":case"image":case"link":_t("error",e),_t("load",e);break;case"details":_t("toggle",e);break;case"input":_t("invalid",e),xc(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),da(e);break;case"select":_t("invalid",e);break;case"textarea":_t("invalid",e),_c(e,i.value,i.defaultValue,i.children),da(e)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||i.suppressHydrationWarning===!0||Rd(e.textContent,n)?(i.popover!=null&&(_t("beforetoggle",e),_t("toggle",e)),i.onScroll!=null&&_t("scroll",e),i.onScrollEnd!=null&&_t("scrollend",e),i.onClick!=null&&(e.onclick=ul),e=!0):e=!1,e||ii(t)}function yf(t){for(Se=t.return;Se;)switch(Se.tag){case 5:case 13:We=!1;return;case 27:case 3:We=!0;return;default:Se=Se.return}}function yr(t){if(t!==Se)return!1;if(!Xt)return yf(t),Xt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ys(t.type,t.memoizedProps)),n=!n),n&&$t&&ii(t),yf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){$t=Je(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}$t=null}}else e===27?(e=$t,Yn(t.type)?(t=Es,Es=null,$t=t):$t=e):$t=Se?Je(t.stateNode.nextSibling):null;return!0}function mr(){$t=Se=null,Xt=!1}function mf(){var t=ni;return t!==null&&(Ne===null?Ne=t:Ne.push.apply(Ne,t),ni=null),t}function gr(t){ni===null?ni=[t]:ni.push(t)}var $u=P(null),ri=null,dn=null;function wn(t,e,n){ft($u,e._currentValue),e._currentValue=n}function pn(t){t._currentValue=$u.current,ct($u)}function to(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function eo(t,e,n,i){var r=t.child;for(r!==null&&(r.return=t);r!==null;){var a=r.dependencies;if(a!==null){var m=r.child;a=a.firstContext;t:for(;a!==null;){var D=a;a=r;for(var L=0;L<e.length;L++)if(D.context===e[L]){a.lanes|=n,D=a.alternate,D!==null&&(D.lanes|=n),to(a.return,n,t),i||(m=null);break t}a=D.next}}else if(r.tag===18){if(m=r.return,m===null)throw Error(o(341));m.lanes|=n,a=m.alternate,a!==null&&(a.lanes|=n),to(m,n,t),m=null}else m=r.child;if(m!==null)m.return=r;else for(m=r;m!==null;){if(m===t){m=null;break}if(r=m.sibling,r!==null){r.return=m.return,m=r;break}m=m.return}r=m}}function vr(t,e,n,i){t=null;for(var r=e,a=!1;r!==null;){if(!a){if((r.flags&524288)!==0)a=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var m=r.alternate;if(m===null)throw Error(o(387));if(m=m.memoizedProps,m!==null){var D=r.type;Re(r.pendingProps.value,m.value)||(t!==null?t.push(D):t=[D])}}else if(r===O.current){if(m=r.alternate,m===null)throw Error(o(387));m.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(t!==null?t.push(Zr):t=[Zr])}r=r.return}t!==null&&eo(e,t,n,i),e.flags|=262144}function xa(t){for(t=t.firstContext;t!==null;){if(!Re(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ai(t){ri=t,dn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ee(t){return gf(ri,t)}function Ma(t,e){return ri===null&&ai(t),gf(t,e)}function gf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},dn===null){if(t===null)throw Error(o(308));dn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else dn=dn.next=e;return n}var Wm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},$m=u.unstable_scheduleCallback,t0=u.unstable_NormalPriority,ue={$$typeof:E,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function no(){return{controller:new Wm,data:new Map,refCount:0}}function Er(t){t.refCount--,t.refCount===0&&$m(t0,function(){t.controller.abort()})}var Tr=null,io=0,Li=0,Ui=null;function e0(t,e){if(Tr===null){var n=Tr=[];io=0,Li=ls(),Ui={status:"pending",value:void 0,then:function(i){n.push(i)}}}return io++,e.then(vf,vf),e}function vf(){if(--io===0&&Tr!==null){Ui!==null&&(Ui.status="fulfilled");var t=Tr;Tr=null,Li=0,Ui=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function n0(t,e){var n=[],i={status:"pending",value:null,reason:null,then:function(r){n.push(r)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var r=0;r<n.length;r++)(0,n[r])(e)},function(r){for(i.status="rejected",i.reason=r,r=0;r<n.length;r++)(0,n[r])(void 0)}),i}var Ef=I.S;I.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&e0(t,e),Ef!==null&&Ef(t,e)};var li=P(null);function ro(){var t=li.current;return t!==null?t:Zt.pooledCache}function _a(t,e){e===null?ft(li,li.current):ft(li,e.pool)}function Tf(){var t=ro();return t===null?null:{parent:ue._currentValue,pool:t}}var br=Error(o(460)),bf=Error(o(474)),Ra=Error(o(542)),ao={then:function(){}};function Sf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ca(){}function Df(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ca,Ca),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Of(t),t;default:if(typeof e.status=="string")e.then(Ca,Ca);else{if(t=Zt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var r=e;r.status="fulfilled",r.value=i}},function(i){if(e.status==="pending"){var r=e;r.status="rejected",r.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Of(t),t}throw Sr=e,br}}var Sr=null;function Af(){if(Sr===null)throw Error(o(459));var t=Sr;return Sr=null,t}function Of(t){if(t===br||t===Ra)throw Error(o(483))}var Nn=!1;function lo(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function uo(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function xn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Mn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(qt&2)!==0){var r=i.pending;return r===null?e.next=e:(e.next=r.next,r.next=e),i.pending=e,e=Aa(t),ff(t,null,n),e}return Da(t,i,e,n),Aa(t)}function Dr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,gc(t,n)}}function oo(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var r=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var m={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};a===null?r=a=m:a=a.next=m,n=n.next}while(n!==null);a===null?r=a=e:a=a.next=e}else r=a=e;n={baseState:i.baseState,firstBaseUpdate:r,lastBaseUpdate:a,shared:i.shared,callbacks:i.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var so=!1;function Ar(){if(so){var t=Ui;if(t!==null)throw t}}function Or(t,e,n,i){so=!1;var r=t.updateQueue;Nn=!1;var a=r.firstBaseUpdate,m=r.lastBaseUpdate,D=r.shared.pending;if(D!==null){r.shared.pending=null;var L=D,G=L.next;L.next=null,m===null?a=G:m.next=G,m=L;var k=t.alternate;k!==null&&(k=k.updateQueue,D=k.lastBaseUpdate,D!==m&&(D===null?k.firstBaseUpdate=G:D.next=G,k.lastBaseUpdate=L))}if(a!==null){var nt=r.baseState;m=0,k=G=L=null,D=a;do{var Q=D.lane&-536870913,Z=Q!==D.lane;if(Z?(Ut&Q)===Q:(i&Q)===Q){Q!==0&&Q===Li&&(so=!0),k!==null&&(k=k.next={lane:0,tag:D.tag,payload:D.payload,callback:null,next:null});t:{var Et=t,mt=D;Q=e;var Gt=n;switch(mt.tag){case 1:if(Et=mt.payload,typeof Et=="function"){nt=Et.call(Gt,nt,Q);break t}nt=Et;break t;case 3:Et.flags=Et.flags&-65537|128;case 0:if(Et=mt.payload,Q=typeof Et=="function"?Et.call(Gt,nt,Q):Et,Q==null)break t;nt=S({},nt,Q);break t;case 2:Nn=!0}}Q=D.callback,Q!==null&&(t.flags|=64,Z&&(t.flags|=8192),Z=r.callbacks,Z===null?r.callbacks=[Q]:Z.push(Q))}else Z={lane:Q,tag:D.tag,payload:D.payload,callback:D.callback,next:null},k===null?(G=k=Z,L=nt):k=k.next=Z,m|=Q;if(D=D.next,D===null){if(D=r.shared.pending,D===null)break;Z=D,D=Z.next,Z.next=null,r.lastBaseUpdate=Z,r.shared.pending=null}}while(!0);k===null&&(L=nt),r.baseState=L,r.firstBaseUpdate=G,r.lastBaseUpdate=k,a===null&&(r.shared.lanes=0),Xn|=m,t.lanes=m,t.memoizedState=nt}}function wf(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function Nf(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)wf(n[t],e)}var Bi=P(null),La=P(0);function xf(t,e){t=bn,ft(La,t),ft(Bi,e),bn=t|e.baseLanes}function co(){ft(La,bn),ft(Bi,Bi.current)}function fo(){bn=La.current,ct(Bi),ct(La)}var _n=0,Ot=null,Vt=null,re=null,Ua=!1,ji=!1,ui=!1,Ba=0,wr=0,Xi=null,i0=0;function ne(){throw Error(o(321))}function ho(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Re(t[n],e[n]))return!1;return!0}function po(t,e,n,i,r,a){return _n=a,Ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,I.H=t===null||t.memoizedState===null?fh:hh,ui=!1,a=n(i,r),ui=!1,ji&&(a=_f(e,n,i,r)),Mf(t),a}function Mf(t){I.H=Ya;var e=Vt!==null&&Vt.next!==null;if(_n=0,re=Vt=Ot=null,Ua=!1,wr=0,Xi=null,e)throw Error(o(300));t===null||fe||(t=t.dependencies,t!==null&&xa(t)&&(fe=!0))}function _f(t,e,n,i){Ot=t;var r=0;do{if(ji&&(Xi=null),wr=0,ji=!1,25<=r)throw Error(o(301));if(r+=1,re=Vt=null,t.updateQueue!=null){var a=t.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,a.memoCache!=null&&(a.memoCache.index=0)}I.H=c0,a=e(n,i)}while(ji);return a}function r0(){var t=I.H,e=t.useState()[0];return e=typeof e.then=="function"?Nr(e):e,t=t.useState()[0],(Vt!==null?Vt.memoizedState:null)!==t&&(Ot.flags|=1024),e}function yo(){var t=Ba!==0;return Ba=0,t}function mo(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function go(t){if(Ua){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Ua=!1}_n=0,re=Vt=Ot=null,ji=!1,wr=Ba=0,Xi=null}function Oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return re===null?Ot.memoizedState=re=t:re=re.next=t,re}function ae(){if(Vt===null){var t=Ot.alternate;t=t!==null?t.memoizedState:null}else t=Vt.next;var e=re===null?Ot.memoizedState:re.next;if(e!==null)re=e,Vt=t;else{if(t===null)throw Ot.alternate===null?Error(o(467)):Error(o(310));Vt=t,t={memoizedState:Vt.memoizedState,baseState:Vt.baseState,baseQueue:Vt.baseQueue,queue:Vt.queue,next:null},re===null?Ot.memoizedState=re=t:re=re.next=t}return re}function vo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Nr(t){var e=wr;return wr+=1,Xi===null&&(Xi=[]),t=Df(Xi,t,e),e=Ot,(re===null?e.memoizedState:re.next)===null&&(e=e.alternate,I.H=e===null||e.memoizedState===null?fh:hh),t}function ja(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Nr(t);if(t.$$typeof===E)return Ee(t)}throw Error(o(438,String(t)))}function Eo(t){var e=null,n=Ot.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var i=Ot.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(r){return r.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=vo(),Ot.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),i=0;i<t;i++)n[i]=J;return e.index++,n}function yn(t,e){return typeof e=="function"?e(t):e}function Xa(t){var e=ae();return To(e,Vt,t)}function To(t,e,n){var i=t.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=n;var r=t.baseQueue,a=i.pending;if(a!==null){if(r!==null){var m=r.next;r.next=a.next,a.next=m}e.baseQueue=r=a,i.pending=null}if(a=t.baseState,r===null)t.memoizedState=a;else{e=r.next;var D=m=null,L=null,G=e,k=!1;do{var nt=G.lane&-536870913;if(nt!==G.lane?(Ut&nt)===nt:(_n&nt)===nt){var Q=G.revertLane;if(Q===0)L!==null&&(L=L.next={lane:0,revertLane:0,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null}),nt===Li&&(k=!0);else if((_n&Q)===Q){G=G.next,Q===Li&&(k=!0);continue}else nt={lane:0,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},L===null?(D=L=nt,m=a):L=L.next=nt,Ot.lanes|=Q,Xn|=Q;nt=G.action,ui&&n(a,nt),a=G.hasEagerState?G.eagerState:n(a,nt)}else Q={lane:nt,revertLane:G.revertLane,action:G.action,hasEagerState:G.hasEagerState,eagerState:G.eagerState,next:null},L===null?(D=L=Q,m=a):L=L.next=Q,Ot.lanes|=nt,Xn|=nt;G=G.next}while(G!==null&&G!==e);if(L===null?m=a:L.next=D,!Re(a,t.memoizedState)&&(fe=!0,k&&(n=Ui,n!==null)))throw n;t.memoizedState=a,t.baseState=m,t.baseQueue=L,i.lastRenderedState=a}return r===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function bo(t){var e=ae(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var i=n.dispatch,r=n.pending,a=e.memoizedState;if(r!==null){n.pending=null;var m=r=r.next;do a=t(a,m.action),m=m.next;while(m!==r);Re(a,e.memoizedState)||(fe=!0),e.memoizedState=a,e.baseQueue===null&&(e.baseState=a),n.lastRenderedState=a}return[a,i]}function Rf(t,e,n){var i=Ot,r=ae(),a=Xt;if(a){if(n===void 0)throw Error(o(407));n=n()}else n=e();var m=!Re((Vt||r).memoizedState,n);m&&(r.memoizedState=n,fe=!0),r=r.queue;var D=Uf.bind(null,i,r,t);if(xr(2048,8,D,[t]),r.getSnapshot!==e||m||re!==null&&re.memoizedState.tag&1){if(i.flags|=2048,zi(9,za(),Lf.bind(null,i,r,n,e),null),Zt===null)throw Error(o(349));a||(_n&124)!==0||Cf(i,e,n)}return n}function Cf(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=Ot.updateQueue,e===null?(e=vo(),Ot.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Lf(t,e,n,i){e.value=n,e.getSnapshot=i,Bf(e)&&jf(t)}function Uf(t,e,n){return n(function(){Bf(e)&&jf(t)})}function Bf(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Re(t,n)}catch{return!0}}function jf(t){var e=Mi(t,2);e!==null&&Xe(e,t,2)}function So(t){var e=Oe();if(typeof t=="function"){var n=t;if(t=n(),ui){_e(!0);try{n()}finally{_e(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:t},e}function Xf(t,e,n,i){return t.baseState=n,To(t,Vt,typeof i=="function"?i:yn)}function a0(t,e,n,i,r){if(Ha(t))throw Error(o(485));if(t=e.action,t!==null){var a={payload:r,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){a.listeners.push(m)}};I.T!==null?n(!0):a.isTransition=!1,i(a),n=e.pending,n===null?(a.next=e.pending=a,zf(e,a)):(a.next=n.next,e.pending=n.next=a)}}function zf(t,e){var n=e.action,i=e.payload,r=t.state;if(e.isTransition){var a=I.T,m={};I.T=m;try{var D=n(r,i),L=I.S;L!==null&&L(m,D),qf(t,e,D)}catch(G){Do(t,e,G)}finally{I.T=a}}else try{a=n(r,i),qf(t,e,a)}catch(G){Do(t,e,G)}}function qf(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Hf(t,e,i)},function(i){return Do(t,e,i)}):Hf(t,e,n)}function Hf(t,e,n){e.status="fulfilled",e.value=n,Yf(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,zf(t,n)))}function Do(t,e,n){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=n,Yf(e),e=e.next;while(e!==i)}t.action=null}function Yf(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Vf(t,e){return e}function Ff(t,e){if(Xt){var n=Zt.formState;if(n!==null){t:{var i=Ot;if(Xt){if($t){e:{for(var r=$t,a=We;r.nodeType!==8;){if(!a){r=null;break e}if(r=Je(r.nextSibling),r===null){r=null;break e}}a=r.data,r=a==="F!"||a==="F"?r:null}if(r){$t=Je(r.nextSibling),i=r.data==="F!";break t}}ii(i)}i=!1}i&&(e=n[0])}}return n=Oe(),n.memoizedState=n.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vf,lastRenderedState:e},n.queue=i,n=oh.bind(null,Ot,i),i.dispatch=n,i=So(!1),a=xo.bind(null,Ot,!1,i.queue),i=Oe(),r={state:e,dispatch:null,action:t,pending:null},i.queue=r,n=a0.bind(null,Ot,r,a,n),r.dispatch=n,i.memoizedState=t,[e,n,!1]}function Gf(t){var e=ae();return If(e,Vt,t)}function If(t,e,n){if(e=To(t,e,Vf)[0],t=Xa(yn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=Nr(e)}catch(m){throw m===br?Ra:m}else i=e;e=ae();var r=e.queue,a=r.dispatch;return n!==e.memoizedState&&(Ot.flags|=2048,zi(9,za(),l0.bind(null,r,n),null)),[i,a,t]}function l0(t,e){t.action=e}function Qf(t){var e=ae(),n=Vt;if(n!==null)return If(e,n,t);ae(),e=e.memoizedState,n=ae();var i=n.queue.dispatch;return n.memoizedState=t,[e,i,!1]}function zi(t,e,n,i){return t={tag:t,create:n,deps:i,inst:e,next:null},e=Ot.updateQueue,e===null&&(e=vo(),Ot.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t),t}function za(){return{destroy:void 0,resource:void 0}}function Zf(){return ae().memoizedState}function qa(t,e,n,i){var r=Oe();i=i===void 0?null:i,Ot.flags|=t,r.memoizedState=zi(1|e,za(),n,i)}function xr(t,e,n,i){var r=ae();i=i===void 0?null:i;var a=r.memoizedState.inst;Vt!==null&&i!==null&&ho(i,Vt.memoizedState.deps)?r.memoizedState=zi(e,a,n,i):(Ot.flags|=t,r.memoizedState=zi(1|e,a,n,i))}function Kf(t,e){qa(8390656,8,t,e)}function Pf(t,e){xr(2048,8,t,e)}function Jf(t,e){return xr(4,2,t,e)}function kf(t,e){return xr(4,4,t,e)}function Wf(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function $f(t,e,n){n=n!=null?n.concat([t]):null,xr(4,4,Wf.bind(null,e,t),n)}function Ao(){}function th(t,e){var n=ae();e=e===void 0?null:e;var i=n.memoizedState;return e!==null&&ho(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function eh(t,e){var n=ae();e=e===void 0?null:e;var i=n.memoizedState;if(e!==null&&ho(e,i[1]))return i[0];if(i=t(),ui){_e(!0);try{t()}finally{_e(!1)}}return n.memoizedState=[i,e],i}function Oo(t,e,n){return n===void 0||(_n&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=rd(),Ot.lanes|=t,Xn|=t,n)}function nh(t,e,n,i){return Re(n,e)?n:Bi.current!==null?(t=Oo(t,n,i),Re(t,e)||(fe=!0),t):(_n&42)===0?(fe=!0,t.memoizedState=n):(t=rd(),Ot.lanes|=t,Xn|=t,e)}function ih(t,e,n,i,r){var a=rt.p;rt.p=a!==0&&8>a?a:8;var m=I.T,D={};I.T=D,xo(t,!1,e,n);try{var L=r(),G=I.S;if(G!==null&&G(D,L),L!==null&&typeof L=="object"&&typeof L.then=="function"){var k=n0(L,i);Mr(t,e,k,je(t))}else Mr(t,e,i,je(t))}catch(nt){Mr(t,e,{then:function(){},status:"rejected",reason:nt},je())}finally{rt.p=a,I.T=m}}function u0(){}function wo(t,e,n,i){if(t.tag!==5)throw Error(o(476));var r=rh(t).queue;ih(t,r,e,yt,n===null?u0:function(){return ah(t),n(i)})}function rh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:yt,baseState:yt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:yt},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:yn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ah(t){var e=rh(t).next.queue;Mr(t,e,{},je())}function No(){return Ee(Zr)}function lh(){return ae().memoizedState}function uh(){return ae().memoizedState}function o0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=je();t=xn(n);var i=Mn(e,t,n);i!==null&&(Xe(i,e,n),Dr(i,e,n)),e={cache:no()},t.payload=e;return}e=e.return}}function s0(t,e,n){var i=je();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ha(t)?sh(e,n):(n=Qu(t,e,n,i),n!==null&&(Xe(n,t,i),ch(n,e,i)))}function oh(t,e,n){var i=je();Mr(t,e,n,i)}function Mr(t,e,n,i){var r={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ha(t))sh(e,r);else{var a=t.alternate;if(t.lanes===0&&(a===null||a.lanes===0)&&(a=e.lastRenderedReducer,a!==null))try{var m=e.lastRenderedState,D=a(m,n);if(r.hasEagerState=!0,r.eagerState=D,Re(D,m))return Da(t,e,r,0),Zt===null&&Sa(),!1}catch{}finally{}if(n=Qu(t,e,r,i),n!==null)return Xe(n,t,i),ch(n,e,i),!0}return!1}function xo(t,e,n,i){if(i={lane:2,revertLane:ls(),action:i,hasEagerState:!1,eagerState:null,next:null},Ha(t)){if(e)throw Error(o(479))}else e=Qu(t,n,i,2),e!==null&&Xe(e,t,2)}function Ha(t){var e=t.alternate;return t===Ot||e!==null&&e===Ot}function sh(t,e){ji=Ua=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function ch(t,e,n){if((n&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,gc(t,n)}}var Ya={readContext:Ee,use:ja,useCallback:ne,useContext:ne,useEffect:ne,useImperativeHandle:ne,useLayoutEffect:ne,useInsertionEffect:ne,useMemo:ne,useReducer:ne,useRef:ne,useState:ne,useDebugValue:ne,useDeferredValue:ne,useTransition:ne,useSyncExternalStore:ne,useId:ne,useHostTransitionStatus:ne,useFormState:ne,useActionState:ne,useOptimistic:ne,useMemoCache:ne,useCacheRefresh:ne},fh={readContext:Ee,use:ja,useCallback:function(t,e){return Oe().memoizedState=[t,e===void 0?null:e],t},useContext:Ee,useEffect:Kf,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,qa(4194308,4,Wf.bind(null,e,t),n)},useLayoutEffect:function(t,e){return qa(4194308,4,t,e)},useInsertionEffect:function(t,e){qa(4,2,t,e)},useMemo:function(t,e){var n=Oe();e=e===void 0?null:e;var i=t();if(ui){_e(!0);try{t()}finally{_e(!1)}}return n.memoizedState=[i,e],i},useReducer:function(t,e,n){var i=Oe();if(n!==void 0){var r=n(e);if(ui){_e(!0);try{n(e)}finally{_e(!1)}}}else r=e;return i.memoizedState=i.baseState=r,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:r},i.queue=t,t=t.dispatch=s0.bind(null,Ot,t),[i.memoizedState,t]},useRef:function(t){var e=Oe();return t={current:t},e.memoizedState=t},useState:function(t){t=So(t);var e=t.queue,n=oh.bind(null,Ot,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Ao,useDeferredValue:function(t,e){var n=Oe();return Oo(n,t,e)},useTransition:function(){var t=So(!1);return t=ih.bind(null,Ot,t.queue,!0,!1),Oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var i=Ot,r=Oe();if(Xt){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),Zt===null)throw Error(o(349));(Ut&124)!==0||Cf(i,e,n)}r.memoizedState=n;var a={value:n,getSnapshot:e};return r.queue=a,Kf(Uf.bind(null,i,a,t),[t]),i.flags|=2048,zi(9,za(),Lf.bind(null,i,a,n,e),null),n},useId:function(){var t=Oe(),e=Zt.identifierPrefix;if(Xt){var n=hn,i=fn;n=(i&~(1<<32-ge(i)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ba++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=i0++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:No,useFormState:Ff,useActionState:Ff,useOptimistic:function(t){var e=Oe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=xo.bind(null,Ot,!0,n),n.dispatch=e,[t,e]},useMemoCache:Eo,useCacheRefresh:function(){return Oe().memoizedState=o0.bind(null,Ot)}},hh={readContext:Ee,use:ja,useCallback:th,useContext:Ee,useEffect:Pf,useImperativeHandle:$f,useInsertionEffect:Jf,useLayoutEffect:kf,useMemo:eh,useReducer:Xa,useRef:Zf,useState:function(){return Xa(yn)},useDebugValue:Ao,useDeferredValue:function(t,e){var n=ae();return nh(n,Vt.memoizedState,t,e)},useTransition:function(){var t=Xa(yn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Nr(t),e]},useSyncExternalStore:Rf,useId:lh,useHostTransitionStatus:No,useFormState:Gf,useActionState:Gf,useOptimistic:function(t,e){var n=ae();return Xf(n,Vt,t,e)},useMemoCache:Eo,useCacheRefresh:uh},c0={readContext:Ee,use:ja,useCallback:th,useContext:Ee,useEffect:Pf,useImperativeHandle:$f,useInsertionEffect:Jf,useLayoutEffect:kf,useMemo:eh,useReducer:bo,useRef:Zf,useState:function(){return bo(yn)},useDebugValue:Ao,useDeferredValue:function(t,e){var n=ae();return Vt===null?Oo(n,t,e):nh(n,Vt.memoizedState,t,e)},useTransition:function(){var t=bo(yn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:Nr(t),e]},useSyncExternalStore:Rf,useId:lh,useHostTransitionStatus:No,useFormState:Qf,useActionState:Qf,useOptimistic:function(t,e){var n=ae();return Vt!==null?Xf(n,Vt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Eo,useCacheRefresh:uh},qi=null,_r=0;function Va(t){var e=_r;return _r+=1,qi===null&&(qi=[]),Df(qi,t,e)}function Rr(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Fa(t,e){throw e.$$typeof===A?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function dh(t){var e=t._init;return e(t._payload)}function ph(t){function e(H,q){if(t){var F=H.deletions;F===null?(H.deletions=[q],H.flags|=16):F.push(q)}}function n(H,q){if(!t)return null;for(;q!==null;)e(H,q),q=q.sibling;return null}function i(H){for(var q=new Map;H!==null;)H.key!==null?q.set(H.key,H):q.set(H.index,H),H=H.sibling;return q}function r(H,q){return H=cn(H,q),H.index=0,H.sibling=null,H}function a(H,q,F){return H.index=F,t?(F=H.alternate,F!==null?(F=F.index,F<q?(H.flags|=67108866,q):F):(H.flags|=67108866,q)):(H.flags|=1048576,q)}function m(H){return t&&H.alternate===null&&(H.flags|=67108866),H}function D(H,q,F,et){return q===null||q.tag!==6?(q=Ku(F,H.mode,et),q.return=H,q):(q=r(q,F),q.return=H,q)}function L(H,q,F,et){var ht=F.type;return ht===N?k(H,q,F.props.children,et,F.key):q!==null&&(q.elementType===ht||typeof ht=="object"&&ht!==null&&ht.$$typeof===$&&dh(ht)===q.type)?(q=r(q,F.props),Rr(q,F),q.return=H,q):(q=Oa(F.type,F.key,F.props,null,H.mode,et),Rr(q,F),q.return=H,q)}function G(H,q,F,et){return q===null||q.tag!==4||q.stateNode.containerInfo!==F.containerInfo||q.stateNode.implementation!==F.implementation?(q=Pu(F,H.mode,et),q.return=H,q):(q=r(q,F.children||[]),q.return=H,q)}function k(H,q,F,et,ht){return q===null||q.tag!==7?(q=$n(F,H.mode,et,ht),q.return=H,q):(q=r(q,F),q.return=H,q)}function nt(H,q,F){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return q=Ku(""+q,H.mode,F),q.return=H,q;if(typeof q=="object"&&q!==null){switch(q.$$typeof){case x:return F=Oa(q.type,q.key,q.props,null,H.mode,F),Rr(F,q),F.return=H,F;case M:return q=Pu(q,H.mode,F),q.return=H,q;case $:var et=q._init;return q=et(q._payload),nt(H,q,F)}if(St(q)||st(q))return q=$n(q,H.mode,F,null),q.return=H,q;if(typeof q.then=="function")return nt(H,Va(q),F);if(q.$$typeof===E)return nt(H,Ma(H,q),F);Fa(H,q)}return null}function Q(H,q,F,et){var ht=q!==null?q.key:null;if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return ht!==null?null:D(H,q,""+F,et);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case x:return F.key===ht?L(H,q,F,et):null;case M:return F.key===ht?G(H,q,F,et):null;case $:return ht=F._init,F=ht(F._payload),Q(H,q,F,et)}if(St(F)||st(F))return ht!==null?null:k(H,q,F,et,null);if(typeof F.then=="function")return Q(H,q,Va(F),et);if(F.$$typeof===E)return Q(H,q,Ma(H,F),et);Fa(H,F)}return null}function Z(H,q,F,et,ht){if(typeof et=="string"&&et!==""||typeof et=="number"||typeof et=="bigint")return H=H.get(F)||null,D(q,H,""+et,ht);if(typeof et=="object"&&et!==null){switch(et.$$typeof){case x:return H=H.get(et.key===null?F:et.key)||null,L(q,H,et,ht);case M:return H=H.get(et.key===null?F:et.key)||null,G(q,H,et,ht);case $:var Nt=et._init;return et=Nt(et._payload),Z(H,q,F,et,ht)}if(St(et)||st(et))return H=H.get(F)||null,k(q,H,et,ht,null);if(typeof et.then=="function")return Z(H,q,F,Va(et),ht);if(et.$$typeof===E)return Z(H,q,F,Ma(q,et),ht);Fa(q,et)}return null}function Et(H,q,F,et){for(var ht=null,Nt=null,pt=q,gt=q=0,de=null;pt!==null&&gt<F.length;gt++){pt.index>gt?(de=pt,pt=null):de=pt.sibling;var jt=Q(H,pt,F[gt],et);if(jt===null){pt===null&&(pt=de);break}t&&pt&&jt.alternate===null&&e(H,pt),q=a(jt,q,gt),Nt===null?ht=jt:Nt.sibling=jt,Nt=jt,pt=de}if(gt===F.length)return n(H,pt),Xt&&ei(H,gt),ht;if(pt===null){for(;gt<F.length;gt++)pt=nt(H,F[gt],et),pt!==null&&(q=a(pt,q,gt),Nt===null?ht=pt:Nt.sibling=pt,Nt=pt);return Xt&&ei(H,gt),ht}for(pt=i(pt);gt<F.length;gt++)de=Z(pt,H,gt,F[gt],et),de!==null&&(t&&de.alternate!==null&&pt.delete(de.key===null?gt:de.key),q=a(de,q,gt),Nt===null?ht=de:Nt.sibling=de,Nt=de);return t&&pt.forEach(function(Qn){return e(H,Qn)}),Xt&&ei(H,gt),ht}function mt(H,q,F,et){if(F==null)throw Error(o(151));for(var ht=null,Nt=null,pt=q,gt=q=0,de=null,jt=F.next();pt!==null&&!jt.done;gt++,jt=F.next()){pt.index>gt?(de=pt,pt=null):de=pt.sibling;var Qn=Q(H,pt,jt.value,et);if(Qn===null){pt===null&&(pt=de);break}t&&pt&&Qn.alternate===null&&e(H,pt),q=a(Qn,q,gt),Nt===null?ht=Qn:Nt.sibling=Qn,Nt=Qn,pt=de}if(jt.done)return n(H,pt),Xt&&ei(H,gt),ht;if(pt===null){for(;!jt.done;gt++,jt=F.next())jt=nt(H,jt.value,et),jt!==null&&(q=a(jt,q,gt),Nt===null?ht=jt:Nt.sibling=jt,Nt=jt);return Xt&&ei(H,gt),ht}for(pt=i(pt);!jt.done;gt++,jt=F.next())jt=Z(pt,H,gt,jt.value,et),jt!==null&&(t&&jt.alternate!==null&&pt.delete(jt.key===null?gt:jt.key),q=a(jt,q,gt),Nt===null?ht=jt:Nt.sibling=jt,Nt=jt);return t&&pt.forEach(function(fg){return e(H,fg)}),Xt&&ei(H,gt),ht}function Gt(H,q,F,et){if(typeof F=="object"&&F!==null&&F.type===N&&F.key===null&&(F=F.props.children),typeof F=="object"&&F!==null){switch(F.$$typeof){case x:t:{for(var ht=F.key;q!==null;){if(q.key===ht){if(ht=F.type,ht===N){if(q.tag===7){n(H,q.sibling),et=r(q,F.props.children),et.return=H,H=et;break t}}else if(q.elementType===ht||typeof ht=="object"&&ht!==null&&ht.$$typeof===$&&dh(ht)===q.type){n(H,q.sibling),et=r(q,F.props),Rr(et,F),et.return=H,H=et;break t}n(H,q);break}else e(H,q);q=q.sibling}F.type===N?(et=$n(F.props.children,H.mode,et,F.key),et.return=H,H=et):(et=Oa(F.type,F.key,F.props,null,H.mode,et),Rr(et,F),et.return=H,H=et)}return m(H);case M:t:{for(ht=F.key;q!==null;){if(q.key===ht)if(q.tag===4&&q.stateNode.containerInfo===F.containerInfo&&q.stateNode.implementation===F.implementation){n(H,q.sibling),et=r(q,F.children||[]),et.return=H,H=et;break t}else{n(H,q);break}else e(H,q);q=q.sibling}et=Pu(F,H.mode,et),et.return=H,H=et}return m(H);case $:return ht=F._init,F=ht(F._payload),Gt(H,q,F,et)}if(St(F))return Et(H,q,F,et);if(st(F)){if(ht=st(F),typeof ht!="function")throw Error(o(150));return F=ht.call(F),mt(H,q,F,et)}if(typeof F.then=="function")return Gt(H,q,Va(F),et);if(F.$$typeof===E)return Gt(H,q,Ma(H,F),et);Fa(H,F)}return typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint"?(F=""+F,q!==null&&q.tag===6?(n(H,q.sibling),et=r(q,F),et.return=H,H=et):(n(H,q),et=Ku(F,H.mode,et),et.return=H,H=et),m(H)):n(H,q)}return function(H,q,F,et){try{_r=0;var ht=Gt(H,q,F,et);return qi=null,ht}catch(pt){if(pt===br||pt===Ra)throw pt;var Nt=Ce(29,pt,null,H.mode);return Nt.lanes=et,Nt.return=H,Nt}finally{}}}var Hi=ph(!0),yh=ph(!1),Ge=P(null),$e=null;function Rn(t){var e=t.alternate;ft(oe,oe.current&1),ft(Ge,t),$e===null&&(e===null||Bi.current!==null||e.memoizedState!==null)&&($e=t)}function mh(t){if(t.tag===22){if(ft(oe,oe.current),ft(Ge,t),$e===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&($e=t)}}else Cn()}function Cn(){ft(oe,oe.current),ft(Ge,Ge.current)}function mn(t){ct(Ge),$e===t&&($e=null),ct(oe)}var oe=P(0);function Ga(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||vs(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Mo(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:S({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var _o={enqueueSetState:function(t,e,n){t=t._reactInternals;var i=je(),r=xn(i);r.payload=e,n!=null&&(r.callback=n),e=Mn(t,r,i),e!==null&&(Xe(e,t,i),Dr(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=je(),r=xn(i);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=Mn(t,r,i),e!==null&&(Xe(e,t,i),Dr(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=je(),i=xn(n);i.tag=2,e!=null&&(i.callback=e),e=Mn(t,i,n),e!==null&&(Xe(e,t,n),Dr(e,t,n))}};function gh(t,e,n,i,r,a,m){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,a,m):e.prototype&&e.prototype.isPureReactComponent?!dr(n,i)||!dr(r,a):!0}function vh(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&_o.enqueueReplaceState(e,e.state,null)}function oi(t,e){var n=e;if("ref"in e){n={};for(var i in e)i!=="ref"&&(n[i]=e[i])}if(t=t.defaultProps){n===e&&(n=S({},n));for(var r in t)n[r]===void 0&&(n[r]=t[r])}return n}var Ia=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Eh(t){Ia(t)}function Th(t){console.error(t)}function bh(t){Ia(t)}function Qa(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function Sh(t,e,n){try{var i=t.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ro(t,e,n){return n=xn(n),n.tag=3,n.payload={element:null},n.callback=function(){Qa(t,e)},n}function Dh(t){return t=xn(t),t.tag=3,t}function Ah(t,e,n,i){var r=n.type.getDerivedStateFromError;if(typeof r=="function"){var a=i.value;t.payload=function(){return r(a)},t.callback=function(){Sh(e,n,i)}}var m=n.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(t.callback=function(){Sh(e,n,i),typeof r!="function"&&(zn===null?zn=new Set([this]):zn.add(this));var D=i.stack;this.componentDidCatch(i.value,{componentStack:D!==null?D:""})})}function f0(t,e,n,i,r){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=n.alternate,e!==null&&vr(e,n,r,!0),n=Ge.current,n!==null){switch(n.tag){case 13:return $e===null?es():n.alternate===null&&te===0&&(te=3),n.flags&=-257,n.flags|=65536,n.lanes=r,i===ao?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([i]):e.add(i),is(t,i,r)),!1;case 22:return n.flags|=65536,i===ao?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([i]):n.add(i)),is(t,i,r)),!1}throw Error(o(435,n.tag))}return is(t,i,r),es(),!1}if(Xt)return e=Ge.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=r,i!==Wu&&(t=Error(o(422),{cause:i}),gr(He(t,n)))):(i!==Wu&&(e=Error(o(423),{cause:i}),gr(He(e,n))),t=t.current.alternate,t.flags|=65536,r&=-r,t.lanes|=r,i=He(i,n),r=Ro(t.stateNode,i,r),oo(t,r),te!==4&&(te=2)),!1;var a=Error(o(520),{cause:i});if(a=He(a,n),zr===null?zr=[a]:zr.push(a),te!==4&&(te=2),e===null)return!0;i=He(i,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=r&-r,n.lanes|=t,t=Ro(n.stateNode,i,t),oo(n,t),!1;case 1:if(e=n.type,a=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||a!==null&&typeof a.componentDidCatch=="function"&&(zn===null||!zn.has(a))))return n.flags|=65536,r&=-r,n.lanes|=r,r=Dh(r),Ah(r,t,n,i),oo(n,r),!1}n=n.return}while(n!==null);return!1}var Oh=Error(o(461)),fe=!1;function pe(t,e,n,i){e.child=t===null?yh(e,null,n,i):Hi(e,t.child,n,i)}function wh(t,e,n,i,r){n=n.render;var a=e.ref;if("ref"in i){var m={};for(var D in i)D!=="ref"&&(m[D]=i[D])}else m=i;return ai(e),i=po(t,e,n,m,a,r),D=yo(),t!==null&&!fe?(mo(t,e,r),gn(t,e,r)):(Xt&&D&&Ju(e),e.flags|=1,pe(t,e,i,r),e.child)}function Nh(t,e,n,i,r){if(t===null){var a=n.type;return typeof a=="function"&&!Zu(a)&&a.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=a,xh(t,e,a,i,r)):(t=Oa(n.type,null,i,e,e.mode,r),t.ref=e.ref,t.return=e,e.child=t)}if(a=t.child,!qo(t,r)){var m=a.memoizedProps;if(n=n.compare,n=n!==null?n:dr,n(m,i)&&t.ref===e.ref)return gn(t,e,r)}return e.flags|=1,t=cn(a,i),t.ref=e.ref,t.return=e,e.child=t}function xh(t,e,n,i,r){if(t!==null){var a=t.memoizedProps;if(dr(a,i)&&t.ref===e.ref)if(fe=!1,e.pendingProps=i=a,qo(t,r))(t.flags&131072)!==0&&(fe=!0);else return e.lanes=t.lanes,gn(t,e,r)}return Co(t,e,n,i,r)}function Mh(t,e,n){var i=e.pendingProps,r=i.children,a=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=a!==null?a.baseLanes|n:n,t!==null){for(r=e.child=t.child,a=0;r!==null;)a=a|r.lanes|r.childLanes,r=r.sibling;e.childLanes=a&~i}else e.childLanes=0,e.child=null;return _h(t,e,i,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&_a(e,a!==null?a.cachePool:null),a!==null?xf(e,a):co(),mh(e);else return e.lanes=e.childLanes=536870912,_h(t,e,a!==null?a.baseLanes|n:n,n)}else a!==null?(_a(e,a.cachePool),xf(e,a),Cn(),e.memoizedState=null):(t!==null&&_a(e,null),co(),Cn());return pe(t,e,r,n),e.child}function _h(t,e,n,i){var r=ro();return r=r===null?null:{parent:ue._currentValue,pool:r},e.memoizedState={baseLanes:n,cachePool:r},t!==null&&_a(e,null),co(),mh(e),t!==null&&vr(t,e,i,!0),null}function Za(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Co(t,e,n,i,r){return ai(e),n=po(t,e,n,i,void 0,r),i=yo(),t!==null&&!fe?(mo(t,e,r),gn(t,e,r)):(Xt&&i&&Ju(e),e.flags|=1,pe(t,e,n,r),e.child)}function Rh(t,e,n,i,r,a){return ai(e),e.updateQueue=null,n=_f(e,i,n,r),Mf(t),i=yo(),t!==null&&!fe?(mo(t,e,a),gn(t,e,a)):(Xt&&i&&Ju(e),e.flags|=1,pe(t,e,n,a),e.child)}function Ch(t,e,n,i,r){if(ai(e),e.stateNode===null){var a=_i,m=n.contextType;typeof m=="object"&&m!==null&&(a=Ee(m)),a=new n(i,a),e.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,a.updater=_o,e.stateNode=a,a._reactInternals=e,a=e.stateNode,a.props=i,a.state=e.memoizedState,a.refs={},lo(e),m=n.contextType,a.context=typeof m=="object"&&m!==null?Ee(m):_i,a.state=e.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(Mo(e,n,m,i),a.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(m=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),m!==a.state&&_o.enqueueReplaceState(a,a.state,null),Or(e,i,a,r),Ar(),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){a=e.stateNode;var D=e.memoizedProps,L=oi(n,D);a.props=L;var G=a.context,k=n.contextType;m=_i,typeof k=="object"&&k!==null&&(m=Ee(k));var nt=n.getDerivedStateFromProps;k=typeof nt=="function"||typeof a.getSnapshotBeforeUpdate=="function",D=e.pendingProps!==D,k||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(D||G!==m)&&vh(e,a,i,m),Nn=!1;var Q=e.memoizedState;a.state=Q,Or(e,i,a,r),Ar(),G=e.memoizedState,D||Q!==G||Nn?(typeof nt=="function"&&(Mo(e,n,nt,i),G=e.memoizedState),(L=Nn||gh(e,n,L,i,Q,G,m))?(k||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(e.flags|=4194308)):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=G),a.props=i,a.state=G,a.context=m,i=L):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{a=e.stateNode,uo(t,e),m=e.memoizedProps,k=oi(n,m),a.props=k,nt=e.pendingProps,Q=a.context,G=n.contextType,L=_i,typeof G=="object"&&G!==null&&(L=Ee(G)),D=n.getDerivedStateFromProps,(G=typeof D=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(m!==nt||Q!==L)&&vh(e,a,i,L),Nn=!1,Q=e.memoizedState,a.state=Q,Or(e,i,a,r),Ar();var Z=e.memoizedState;m!==nt||Q!==Z||Nn||t!==null&&t.dependencies!==null&&xa(t.dependencies)?(typeof D=="function"&&(Mo(e,n,D,i),Z=e.memoizedState),(k=Nn||gh(e,n,k,i,Q,Z,L)||t!==null&&t.dependencies!==null&&xa(t.dependencies))?(G||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(i,Z,L),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(i,Z,L)),typeof a.componentDidUpdate=="function"&&(e.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof a.componentDidUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=Z),a.props=i,a.state=Z,a.context=L,i=k):(typeof a.componentDidUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||m===t.memoizedProps&&Q===t.memoizedState||(e.flags|=1024),i=!1)}return a=i,Za(t,e),i=(e.flags&128)!==0,a||i?(a=e.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:a.render(),e.flags|=1,t!==null&&i?(e.child=Hi(e,t.child,null,r),e.child=Hi(e,null,n,r)):pe(t,e,n,r),e.memoizedState=a.state,t=e.child):t=gn(t,e,r),t}function Lh(t,e,n,i){return mr(),e.flags|=256,pe(t,e,n,i),e.child}var Lo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Uo(t){return{baseLanes:t,cachePool:Tf()}}function Bo(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ie),t}function Uh(t,e,n){var i=e.pendingProps,r=!1,a=(e.flags&128)!==0,m;if((m=a)||(m=t!==null&&t.memoizedState===null?!1:(oe.current&2)!==0),m&&(r=!0,e.flags&=-129),m=(e.flags&32)!==0,e.flags&=-33,t===null){if(Xt){if(r?Rn(e):Cn(),Xt){var D=$t,L;if(L=D){t:{for(L=D,D=We;L.nodeType!==8;){if(!D){D=null;break t}if(L=Je(L.nextSibling),L===null){D=null;break t}}D=L}D!==null?(e.memoizedState={dehydrated:D,treeContext:ti!==null?{id:fn,overflow:hn}:null,retryLane:536870912,hydrationErrors:null},L=Ce(18,null,null,0),L.stateNode=D,L.return=e,e.child=L,Se=e,$t=null,L=!0):L=!1}L||ii(e)}if(D=e.memoizedState,D!==null&&(D=D.dehydrated,D!==null))return vs(D)?e.lanes=32:e.lanes=536870912,null;mn(e)}return D=i.children,i=i.fallback,r?(Cn(),r=e.mode,D=Ka({mode:"hidden",children:D},r),i=$n(i,r,n,null),D.return=e,i.return=e,D.sibling=i,e.child=D,r=e.child,r.memoizedState=Uo(n),r.childLanes=Bo(t,m,n),e.memoizedState=Lo,i):(Rn(e),jo(e,D))}if(L=t.memoizedState,L!==null&&(D=L.dehydrated,D!==null)){if(a)e.flags&256?(Rn(e),e.flags&=-257,e=Xo(t,e,n)):e.memoizedState!==null?(Cn(),e.child=t.child,e.flags|=128,e=null):(Cn(),r=i.fallback,D=e.mode,i=Ka({mode:"visible",children:i.children},D),r=$n(r,D,n,null),r.flags|=2,i.return=e,r.return=e,i.sibling=r,e.child=i,Hi(e,t.child,null,n),i=e.child,i.memoizedState=Uo(n),i.childLanes=Bo(t,m,n),e.memoizedState=Lo,e=r);else if(Rn(e),vs(D)){if(m=D.nextSibling&&D.nextSibling.dataset,m)var G=m.dgst;m=G,i=Error(o(419)),i.stack="",i.digest=m,gr({value:i,source:null,stack:null}),e=Xo(t,e,n)}else if(fe||vr(t,e,n,!1),m=(n&t.childLanes)!==0,fe||m){if(m=Zt,m!==null&&(i=n&-n,i=(i&42)!==0?1:vu(i),i=(i&(m.suspendedLanes|n))!==0?0:i,i!==0&&i!==L.retryLane))throw L.retryLane=i,Mi(t,i),Xe(m,t,i),Oh;D.data==="$?"||es(),e=Xo(t,e,n)}else D.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=L.treeContext,$t=Je(D.nextSibling),Se=e,Xt=!0,ni=null,We=!1,t!==null&&(Ve[Fe++]=fn,Ve[Fe++]=hn,Ve[Fe++]=ti,fn=t.id,hn=t.overflow,ti=e),e=jo(e,i.children),e.flags|=4096);return e}return r?(Cn(),r=i.fallback,D=e.mode,L=t.child,G=L.sibling,i=cn(L,{mode:"hidden",children:i.children}),i.subtreeFlags=L.subtreeFlags&65011712,G!==null?r=cn(G,r):(r=$n(r,D,n,null),r.flags|=2),r.return=e,i.return=e,i.sibling=r,e.child=i,i=r,r=e.child,D=t.child.memoizedState,D===null?D=Uo(n):(L=D.cachePool,L!==null?(G=ue._currentValue,L=L.parent!==G?{parent:G,pool:G}:L):L=Tf(),D={baseLanes:D.baseLanes|n,cachePool:L}),r.memoizedState=D,r.childLanes=Bo(t,m,n),e.memoizedState=Lo,i):(Rn(e),n=t.child,t=n.sibling,n=cn(n,{mode:"visible",children:i.children}),n.return=e,n.sibling=null,t!==null&&(m=e.deletions,m===null?(e.deletions=[t],e.flags|=16):m.push(t)),e.child=n,e.memoizedState=null,n)}function jo(t,e){return e=Ka({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ka(t,e){return t=Ce(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Xo(t,e,n){return Hi(e,t.child,null,n),t=jo(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Bh(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),to(t.return,e,n)}function zo(t,e,n,i,r){var a=t.memoizedState;a===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:r}:(a.isBackwards=e,a.rendering=null,a.renderingStartTime=0,a.last=i,a.tail=n,a.tailMode=r)}function jh(t,e,n){var i=e.pendingProps,r=i.revealOrder,a=i.tail;if(pe(t,e,i.children,n),i=oe.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Bh(t,n,e);else if(t.tag===19)Bh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch(ft(oe,i),r){case"forwards":for(n=e.child,r=null;n!==null;)t=n.alternate,t!==null&&Ga(t)===null&&(r=n),n=n.sibling;n=r,n===null?(r=e.child,e.child=null):(r=n.sibling,n.sibling=null),zo(e,!1,r,n,a);break;case"backwards":for(n=null,r=e.child,e.child=null;r!==null;){if(t=r.alternate,t!==null&&Ga(t)===null){e.child=r;break}t=r.sibling,r.sibling=n,n=r,r=t}zo(e,!0,n,null,a);break;case"together":zo(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function gn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Xn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(vr(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=cn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=cn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function qo(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&xa(t)))}function h0(t,e,n){switch(e.tag){case 3:T(e,e.stateNode.containerInfo),wn(e,ue,t.memoizedState.cache),mr();break;case 27:case 5:Y(e);break;case 4:T(e,e.stateNode.containerInfo);break;case 10:wn(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(Rn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Uh(t,e,n):(Rn(e),t=gn(t,e,n),t!==null?t.sibling:null);Rn(e);break;case 19:var r=(t.flags&128)!==0;if(i=(n&e.childLanes)!==0,i||(vr(t,e,n,!1),i=(n&e.childLanes)!==0),r){if(i)return jh(t,e,n);e.flags|=128}if(r=e.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),ft(oe,oe.current),i)break;return null;case 22:case 23:return e.lanes=0,Mh(t,e,n);case 24:wn(e,ue,t.memoizedState.cache)}return gn(t,e,n)}function Xh(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)fe=!0;else{if(!qo(t,n)&&(e.flags&128)===0)return fe=!1,h0(t,e,n);fe=(t.flags&131072)!==0}else fe=!1,Xt&&(e.flags&1048576)!==0&&df(e,Na,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,r=i._init;if(i=r(i._payload),e.type=i,typeof i=="function")Zu(i)?(t=oi(i,t),e.tag=1,e=Ch(null,e,i,t,n)):(e.tag=0,e=Co(null,e,i,t,n));else{if(i!=null){if(r=i.$$typeof,r===U){e.tag=11,e=wh(null,e,i,t,n);break t}else if(r===ut){e.tag=14,e=Nh(null,e,i,t,n);break t}}throw e=bt(i)||i,Error(o(306,e,""))}}return e;case 0:return Co(t,e,e.type,e.pendingProps,n);case 1:return i=e.type,r=oi(i,e.pendingProps),Ch(t,e,i,r,n);case 3:t:{if(T(e,e.stateNode.containerInfo),t===null)throw Error(o(387));i=e.pendingProps;var a=e.memoizedState;r=a.element,uo(t,e),Or(e,i,null,n);var m=e.memoizedState;if(i=m.cache,wn(e,ue,i),i!==a.cache&&eo(e,[ue],n,!0),Ar(),i=m.element,a.isDehydrated)if(a={element:i,isDehydrated:!1,cache:m.cache},e.updateQueue.baseState=a,e.memoizedState=a,e.flags&256){e=Lh(t,e,i,n);break t}else if(i!==r){r=He(Error(o(424)),e),gr(r),e=Lh(t,e,i,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for($t=Je(t.firstChild),Se=e,Xt=!0,ni=null,We=!0,n=yh(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(mr(),i===r){e=gn(t,e,n);break t}pe(t,e,i,n)}e=e.child}return e;case 26:return Za(t,e),t===null?(n=Yd(e.type,null,e.pendingProps,null))?e.memoizedState=n:Xt||(n=e.type,t=e.pendingProps,i=ol(vt.current).createElement(n),i[ve]=e,i[De]=t,me(i,n,t),ce(i),e.stateNode=i):e.memoizedState=Yd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Y(e),t===null&&Xt&&(i=e.stateNode=zd(e.type,e.pendingProps,vt.current),Se=e,We=!0,r=$t,Yn(e.type)?(Es=r,$t=Je(i.firstChild)):$t=r),pe(t,e,e.pendingProps.children,n),Za(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Xt&&((r=i=$t)&&(i=H0(i,e.type,e.pendingProps,We),i!==null?(e.stateNode=i,Se=e,$t=Je(i.firstChild),We=!1,r=!0):r=!1),r||ii(e)),Y(e),r=e.type,a=e.pendingProps,m=t!==null?t.memoizedProps:null,i=a.children,ys(r,a)?i=null:m!==null&&ys(r,m)&&(e.flags|=32),e.memoizedState!==null&&(r=po(t,e,r0,null,null,n),Zr._currentValue=r),Za(t,e),pe(t,e,i,n),e.child;case 6:return t===null&&Xt&&((t=n=$t)&&(n=Y0(n,e.pendingProps,We),n!==null?(e.stateNode=n,Se=e,$t=null,t=!0):t=!1),t||ii(e)),null;case 13:return Uh(t,e,n);case 4:return T(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=Hi(e,null,i,n):pe(t,e,i,n),e.child;case 11:return wh(t,e,e.type,e.pendingProps,n);case 7:return pe(t,e,e.pendingProps,n),e.child;case 8:return pe(t,e,e.pendingProps.children,n),e.child;case 12:return pe(t,e,e.pendingProps.children,n),e.child;case 10:return i=e.pendingProps,wn(e,e.type,i.value),pe(t,e,i.children,n),e.child;case 9:return r=e.type._context,i=e.pendingProps.children,ai(e),r=Ee(r),i=i(r),e.flags|=1,pe(t,e,i,n),e.child;case 14:return Nh(t,e,e.type,e.pendingProps,n);case 15:return xh(t,e,e.type,e.pendingProps,n);case 19:return jh(t,e,n);case 31:return i=e.pendingProps,n=e.mode,i={mode:i.mode,children:i.children},t===null?(n=Ka(i,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=cn(t.child,i),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Mh(t,e,n);case 24:return ai(e),i=Ee(ue),t===null?(r=ro(),r===null&&(r=Zt,a=no(),r.pooledCache=a,a.refCount++,a!==null&&(r.pooledCacheLanes|=n),r=a),e.memoizedState={parent:i,cache:r},lo(e),wn(e,ue,r)):((t.lanes&n)!==0&&(uo(t,e),Or(e,null,null,n),Ar()),r=t.memoizedState,a=e.memoizedState,r.parent!==i?(r={parent:i,cache:i},e.memoizedState=r,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=r),wn(e,ue,i)):(i=a.cache,wn(e,ue,i),i!==r.cache&&eo(e,[ue],n,!0))),pe(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function vn(t){t.flags|=4}function zh(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Qd(e)){if(e=Ge.current,e!==null&&((Ut&4194048)===Ut?$e!==null:(Ut&62914560)!==Ut&&(Ut&536870912)===0||e!==$e))throw Sr=ao,bf;t.flags|=8192}}function Pa(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?yc():536870912,t.lanes|=e,Gi|=e)}function Cr(t,e){if(!Xt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Jt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var r=t.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags&65011712,i|=r.flags&65011712,r.return=t,r=r.sibling;else for(r=t.child;r!==null;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags,i|=r.flags,r.return=t,r=r.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function d0(t,e,n){var i=e.pendingProps;switch(ku(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Jt(e),null;case 1:return Jt(e),null;case 3:return n=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),pn(ue),tt(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(yr(e)?vn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,mf())),Jt(e),null;case 26:return n=e.memoizedState,t===null?(vn(e),n!==null?(Jt(e),zh(e,n)):(Jt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(vn(e),Jt(e),zh(e,n)):(Jt(e),e.flags&=-16777217):(t.memoizedProps!==i&&vn(e),Jt(e),e.flags&=-16777217),null;case 27:xt(e),n=vt.current;var r=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&vn(e);else{if(!i){if(e.stateNode===null)throw Error(o(166));return Jt(e),null}t=dt.current,yr(e)?pf(e):(t=zd(r,i,n),e.stateNode=t,vn(e))}return Jt(e),null;case 5:if(xt(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&vn(e);else{if(!i){if(e.stateNode===null)throw Error(o(166));return Jt(e),null}if(t=dt.current,yr(e))pf(e);else{switch(r=ol(vt.current),t){case 1:t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=r.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?r.createElement("select",{is:i.is}):r.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?r.createElement(n,{is:i.is}):r.createElement(n)}}t[ve]=e,t[De]=i;t:for(r=e.child;r!==null;){if(r.tag===5||r.tag===6)t.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===e)break t;for(;r.sibling===null;){if(r.return===null||r.return===e)break t;r=r.return}r.sibling.return=r.return,r=r.sibling}e.stateNode=t;t:switch(me(t,n,i),n){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&vn(e)}}return Jt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&vn(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(o(166));if(t=vt.current,yr(e)){if(t=e.stateNode,n=e.memoizedProps,i=null,r=Se,r!==null)switch(r.tag){case 27:case 5:i=r.memoizedProps}t[ve]=e,t=!!(t.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||Rd(t.nodeValue,n)),t||ii(e)}else t=ol(t).createTextNode(i),t[ve]=e,e.stateNode=t}return Jt(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(r=yr(e),i!==null&&i.dehydrated!==null){if(t===null){if(!r)throw Error(o(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(o(317));r[ve]=e}else mr(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Jt(e),r=!1}else r=mf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=r),r=!0;if(!r)return e.flags&256?(mn(e),e):(mn(e),null)}if(mn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=i!==null,t=t!==null&&t.memoizedState!==null,n){i=e.child,r=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(r=i.alternate.memoizedState.cachePool.pool);var a=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(a=i.memoizedState.cachePool.pool),a!==r&&(i.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Pa(e,e.updateQueue),Jt(e),null;case 4:return tt(),t===null&&cs(e.stateNode.containerInfo),Jt(e),null;case 10:return pn(e.type),Jt(e),null;case 19:if(ct(oe),r=e.memoizedState,r===null)return Jt(e),null;if(i=(e.flags&128)!==0,a=r.rendering,a===null)if(i)Cr(r,!1);else{if(te!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(a=Ga(t),a!==null){for(e.flags|=128,Cr(r,!1),t=a.updateQueue,e.updateQueue=t,Pa(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)hf(n,t),n=n.sibling;return ft(oe,oe.current&1|2),e.child}t=t.sibling}r.tail!==null&&p()>Wa&&(e.flags|=128,i=!0,Cr(r,!1),e.lanes=4194304)}else{if(!i)if(t=Ga(a),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,Pa(e,t),Cr(r,!0),r.tail===null&&r.tailMode==="hidden"&&!a.alternate&&!Xt)return Jt(e),null}else 2*p()-r.renderingStartTime>Wa&&n!==536870912&&(e.flags|=128,i=!0,Cr(r,!1),e.lanes=4194304);r.isBackwards?(a.sibling=e.child,e.child=a):(t=r.last,t!==null?t.sibling=a:e.child=a,r.last=a)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=p(),e.sibling=null,t=oe.current,ft(oe,i?t&1|2:t&1),e):(Jt(e),null);case 22:case 23:return mn(e),fo(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(n&536870912)!==0&&(e.flags&128)===0&&(Jt(e),e.subtreeFlags&6&&(e.flags|=8192)):Jt(e),n=e.updateQueue,n!==null&&Pa(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==n&&(e.flags|=2048),t!==null&&ct(li),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),pn(ue),Jt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function p0(t,e){switch(ku(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return pn(ue),tt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return xt(e),null;case 13:if(mn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));mr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return ct(oe),null;case 4:return tt(),null;case 10:return pn(e.type),null;case 22:case 23:return mn(e),fo(),t!==null&&ct(li),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return pn(ue),null;case 25:return null;default:return null}}function qh(t,e){switch(ku(e),e.tag){case 3:pn(ue),tt();break;case 26:case 27:case 5:xt(e);break;case 4:tt();break;case 13:mn(e);break;case 19:ct(oe);break;case 10:pn(e.type);break;case 22:case 23:mn(e),fo(),t!==null&&ct(li);break;case 24:pn(ue)}}function Lr(t,e){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var r=i.next;n=r;do{if((n.tag&t)===t){i=void 0;var a=n.create,m=n.inst;i=a(),m.destroy=i}n=n.next}while(n!==r)}}catch(D){It(e,e.return,D)}}function Ln(t,e,n){try{var i=e.updateQueue,r=i!==null?i.lastEffect:null;if(r!==null){var a=r.next;i=a;do{if((i.tag&t)===t){var m=i.inst,D=m.destroy;if(D!==void 0){m.destroy=void 0,r=e;var L=n,G=D;try{G()}catch(k){It(r,L,k)}}}i=i.next}while(i!==a)}}catch(k){It(e,e.return,k)}}function Hh(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Nf(e,n)}catch(i){It(t,t.return,i)}}}function Yh(t,e,n){n.props=oi(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(i){It(t,e,i)}}function Ur(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof n=="function"?t.refCleanup=n(i):n.current=i}}catch(r){It(t,e,r)}}function tn(t,e){var n=t.ref,i=t.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(r){It(t,e,r)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(r){It(t,e,r)}else n.current=null}function Vh(t){var e=t.type,n=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break t;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(r){It(t,t.return,r)}}function Ho(t,e,n){try{var i=t.stateNode;B0(i,t.type,n,e),i[De]=e}catch(r){It(t,t.return,r)}}function Fh(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Yn(t.type)||t.tag===4}function Yo(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Fh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Yn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Vo(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ul));else if(i!==4&&(i===27&&Yn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Vo(t,e,n),t=t.sibling;t!==null;)Vo(t,e,n),t=t.sibling}function Ja(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(i===27&&Yn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Ja(t,e,n),t=t.sibling;t!==null;)Ja(t,e,n),t=t.sibling}function Gh(t){var e=t.stateNode,n=t.memoizedProps;try{for(var i=t.type,r=e.attributes;r.length;)e.removeAttributeNode(r[0]);me(e,i,n),e[ve]=t,e[De]=n}catch(a){It(t,t.return,a)}}var En=!1,ie=!1,Fo=!1,Ih=typeof WeakSet=="function"?WeakSet:Set,he=null;function y0(t,e){if(t=t.containerInfo,ds=pl,t=ef(t),Hu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var r=i.anchorOffset,a=i.focusNode;i=i.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break t}var m=0,D=-1,L=-1,G=0,k=0,nt=t,Q=null;e:for(;;){for(var Z;nt!==n||r!==0&&nt.nodeType!==3||(D=m+r),nt!==a||i!==0&&nt.nodeType!==3||(L=m+i),nt.nodeType===3&&(m+=nt.nodeValue.length),(Z=nt.firstChild)!==null;)Q=nt,nt=Z;for(;;){if(nt===t)break e;if(Q===n&&++G===r&&(D=m),Q===a&&++k===i&&(L=m),(Z=nt.nextSibling)!==null)break;nt=Q,Q=nt.parentNode}nt=Z}n=D===-1||L===-1?null:{start:D,end:L}}else n=null}n=n||{start:0,end:0}}else n=null;for(ps={focusedElem:t,selectionRange:n},pl=!1,he=e;he!==null;)if(e=he,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,he=t;else for(;he!==null;){switch(e=he,a=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&a!==null){t=void 0,n=e,r=a.memoizedProps,a=a.memoizedState,i=n.stateNode;try{var Et=oi(n.type,r,n.elementType===n.type);t=i.getSnapshotBeforeUpdate(Et,a),i.__reactInternalSnapshotBeforeUpdate=t}catch(mt){It(n,n.return,mt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)gs(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gs(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,he=t;break}he=e.return}}function Qh(t,e,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Un(t,n),i&4&&Lr(5,n);break;case 1:if(Un(t,n),i&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(m){It(n,n.return,m)}else{var r=oi(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(r,e,t.__reactInternalSnapshotBeforeUpdate)}catch(m){It(n,n.return,m)}}i&64&&Hh(n),i&512&&Ur(n,n.return);break;case 3:if(Un(t,n),i&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Nf(t,e)}catch(m){It(n,n.return,m)}}break;case 27:e===null&&i&4&&Gh(n);case 26:case 5:Un(t,n),e===null&&i&4&&Vh(n),i&512&&Ur(n,n.return);break;case 12:Un(t,n);break;case 13:Un(t,n),i&4&&Ph(t,n),i&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=A0.bind(null,n),V0(t,n))));break;case 22:if(i=n.memoizedState!==null||En,!i){e=e!==null&&e.memoizedState!==null||ie,r=En;var a=ie;En=i,(ie=e)&&!a?Bn(t,n,(n.subtreeFlags&8772)!==0):Un(t,n),En=r,ie=a}break;case 30:break;default:Un(t,n)}}function Zh(t){var e=t.alternate;e!==null&&(t.alternate=null,Zh(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&bu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Pt=null,we=!1;function Tn(t,e,n){for(n=n.child;n!==null;)Kh(t,e,n),n=n.sibling}function Kh(t,e,n){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(Wt,n)}catch{}switch(n.tag){case 26:ie||tn(n,e),Tn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ie||tn(n,e);var i=Pt,r=we;Yn(n.type)&&(Pt=n.stateNode,we=!1),Tn(t,e,n),Fr(n.stateNode),Pt=i,we=r;break;case 5:ie||tn(n,e);case 6:if(i=Pt,r=we,Pt=null,Tn(t,e,n),Pt=i,we=r,Pt!==null)if(we)try{(Pt.nodeType===9?Pt.body:Pt.nodeName==="HTML"?Pt.ownerDocument.body:Pt).removeChild(n.stateNode)}catch(a){It(n,e,a)}else try{Pt.removeChild(n.stateNode)}catch(a){It(n,e,a)}break;case 18:Pt!==null&&(we?(t=Pt,jd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),kr(t)):jd(Pt,n.stateNode));break;case 4:i=Pt,r=we,Pt=n.stateNode.containerInfo,we=!0,Tn(t,e,n),Pt=i,we=r;break;case 0:case 11:case 14:case 15:ie||Ln(2,n,e),ie||Ln(4,n,e),Tn(t,e,n);break;case 1:ie||(tn(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Yh(n,e,i)),Tn(t,e,n);break;case 21:Tn(t,e,n);break;case 22:ie=(i=ie)||n.memoizedState!==null,Tn(t,e,n),ie=i;break;default:Tn(t,e,n)}}function Ph(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{kr(t)}catch(n){It(e,e.return,n)}}function m0(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Ih),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Ih),e;default:throw Error(o(435,t.tag))}}function Go(t,e){var n=m0(t);e.forEach(function(i){var r=O0.bind(null,t,i);n.has(i)||(n.add(i),i.then(r,r))})}function Le(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var r=n[i],a=t,m=e,D=m;t:for(;D!==null;){switch(D.tag){case 27:if(Yn(D.type)){Pt=D.stateNode,we=!1;break t}break;case 5:Pt=D.stateNode,we=!1;break t;case 3:case 4:Pt=D.stateNode.containerInfo,we=!0;break t}D=D.return}if(Pt===null)throw Error(o(160));Kh(a,m,r),Pt=null,we=!1,a=r.alternate,a!==null&&(a.return=null),r.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Jh(e,t),e=e.sibling}var Pe=null;function Jh(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Le(e,t),Ue(t),i&4&&(Ln(3,t,t.return),Lr(3,t),Ln(5,t,t.return));break;case 1:Le(e,t),Ue(t),i&512&&(ie||n===null||tn(n,n.return)),i&64&&En&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var r=Pe;if(Le(e,t),Ue(t),i&512&&(ie||n===null||tn(n,n.return)),i&4){var a=n!==null?n.memoizedState:null;if(i=t.memoizedState,n===null)if(i===null)if(t.stateNode===null){t:{i=t.type,n=t.memoizedProps,r=r.ownerDocument||r;e:switch(i){case"title":a=r.getElementsByTagName("title")[0],(!a||a[rr]||a[ve]||a.namespaceURI==="http://www.w3.org/2000/svg"||a.hasAttribute("itemprop"))&&(a=r.createElement(i),r.head.insertBefore(a,r.querySelector("head > title"))),me(a,i,n),a[ve]=t,ce(a),i=a;break t;case"link":var m=Gd("link","href",r).get(i+(n.href||""));if(m){for(var D=0;D<m.length;D++)if(a=m[D],a.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&a.getAttribute("rel")===(n.rel==null?null:n.rel)&&a.getAttribute("title")===(n.title==null?null:n.title)&&a.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){m.splice(D,1);break e}}a=r.createElement(i),me(a,i,n),r.head.appendChild(a);break;case"meta":if(m=Gd("meta","content",r).get(i+(n.content||""))){for(D=0;D<m.length;D++)if(a=m[D],a.getAttribute("content")===(n.content==null?null:""+n.content)&&a.getAttribute("name")===(n.name==null?null:n.name)&&a.getAttribute("property")===(n.property==null?null:n.property)&&a.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&a.getAttribute("charset")===(n.charSet==null?null:n.charSet)){m.splice(D,1);break e}}a=r.createElement(i),me(a,i,n),r.head.appendChild(a);break;default:throw Error(o(468,i))}a[ve]=t,ce(a),i=a}t.stateNode=i}else Id(r,t.type,t.stateNode);else t.stateNode=Fd(r,i,t.memoizedProps);else a!==i?(a===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):a.count--,i===null?Id(r,t.type,t.stateNode):Fd(r,i,t.memoizedProps)):i===null&&t.stateNode!==null&&Ho(t,t.memoizedProps,n.memoizedProps)}break;case 27:Le(e,t),Ue(t),i&512&&(ie||n===null||tn(n,n.return)),n!==null&&i&4&&Ho(t,t.memoizedProps,n.memoizedProps);break;case 5:if(Le(e,t),Ue(t),i&512&&(ie||n===null||tn(n,n.return)),t.flags&32){r=t.stateNode;try{Si(r,"")}catch(Z){It(t,t.return,Z)}}i&4&&t.stateNode!=null&&(r=t.memoizedProps,Ho(t,r,n!==null?n.memoizedProps:r)),i&1024&&(Fo=!0);break;case 6:if(Le(e,t),Ue(t),i&4){if(t.stateNode===null)throw Error(o(162));i=t.memoizedProps,n=t.stateNode;try{n.nodeValue=i}catch(Z){It(t,t.return,Z)}}break;case 3:if(fl=null,r=Pe,Pe=sl(e.containerInfo),Le(e,t),Pe=r,Ue(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{kr(e.containerInfo)}catch(Z){It(t,t.return,Z)}Fo&&(Fo=!1,kh(t));break;case 4:i=Pe,Pe=sl(t.stateNode.containerInfo),Le(e,t),Ue(t),Pe=i;break;case 12:Le(e,t),Ue(t);break;case 13:Le(e,t),Ue(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Jo=p()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Go(t,i)));break;case 22:r=t.memoizedState!==null;var L=n!==null&&n.memoizedState!==null,G=En,k=ie;if(En=G||r,ie=k||L,Le(e,t),ie=k,En=G,Ue(t),i&8192)t:for(e=t.stateNode,e._visibility=r?e._visibility&-2:e._visibility|1,r&&(n===null||L||En||ie||si(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){L=n=e;try{if(a=L.stateNode,r)m=a.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{D=L.stateNode;var nt=L.memoizedProps.style,Q=nt!=null&&nt.hasOwnProperty("display")?nt.display:null;D.style.display=Q==null||typeof Q=="boolean"?"":(""+Q).trim()}}catch(Z){It(L,L.return,Z)}}}else if(e.tag===6){if(n===null){L=e;try{L.stateNode.nodeValue=r?"":L.memoizedProps}catch(Z){It(L,L.return,Z)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Go(t,n))));break;case 19:Le(e,t),Ue(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Go(t,i)));break;case 30:break;case 21:break;default:Le(e,t),Ue(t)}}function Ue(t){var e=t.flags;if(e&2){try{for(var n,i=t.return;i!==null;){if(Fh(i)){n=i;break}i=i.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var r=n.stateNode,a=Yo(t);Ja(t,a,r);break;case 5:var m=n.stateNode;n.flags&32&&(Si(m,""),n.flags&=-33);var D=Yo(t);Ja(t,D,m);break;case 3:case 4:var L=n.stateNode.containerInfo,G=Yo(t);Vo(t,G,L);break;default:throw Error(o(161))}}catch(k){It(t,t.return,k)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function kh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;kh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Un(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Qh(t,e.alternate,e),e=e.sibling}function si(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Ln(4,e,e.return),si(e);break;case 1:tn(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Yh(e,e.return,n),si(e);break;case 27:Fr(e.stateNode);case 26:case 5:tn(e,e.return),si(e);break;case 22:e.memoizedState===null&&si(e);break;case 30:si(e);break;default:si(e)}t=t.sibling}}function Bn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,r=t,a=e,m=a.flags;switch(a.tag){case 0:case 11:case 15:Bn(r,a,n),Lr(4,a);break;case 1:if(Bn(r,a,n),i=a,r=i.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(G){It(i,i.return,G)}if(i=a,r=i.updateQueue,r!==null){var D=i.stateNode;try{var L=r.shared.hiddenCallbacks;if(L!==null)for(r.shared.hiddenCallbacks=null,r=0;r<L.length;r++)wf(L[r],D)}catch(G){It(i,i.return,G)}}n&&m&64&&Hh(a),Ur(a,a.return);break;case 27:Gh(a);case 26:case 5:Bn(r,a,n),n&&i===null&&m&4&&Vh(a),Ur(a,a.return);break;case 12:Bn(r,a,n);break;case 13:Bn(r,a,n),n&&m&4&&Ph(r,a);break;case 22:a.memoizedState===null&&Bn(r,a,n),Ur(a,a.return);break;case 30:break;default:Bn(r,a,n)}e=e.sibling}}function Io(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Er(n))}function Qo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Er(t))}function en(t,e,n,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Wh(t,e,n,i),e=e.sibling}function Wh(t,e,n,i){var r=e.flags;switch(e.tag){case 0:case 11:case 15:en(t,e,n,i),r&2048&&Lr(9,e);break;case 1:en(t,e,n,i);break;case 3:en(t,e,n,i),r&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Er(t)));break;case 12:if(r&2048){en(t,e,n,i),t=e.stateNode;try{var a=e.memoizedProps,m=a.id,D=a.onPostCommit;typeof D=="function"&&D(m,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(L){It(e,e.return,L)}}else en(t,e,n,i);break;case 13:en(t,e,n,i);break;case 23:break;case 22:a=e.stateNode,m=e.alternate,e.memoizedState!==null?a._visibility&2?en(t,e,n,i):Br(t,e):a._visibility&2?en(t,e,n,i):(a._visibility|=2,Yi(t,e,n,i,(e.subtreeFlags&10256)!==0)),r&2048&&Io(m,e);break;case 24:en(t,e,n,i),r&2048&&Qo(e.alternate,e);break;default:en(t,e,n,i)}}function Yi(t,e,n,i,r){for(r=r&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var a=t,m=e,D=n,L=i,G=m.flags;switch(m.tag){case 0:case 11:case 15:Yi(a,m,D,L,r),Lr(8,m);break;case 23:break;case 22:var k=m.stateNode;m.memoizedState!==null?k._visibility&2?Yi(a,m,D,L,r):Br(a,m):(k._visibility|=2,Yi(a,m,D,L,r)),r&&G&2048&&Io(m.alternate,m);break;case 24:Yi(a,m,D,L,r),r&&G&2048&&Qo(m.alternate,m);break;default:Yi(a,m,D,L,r)}e=e.sibling}}function Br(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,i=e,r=i.flags;switch(i.tag){case 22:Br(n,i),r&2048&&Io(i.alternate,i);break;case 24:Br(n,i),r&2048&&Qo(i.alternate,i);break;default:Br(n,i)}e=e.sibling}}var jr=8192;function Vi(t){if(t.subtreeFlags&jr)for(t=t.child;t!==null;)$h(t),t=t.sibling}function $h(t){switch(t.tag){case 26:Vi(t),t.flags&jr&&t.memoizedState!==null&&eg(Pe,t.memoizedState,t.memoizedProps);break;case 5:Vi(t);break;case 3:case 4:var e=Pe;Pe=sl(t.stateNode.containerInfo),Vi(t),Pe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=jr,jr=16777216,Vi(t),jr=e):Vi(t));break;default:Vi(t)}}function td(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xr(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];he=i,nd(i,t)}td(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ed(t),t=t.sibling}function ed(t){switch(t.tag){case 0:case 11:case 15:Xr(t),t.flags&2048&&Ln(9,t,t.return);break;case 3:Xr(t);break;case 12:Xr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ka(t)):Xr(t);break;default:Xr(t)}}function ka(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];he=i,nd(i,t)}td(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Ln(8,e,e.return),ka(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ka(e));break;default:ka(e)}t=t.sibling}}function nd(t,e){for(;he!==null;){var n=he;switch(n.tag){case 0:case 11:case 15:Ln(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Er(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,he=i;else t:for(n=t;he!==null;){i=he;var r=i.sibling,a=i.return;if(Zh(i),i===n){he=null;break t}if(r!==null){r.return=a,he=r;break t}he=a}}}var g0={getCacheForType:function(t){var e=Ee(ue),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},v0=typeof WeakMap=="function"?WeakMap:Map,qt=0,Zt=null,Mt=null,Ut=0,Ht=0,Be=null,jn=!1,Fi=!1,Zo=!1,bn=0,te=0,Xn=0,ci=0,Ko=0,Ie=0,Gi=0,zr=null,Ne=null,Po=!1,Jo=0,Wa=1/0,$a=null,zn=null,ye=0,qn=null,Ii=null,Qi=0,ko=0,Wo=null,id=null,qr=0,$o=null;function je(){if((qt&2)!==0&&Ut!==0)return Ut&-Ut;if(I.T!==null){var t=Li;return t!==0?t:ls()}return vc()}function rd(){Ie===0&&(Ie=(Ut&536870912)===0||Xt?pc():536870912);var t=Ge.current;return t!==null&&(t.flags|=32),Ie}function Xe(t,e,n){(t===Zt&&(Ht===2||Ht===9)||t.cancelPendingCommit!==null)&&(Zi(t,0),Hn(t,Ut,Ie,!1)),ir(t,n),((qt&2)===0||t!==Zt)&&(t===Zt&&((qt&2)===0&&(ci|=n),te===4&&Hn(t,Ut,Ie,!1)),nn(t))}function ad(t,e,n){if((qt&6)!==0)throw Error(o(327));var i=!n&&(e&124)===0&&(e&t.expiredLanes)===0||nr(t,e),r=i?b0(t,e):ns(t,e,!0),a=i;do{if(r===0){Fi&&!i&&Hn(t,e,0,!1);break}else{if(n=t.current.alternate,a&&!E0(n)){r=ns(t,e,!1),a=!1;continue}if(r===2){if(a=e,t.errorRecoveryDisabledLanes&a)var m=0;else m=t.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){e=m;t:{var D=t;r=zr;var L=D.current.memoizedState.isDehydrated;if(L&&(Zi(D,m).flags|=256),m=ns(D,m,!1),m!==2){if(Zo&&!L){D.errorRecoveryDisabledLanes|=a,ci|=a,r=4;break t}a=Ne,Ne=r,a!==null&&(Ne===null?Ne=a:Ne.push.apply(Ne,a))}r=m}if(a=!1,r!==2)continue}}if(r===1){Zi(t,0),Hn(t,e,0,!0);break}t:{switch(i=t,a=r,a){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Hn(i,e,Ie,!jn);break t;case 2:Ne=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(r=Jo+300-p(),10<r)){if(Hn(i,e,Ie,!jn),ca(i,0,!0)!==0)break t;i.timeoutHandle=Ud(ld.bind(null,i,n,Ne,$a,Po,e,Ie,ci,Gi,jn,a,2,-0,0),r);break t}ld(i,n,Ne,$a,Po,e,Ie,ci,Gi,jn,a,0,-0,0)}}break}while(!0);nn(t)}function ld(t,e,n,i,r,a,m,D,L,G,k,nt,Q,Z){if(t.timeoutHandle=-1,nt=e.subtreeFlags,(nt&8192||(nt&16785408)===16785408)&&(Qr={stylesheets:null,count:0,unsuspend:tg},$h(e),nt=ng(),nt!==null)){t.cancelPendingCommit=nt(dd.bind(null,t,e,a,n,i,r,m,D,L,k,1,Q,Z)),Hn(t,a,m,!G);return}dd(t,e,a,n,i,r,m,D,L)}function E0(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var r=n[i],a=r.getSnapshot;r=r.value;try{if(!Re(a(),r))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Hn(t,e,n,i){e&=~Ko,e&=~ci,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var r=e;0<r;){var a=31-ge(r),m=1<<a;i[a]=-1,r&=~m}n!==0&&mc(t,n,e)}function tl(){return(qt&6)===0?(Hr(0),!1):!0}function ts(){if(Mt!==null){if(Ht===0)var t=Mt.return;else t=Mt,dn=ri=null,go(t),qi=null,_r=0,t=Mt;for(;t!==null;)qh(t.alternate,t),t=t.return;Mt=null}}function Zi(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,X0(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),ts(),Zt=t,Mt=n=cn(t.current,null),Ut=e,Ht=0,Be=null,jn=!1,Fi=nr(t,e),Zo=!1,Gi=Ie=Ko=ci=Xn=te=0,Ne=zr=null,Po=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var r=31-ge(i),a=1<<r;e|=t[r],i&=~a}return bn=e,Sa(),n}function ud(t,e){Ot=null,I.H=Ya,e===br||e===Ra?(e=Af(),Ht=3):e===bf?(e=Af(),Ht=4):Ht=e===Oh?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Be=e,Mt===null&&(te=1,Qa(t,He(e,t.current)))}function od(){var t=I.H;return I.H=Ya,t===null?Ya:t}function sd(){var t=I.A;return I.A=g0,t}function es(){te=4,jn||(Ut&4194048)!==Ut&&Ge.current!==null||(Fi=!0),(Xn&134217727)===0&&(ci&134217727)===0||Zt===null||Hn(Zt,Ut,Ie,!1)}function ns(t,e,n){var i=qt;qt|=2;var r=od(),a=sd();(Zt!==t||Ut!==e)&&($a=null,Zi(t,e)),e=!1;var m=te;t:do try{if(Ht!==0&&Mt!==null){var D=Mt,L=Be;switch(Ht){case 8:ts(),m=6;break t;case 3:case 2:case 9:case 6:Ge.current===null&&(e=!0);var G=Ht;if(Ht=0,Be=null,Ki(t,D,L,G),n&&Fi){m=0;break t}break;default:G=Ht,Ht=0,Be=null,Ki(t,D,L,G)}}T0(),m=te;break}catch(k){ud(t,k)}while(!0);return e&&t.shellSuspendCounter++,dn=ri=null,qt=i,I.H=r,I.A=a,Mt===null&&(Zt=null,Ut=0,Sa()),m}function T0(){for(;Mt!==null;)cd(Mt)}function b0(t,e){var n=qt;qt|=2;var i=od(),r=sd();Zt!==t||Ut!==e?($a=null,Wa=p()+500,Zi(t,e)):Fi=nr(t,e);t:do try{if(Ht!==0&&Mt!==null){e=Mt;var a=Be;e:switch(Ht){case 1:Ht=0,Be=null,Ki(t,e,a,1);break;case 2:case 9:if(Sf(a)){Ht=0,Be=null,fd(e);break}e=function(){Ht!==2&&Ht!==9||Zt!==t||(Ht=7),nn(t)},a.then(e,e);break t;case 3:Ht=7;break t;case 4:Ht=5;break t;case 7:Sf(a)?(Ht=0,Be=null,fd(e)):(Ht=0,Be=null,Ki(t,e,a,7));break;case 5:var m=null;switch(Mt.tag){case 26:m=Mt.memoizedState;case 5:case 27:var D=Mt;if(!m||Qd(m)){Ht=0,Be=null;var L=D.sibling;if(L!==null)Mt=L;else{var G=D.return;G!==null?(Mt=G,el(G)):Mt=null}break e}}Ht=0,Be=null,Ki(t,e,a,5);break;case 6:Ht=0,Be=null,Ki(t,e,a,6);break;case 8:ts(),te=6;break t;default:throw Error(o(462))}}S0();break}catch(k){ud(t,k)}while(!0);return dn=ri=null,I.H=i,I.A=r,qt=n,Mt!==null?0:(Zt=null,Ut=0,Sa(),te)}function S0(){for(;Mt!==null&&!se();)cd(Mt)}function cd(t){var e=Xh(t.alternate,t,bn);t.memoizedProps=t.pendingProps,e===null?el(t):Mt=e}function fd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Rh(n,e,e.pendingProps,e.type,void 0,Ut);break;case 11:e=Rh(n,e,e.pendingProps,e.type.render,e.ref,Ut);break;case 5:go(e);default:qh(n,e),e=Mt=hf(e,bn),e=Xh(n,e,bn)}t.memoizedProps=t.pendingProps,e===null?el(t):Mt=e}function Ki(t,e,n,i){dn=ri=null,go(e),qi=null,_r=0;var r=e.return;try{if(f0(t,r,e,n,Ut)){te=1,Qa(t,He(n,t.current)),Mt=null;return}}catch(a){if(r!==null)throw Mt=r,a;te=1,Qa(t,He(n,t.current)),Mt=null;return}e.flags&32768?(Xt||i===1?t=!0:Fi||(Ut&536870912)!==0?t=!1:(jn=t=!0,(i===2||i===9||i===3||i===6)&&(i=Ge.current,i!==null&&i.tag===13&&(i.flags|=16384))),hd(e,t)):el(e)}function el(t){var e=t;do{if((e.flags&32768)!==0){hd(e,jn);return}t=e.return;var n=d0(e.alternate,e,bn);if(n!==null){Mt=n;return}if(e=e.sibling,e!==null){Mt=e;return}Mt=e=t}while(e!==null);te===0&&(te=5)}function hd(t,e){do{var n=p0(t.alternate,t);if(n!==null){n.flags&=32767,Mt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){Mt=t;return}Mt=t=n}while(t!==null);te=6,Mt=null}function dd(t,e,n,i,r,a,m,D,L){t.cancelPendingCommit=null;do nl();while(ye!==0);if((qt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(a=e.lanes|e.childLanes,a|=Iu,tm(t,n,a,m,D,L),t===Zt&&(Mt=Zt=null,Ut=0),Ii=e,qn=t,Qi=n,ko=a,Wo=r,id=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,w0(at,function(){return vd(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=I.T,I.T=null,r=rt.p,rt.p=2,m=qt,qt|=4;try{y0(t,e,n)}finally{qt=m,rt.p=r,I.T=i}}ye=1,pd(),yd(),md()}}function pd(){if(ye===1){ye=0;var t=qn,e=Ii,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=I.T,I.T=null;var i=rt.p;rt.p=2;var r=qt;qt|=4;try{Jh(e,t);var a=ps,m=ef(t.containerInfo),D=a.focusedElem,L=a.selectionRange;if(m!==D&&D&&D.ownerDocument&&tf(D.ownerDocument.documentElement,D)){if(L!==null&&Hu(D)){var G=L.start,k=L.end;if(k===void 0&&(k=G),"selectionStart"in D)D.selectionStart=G,D.selectionEnd=Math.min(k,D.value.length);else{var nt=D.ownerDocument||document,Q=nt&&nt.defaultView||window;if(Q.getSelection){var Z=Q.getSelection(),Et=D.textContent.length,mt=Math.min(L.start,Et),Gt=L.end===void 0?mt:Math.min(L.end,Et);!Z.extend&&mt>Gt&&(m=Gt,Gt=mt,mt=m);var H=$c(D,mt),q=$c(D,Gt);if(H&&q&&(Z.rangeCount!==1||Z.anchorNode!==H.node||Z.anchorOffset!==H.offset||Z.focusNode!==q.node||Z.focusOffset!==q.offset)){var F=nt.createRange();F.setStart(H.node,H.offset),Z.removeAllRanges(),mt>Gt?(Z.addRange(F),Z.extend(q.node,q.offset)):(F.setEnd(q.node,q.offset),Z.addRange(F))}}}}for(nt=[],Z=D;Z=Z.parentNode;)Z.nodeType===1&&nt.push({element:Z,left:Z.scrollLeft,top:Z.scrollTop});for(typeof D.focus=="function"&&D.focus(),D=0;D<nt.length;D++){var et=nt[D];et.element.scrollLeft=et.left,et.element.scrollTop=et.top}}pl=!!ds,ps=ds=null}finally{qt=r,rt.p=i,I.T=n}}t.current=e,ye=2}}function yd(){if(ye===2){ye=0;var t=qn,e=Ii,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=I.T,I.T=null;var i=rt.p;rt.p=2;var r=qt;qt|=4;try{Qh(t,e.alternate,e)}finally{qt=r,rt.p=i,I.T=n}}ye=3}}function md(){if(ye===4||ye===3){ye=0,w();var t=qn,e=Ii,n=Qi,i=id;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ye=5:(ye=0,Ii=qn=null,gd(t,t.pendingLanes));var r=t.pendingLanes;if(r===0&&(zn=null),Eu(n),e=e.stateNode,Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(Wt,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=I.T,r=rt.p,rt.p=2,I.T=null;try{for(var a=t.onRecoverableError,m=0;m<i.length;m++){var D=i[m];a(D.value,{componentStack:D.stack})}}finally{I.T=e,rt.p=r}}(Qi&3)!==0&&nl(),nn(t),r=t.pendingLanes,(n&4194090)!==0&&(r&42)!==0?t===$o?qr++:(qr=0,$o=t):qr=0,Hr(0)}}function gd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Er(e)))}function nl(t){return pd(),yd(),md(),vd()}function vd(){if(ye!==5)return!1;var t=qn,e=ko;ko=0;var n=Eu(Qi),i=I.T,r=rt.p;try{rt.p=32>n?32:n,I.T=null,n=Wo,Wo=null;var a=qn,m=Qi;if(ye=0,Ii=qn=null,Qi=0,(qt&6)!==0)throw Error(o(331));var D=qt;if(qt|=4,ed(a.current),Wh(a,a.current,m,n),qt=D,Hr(0,!1),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(Wt,a)}catch{}return!0}finally{rt.p=r,I.T=i,gd(t,e)}}function Ed(t,e,n){e=He(n,e),e=Ro(t.stateNode,e,2),t=Mn(t,e,2),t!==null&&(ir(t,2),nn(t))}function It(t,e,n){if(t.tag===3)Ed(t,t,n);else for(;e!==null;){if(e.tag===3){Ed(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(zn===null||!zn.has(i))){t=He(n,t),n=Dh(2),i=Mn(e,n,2),i!==null&&(Ah(n,i,e,t),ir(i,2),nn(i));break}}e=e.return}}function is(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new v0;var r=new Set;i.set(e,r)}else r=i.get(e),r===void 0&&(r=new Set,i.set(e,r));r.has(n)||(Zo=!0,r.add(n),t=D0.bind(null,t,e,n),e.then(t,t))}function D0(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Zt===t&&(Ut&n)===n&&(te===4||te===3&&(Ut&62914560)===Ut&&300>p()-Jo?(qt&2)===0&&Zi(t,0):Ko|=n,Gi===Ut&&(Gi=0)),nn(t)}function Td(t,e){e===0&&(e=yc()),t=Mi(t,e),t!==null&&(ir(t,e),nn(t))}function A0(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Td(t,n)}function O0(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,r=t.memoizedState;r!==null&&(n=r.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(o(314))}i!==null&&i.delete(e),Td(t,n)}function w0(t,e){return Lt(t,e)}var il=null,Pi=null,rs=!1,rl=!1,as=!1,fi=0;function nn(t){t!==Pi&&t.next===null&&(Pi===null?il=Pi=t:Pi=Pi.next=t),rl=!0,rs||(rs=!0,x0())}function Hr(t,e){if(!as&&rl){as=!0;do for(var n=!1,i=il;i!==null;){if(t!==0){var r=i.pendingLanes;if(r===0)var a=0;else{var m=i.suspendedLanes,D=i.pingedLanes;a=(1<<31-ge(42|t)+1)-1,a&=r&~(m&~D),a=a&201326741?a&201326741|1:a?a|2:0}a!==0&&(n=!0,Ad(i,a))}else a=Ut,a=ca(i,i===Zt?a:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(a&3)===0||nr(i,a)||(n=!0,Ad(i,a));i=i.next}while(n);as=!1}}function N0(){bd()}function bd(){rl=rs=!1;var t=0;fi!==0&&(j0()&&(t=fi),fi=0);for(var e=p(),n=null,i=il;i!==null;){var r=i.next,a=Sd(i,e);a===0?(i.next=null,n===null?il=r:n.next=r,r===null&&(Pi=n)):(n=i,(t!==0||(a&3)!==0)&&(rl=!0)),i=r}Hr(t)}function Sd(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,r=t.expirationTimes,a=t.pendingLanes&-62914561;0<a;){var m=31-ge(a),D=1<<m,L=r[m];L===-1?((D&n)===0||(D&i)!==0)&&(r[m]=$y(D,e)):L<=e&&(t.expiredLanes|=D),a&=~D}if(e=Zt,n=Ut,n=ca(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,n===0||t===e&&(Ht===2||Ht===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&kt(i),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||nr(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(i!==null&&kt(i),Eu(n)){case 2:case 8:n=V;break;case 32:n=at;break;case 268435456:n=Rt;break;default:n=at}return i=Dd.bind(null,t),n=Lt(n,i),t.callbackPriority=e,t.callbackNode=n,e}return i!==null&&i!==null&&kt(i),t.callbackPriority=2,t.callbackNode=null,2}function Dd(t,e){if(ye!==0&&ye!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(nl()&&t.callbackNode!==n)return null;var i=Ut;return i=ca(t,t===Zt?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(ad(t,i,e),Sd(t,p()),t.callbackNode!=null&&t.callbackNode===n?Dd.bind(null,t):null)}function Ad(t,e){if(nl())return null;ad(t,e,!0)}function x0(){z0(function(){(qt&6)!==0?Lt(R,N0):bd()})}function ls(){return fi===0&&(fi=pc()),fi}function Od(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ya(""+t)}function wd(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function M0(t,e,n,i,r){if(e==="submit"&&n&&n.stateNode===r){var a=Od((r[De]||null).action),m=i.submitter;m&&(e=(e=m[De]||null)?Od(e.formAction):m.getAttribute("formAction"),e!==null&&(a=e,m=null));var D=new Ea("action","action",null,i,r);t.push({event:D,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(fi!==0){var L=m?wd(r,m):new FormData(r);wo(n,{pending:!0,data:L,method:r.method,action:a},null,L)}}else typeof a=="function"&&(D.preventDefault(),L=m?wd(r,m):new FormData(r),wo(n,{pending:!0,data:L,method:r.method,action:a},a,L))},currentTarget:r}]})}}for(var us=0;us<Gu.length;us++){var os=Gu[us],_0=os.toLowerCase(),R0=os[0].toUpperCase()+os.slice(1);Ke(_0,"on"+R0)}Ke(af,"onAnimationEnd"),Ke(lf,"onAnimationIteration"),Ke(uf,"onAnimationStart"),Ke("dblclick","onDoubleClick"),Ke("focusin","onFocus"),Ke("focusout","onBlur"),Ke(Km,"onTransitionRun"),Ke(Pm,"onTransitionStart"),Ke(Jm,"onTransitionCancel"),Ke(of,"onTransitionEnd"),Ei("onMouseEnter",["mouseout","mouseover"]),Ei("onMouseLeave",["mouseout","mouseover"]),Ei("onPointerEnter",["pointerout","pointerover"]),Ei("onPointerLeave",["pointerout","pointerover"]),Pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Pn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),C0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Yr));function Nd(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],r=i.event;i=i.listeners;t:{var a=void 0;if(e)for(var m=i.length-1;0<=m;m--){var D=i[m],L=D.instance,G=D.currentTarget;if(D=D.listener,L!==a&&r.isPropagationStopped())break t;a=D,r.currentTarget=G;try{a(r)}catch(k){Ia(k)}r.currentTarget=null,a=L}else for(m=0;m<i.length;m++){if(D=i[m],L=D.instance,G=D.currentTarget,D=D.listener,L!==a&&r.isPropagationStopped())break t;a=D,r.currentTarget=G;try{a(r)}catch(k){Ia(k)}r.currentTarget=null,a=L}}}}function _t(t,e){var n=e[Tu];n===void 0&&(n=e[Tu]=new Set);var i=t+"__bubble";n.has(i)||(xd(e,t,2,!1),n.add(i))}function ss(t,e,n){var i=0;e&&(i|=4),xd(n,t,i,e)}var al="_reactListening"+Math.random().toString(36).slice(2);function cs(t){if(!t[al]){t[al]=!0,Tc.forEach(function(n){n!=="selectionchange"&&(C0.has(n)||ss(n,!1,t),ss(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[al]||(e[al]=!0,ss("selectionchange",!1,e))}}function xd(t,e,n,i){switch(Wd(e)){case 2:var r=ag;break;case 8:r=lg;break;default:r=As}n=r.bind(null,e,n,t),r=void 0,!Ru||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(r=!0),i?r!==void 0?t.addEventListener(e,n,{capture:!0,passive:r}):t.addEventListener(e,n,!0):r!==void 0?t.addEventListener(e,n,{passive:r}):t.addEventListener(e,n,!1)}function fs(t,e,n,i,r){var a=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var m=i.tag;if(m===3||m===4){var D=i.stateNode.containerInfo;if(D===r)break;if(m===4)for(m=i.return;m!==null;){var L=m.tag;if((L===3||L===4)&&m.stateNode.containerInfo===r)return;m=m.return}for(;D!==null;){if(m=mi(D),m===null)return;if(L=m.tag,L===5||L===6||L===26||L===27){i=a=m;continue t}D=D.parentNode}}i=i.return}Uc(function(){var G=a,k=Mu(n),nt=[];t:{var Q=sf.get(t);if(Q!==void 0){var Z=Ea,Et=t;switch(t){case"keypress":if(ga(n)===0)break t;case"keydown":case"keyup":Z=wm;break;case"focusin":Et="focus",Z=Bu;break;case"focusout":Et="blur",Z=Bu;break;case"beforeblur":case"afterblur":Z=Bu;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Z=Xc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Z=pm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Z=Mm;break;case af:case lf:case uf:Z=gm;break;case of:Z=Rm;break;case"scroll":case"scrollend":Z=hm;break;case"wheel":Z=Lm;break;case"copy":case"cut":case"paste":Z=Em;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Z=qc;break;case"toggle":case"beforetoggle":Z=Bm}var mt=(e&4)!==0,Gt=!mt&&(t==="scroll"||t==="scrollend"),H=mt?Q!==null?Q+"Capture":null:Q;mt=[];for(var q=G,F;q!==null;){var et=q;if(F=et.stateNode,et=et.tag,et!==5&&et!==26&&et!==27||F===null||H===null||(et=lr(q,H),et!=null&&mt.push(Vr(q,et,F))),Gt)break;q=q.return}0<mt.length&&(Q=new Z(Q,Et,null,n,k),nt.push({event:Q,listeners:mt}))}}if((e&7)===0){t:{if(Q=t==="mouseover"||t==="pointerover",Z=t==="mouseout"||t==="pointerout",Q&&n!==xu&&(Et=n.relatedTarget||n.fromElement)&&(mi(Et)||Et[yi]))break t;if((Z||Q)&&(Q=k.window===k?k:(Q=k.ownerDocument)?Q.defaultView||Q.parentWindow:window,Z?(Et=n.relatedTarget||n.toElement,Z=G,Et=Et?mi(Et):null,Et!==null&&(Gt=c(Et),mt=Et.tag,Et!==Gt||mt!==5&&mt!==27&&mt!==6)&&(Et=null)):(Z=null,Et=G),Z!==Et)){if(mt=Xc,et="onMouseLeave",H="onMouseEnter",q="mouse",(t==="pointerout"||t==="pointerover")&&(mt=qc,et="onPointerLeave",H="onPointerEnter",q="pointer"),Gt=Z==null?Q:ar(Z),F=Et==null?Q:ar(Et),Q=new mt(et,q+"leave",Z,n,k),Q.target=Gt,Q.relatedTarget=F,et=null,mi(k)===G&&(mt=new mt(H,q+"enter",Et,n,k),mt.target=F,mt.relatedTarget=Gt,et=mt),Gt=et,Z&&Et)e:{for(mt=Z,H=Et,q=0,F=mt;F;F=Ji(F))q++;for(F=0,et=H;et;et=Ji(et))F++;for(;0<q-F;)mt=Ji(mt),q--;for(;0<F-q;)H=Ji(H),F--;for(;q--;){if(mt===H||H!==null&&mt===H.alternate)break e;mt=Ji(mt),H=Ji(H)}mt=null}else mt=null;Z!==null&&Md(nt,Q,Z,mt,!1),Et!==null&&Gt!==null&&Md(nt,Gt,Et,mt,!0)}}t:{if(Q=G?ar(G):window,Z=Q.nodeName&&Q.nodeName.toLowerCase(),Z==="select"||Z==="input"&&Q.type==="file")var ht=Zc;else if(Ic(Q))if(Kc)ht=Im;else{ht=Fm;var Nt=Vm}else Z=Q.nodeName,!Z||Z.toLowerCase()!=="input"||Q.type!=="checkbox"&&Q.type!=="radio"?G&&Nu(G.elementType)&&(ht=Zc):ht=Gm;if(ht&&(ht=ht(t,G))){Qc(nt,ht,n,k);break t}Nt&&Nt(t,Q,G),t==="focusout"&&G&&Q.type==="number"&&G.memoizedProps.value!=null&&wu(Q,"number",Q.value)}switch(Nt=G?ar(G):window,t){case"focusin":(Ic(Nt)||Nt.contentEditable==="true")&&(wi=Nt,Yu=G,pr=null);break;case"focusout":pr=Yu=wi=null;break;case"mousedown":Vu=!0;break;case"contextmenu":case"mouseup":case"dragend":Vu=!1,nf(nt,n,k);break;case"selectionchange":if(Zm)break;case"keydown":case"keyup":nf(nt,n,k)}var pt;if(Xu)t:{switch(t){case"compositionstart":var gt="onCompositionStart";break t;case"compositionend":gt="onCompositionEnd";break t;case"compositionupdate":gt="onCompositionUpdate";break t}gt=void 0}else Oi?Fc(t,n)&&(gt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(gt="onCompositionStart");gt&&(Hc&&n.locale!=="ko"&&(Oi||gt!=="onCompositionStart"?gt==="onCompositionEnd"&&Oi&&(pt=Bc()):(On=k,Cu="value"in On?On.value:On.textContent,Oi=!0)),Nt=ll(G,gt),0<Nt.length&&(gt=new zc(gt,t,null,n,k),nt.push({event:gt,listeners:Nt}),pt?gt.data=pt:(pt=Gc(n),pt!==null&&(gt.data=pt)))),(pt=Xm?zm(t,n):qm(t,n))&&(gt=ll(G,"onBeforeInput"),0<gt.length&&(Nt=new zc("onBeforeInput","beforeinput",null,n,k),nt.push({event:Nt,listeners:gt}),Nt.data=pt)),M0(nt,t,G,n,k)}Nd(nt,e)})}function Vr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function ll(t,e){for(var n=e+"Capture",i=[];t!==null;){var r=t,a=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||a===null||(r=lr(t,n),r!=null&&i.unshift(Vr(t,r,a)),r=lr(t,e),r!=null&&i.push(Vr(t,r,a))),t.tag===3)return i;t=t.return}return[]}function Ji(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Md(t,e,n,i,r){for(var a=e._reactName,m=[];n!==null&&n!==i;){var D=n,L=D.alternate,G=D.stateNode;if(D=D.tag,L!==null&&L===i)break;D!==5&&D!==26&&D!==27||G===null||(L=G,r?(G=lr(n,a),G!=null&&m.unshift(Vr(n,G,L))):r||(G=lr(n,a),G!=null&&m.push(Vr(n,G,L)))),n=n.return}m.length!==0&&t.push({event:e,listeners:m})}var L0=/\r\n?/g,U0=/\u0000|\uFFFD/g;function _d(t){return(typeof t=="string"?t:""+t).replace(L0,`
`).replace(U0,"")}function Rd(t,e){return e=_d(e),_d(t)===e}function ul(){}function Ft(t,e,n,i,r,a){switch(n){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||Si(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&Si(t,""+i);break;case"className":ha(t,"class",i);break;case"tabIndex":ha(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":ha(t,n,i);break;case"style":Cc(t,i,a);break;case"data":if(e!=="object"){ha(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=ya(""+i),t.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof a=="function"&&(n==="formAction"?(e!=="input"&&Ft(t,e,"name",r.name,r,null),Ft(t,e,"formEncType",r.formEncType,r,null),Ft(t,e,"formMethod",r.formMethod,r,null),Ft(t,e,"formTarget",r.formTarget,r,null)):(Ft(t,e,"encType",r.encType,r,null),Ft(t,e,"method",r.method,r,null),Ft(t,e,"target",r.target,r,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=ya(""+i),t.setAttribute(n,i);break;case"onClick":i!=null&&(t.onclick=ul);break;case"onScroll":i!=null&&_t("scroll",t);break;case"onScrollEnd":i!=null&&_t("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(r.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}n=ya(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""+i):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":i===!0?t.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,i):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(n,i):t.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(n):t.setAttribute(n,i);break;case"popover":_t("beforetoggle",t),_t("toggle",t),fa(t,"popover",i);break;case"xlinkActuate":on(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":on(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":on(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":on(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":on(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":on(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":on(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":on(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":on(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":fa(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=cm.get(n)||n,fa(t,n,i))}}function hs(t,e,n,i,r,a){switch(n){case"style":Cc(t,i,a);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(o(61));if(n=i.__html,n!=null){if(r.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof i=="string"?Si(t,i):(typeof i=="number"||typeof i=="bigint")&&Si(t,""+i);break;case"onScroll":i!=null&&_t("scroll",t);break;case"onScrollEnd":i!=null&&_t("scrollend",t);break;case"onClick":i!=null&&(t.onclick=ul);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!bc.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(r=n.endsWith("Capture"),e=n.slice(2,r?n.length-7:void 0),a=t[De]||null,a=a!=null?a[n]:null,typeof a=="function"&&t.removeEventListener(e,a,r),typeof i=="function")){typeof a!="function"&&a!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,i,r);break t}n in t?t[n]=i:i===!0?t.setAttribute(n,""):fa(t,n,i)}}}function me(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_t("error",t),_t("load",t);var i=!1,r=!1,a;for(a in n)if(n.hasOwnProperty(a)){var m=n[a];if(m!=null)switch(a){case"src":i=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Ft(t,e,a,m,n,null)}}r&&Ft(t,e,"srcSet",n.srcSet,n,null),i&&Ft(t,e,"src",n.src,n,null);return;case"input":_t("invalid",t);var D=a=m=r=null,L=null,G=null;for(i in n)if(n.hasOwnProperty(i)){var k=n[i];if(k!=null)switch(i){case"name":r=k;break;case"type":m=k;break;case"checked":L=k;break;case"defaultChecked":G=k;break;case"value":a=k;break;case"defaultValue":D=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(o(137,e));break;default:Ft(t,e,i,k,n,null)}}xc(t,a,D,L,G,m,r,!1),da(t);return;case"select":_t("invalid",t),i=m=a=null;for(r in n)if(n.hasOwnProperty(r)&&(D=n[r],D!=null))switch(r){case"value":a=D;break;case"defaultValue":m=D;break;case"multiple":i=D;default:Ft(t,e,r,D,n,null)}e=a,n=m,t.multiple=!!i,e!=null?bi(t,!!i,e,!1):n!=null&&bi(t,!!i,n,!0);return;case"textarea":_t("invalid",t),a=r=i=null;for(m in n)if(n.hasOwnProperty(m)&&(D=n[m],D!=null))switch(m){case"value":i=D;break;case"defaultValue":r=D;break;case"children":a=D;break;case"dangerouslySetInnerHTML":if(D!=null)throw Error(o(91));break;default:Ft(t,e,m,D,n,null)}_c(t,i,r,a),da(t);return;case"option":for(L in n)if(n.hasOwnProperty(L)&&(i=n[L],i!=null))switch(L){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:Ft(t,e,L,i,n,null)}return;case"dialog":_t("beforetoggle",t),_t("toggle",t),_t("cancel",t),_t("close",t);break;case"iframe":case"object":_t("load",t);break;case"video":case"audio":for(i=0;i<Yr.length;i++)_t(Yr[i],t);break;case"image":_t("error",t),_t("load",t);break;case"details":_t("toggle",t);break;case"embed":case"source":case"link":_t("error",t),_t("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(G in n)if(n.hasOwnProperty(G)&&(i=n[G],i!=null))switch(G){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Ft(t,e,G,i,n,null)}return;default:if(Nu(e)){for(k in n)n.hasOwnProperty(k)&&(i=n[k],i!==void 0&&hs(t,e,k,i,n,void 0));return}}for(D in n)n.hasOwnProperty(D)&&(i=n[D],i!=null&&Ft(t,e,D,i,n,null))}function B0(t,e,n,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,a=null,m=null,D=null,L=null,G=null,k=null;for(Z in n){var nt=n[Z];if(n.hasOwnProperty(Z)&&nt!=null)switch(Z){case"checked":break;case"value":break;case"defaultValue":L=nt;default:i.hasOwnProperty(Z)||Ft(t,e,Z,null,i,nt)}}for(var Q in i){var Z=i[Q];if(nt=n[Q],i.hasOwnProperty(Q)&&(Z!=null||nt!=null))switch(Q){case"type":a=Z;break;case"name":r=Z;break;case"checked":G=Z;break;case"defaultChecked":k=Z;break;case"value":m=Z;break;case"defaultValue":D=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(o(137,e));break;default:Z!==nt&&Ft(t,e,Q,Z,i,nt)}}Ou(t,m,D,L,G,k,a,r);return;case"select":Z=m=D=Q=null;for(a in n)if(L=n[a],n.hasOwnProperty(a)&&L!=null)switch(a){case"value":break;case"multiple":Z=L;default:i.hasOwnProperty(a)||Ft(t,e,a,null,i,L)}for(r in i)if(a=i[r],L=n[r],i.hasOwnProperty(r)&&(a!=null||L!=null))switch(r){case"value":Q=a;break;case"defaultValue":D=a;break;case"multiple":m=a;default:a!==L&&Ft(t,e,r,a,i,L)}e=D,n=m,i=Z,Q!=null?bi(t,!!n,Q,!1):!!i!=!!n&&(e!=null?bi(t,!!n,e,!0):bi(t,!!n,n?[]:"",!1));return;case"textarea":Z=Q=null;for(D in n)if(r=n[D],n.hasOwnProperty(D)&&r!=null&&!i.hasOwnProperty(D))switch(D){case"value":break;case"children":break;default:Ft(t,e,D,null,i,r)}for(m in i)if(r=i[m],a=n[m],i.hasOwnProperty(m)&&(r!=null||a!=null))switch(m){case"value":Q=r;break;case"defaultValue":Z=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(o(91));break;default:r!==a&&Ft(t,e,m,r,i,a)}Mc(t,Q,Z);return;case"option":for(var Et in n)if(Q=n[Et],n.hasOwnProperty(Et)&&Q!=null&&!i.hasOwnProperty(Et))switch(Et){case"selected":t.selected=!1;break;default:Ft(t,e,Et,null,i,Q)}for(L in i)if(Q=i[L],Z=n[L],i.hasOwnProperty(L)&&Q!==Z&&(Q!=null||Z!=null))switch(L){case"selected":t.selected=Q&&typeof Q!="function"&&typeof Q!="symbol";break;default:Ft(t,e,L,Q,i,Z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var mt in n)Q=n[mt],n.hasOwnProperty(mt)&&Q!=null&&!i.hasOwnProperty(mt)&&Ft(t,e,mt,null,i,Q);for(G in i)if(Q=i[G],Z=n[G],i.hasOwnProperty(G)&&Q!==Z&&(Q!=null||Z!=null))switch(G){case"children":case"dangerouslySetInnerHTML":if(Q!=null)throw Error(o(137,e));break;default:Ft(t,e,G,Q,i,Z)}return;default:if(Nu(e)){for(var Gt in n)Q=n[Gt],n.hasOwnProperty(Gt)&&Q!==void 0&&!i.hasOwnProperty(Gt)&&hs(t,e,Gt,void 0,i,Q);for(k in i)Q=i[k],Z=n[k],!i.hasOwnProperty(k)||Q===Z||Q===void 0&&Z===void 0||hs(t,e,k,Q,i,Z);return}}for(var H in n)Q=n[H],n.hasOwnProperty(H)&&Q!=null&&!i.hasOwnProperty(H)&&Ft(t,e,H,null,i,Q);for(nt in i)Q=i[nt],Z=n[nt],!i.hasOwnProperty(nt)||Q===Z||Q==null&&Z==null||Ft(t,e,nt,Q,i,Z)}var ds=null,ps=null;function ol(t){return t.nodeType===9?t:t.ownerDocument}function Cd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ld(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ys(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ms=null;function j0(){var t=window.event;return t&&t.type==="popstate"?t===ms?!1:(ms=t,!0):(ms=null,!1)}var Ud=typeof setTimeout=="function"?setTimeout:void 0,X0=typeof clearTimeout=="function"?clearTimeout:void 0,Bd=typeof Promise=="function"?Promise:void 0,z0=typeof queueMicrotask=="function"?queueMicrotask:typeof Bd<"u"?function(t){return Bd.resolve(null).then(t).catch(q0)}:Ud;function q0(t){setTimeout(function(){throw t})}function Yn(t){return t==="head"}function jd(t,e){var n=e,i=0,r=0;do{var a=n.nextSibling;if(t.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(0<i&&8>i){n=i;var m=t.ownerDocument;if(n&1&&Fr(m.documentElement),n&2&&Fr(m.body),n&4)for(n=m.head,Fr(n),m=n.firstChild;m;){var D=m.nextSibling,L=m.nodeName;m[rr]||L==="SCRIPT"||L==="STYLE"||L==="LINK"&&m.rel.toLowerCase()==="stylesheet"||n.removeChild(m),m=D}}if(r===0){t.removeChild(a),kr(e);return}r--}else n==="$"||n==="$?"||n==="$!"?r++:i=n.charCodeAt(0)-48;else i=0;n=a}while(n);kr(e)}function gs(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gs(n),bu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function H0(t,e,n,i){for(;t.nodeType===1;){var r=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[rr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(a=t.getAttribute("rel"),a==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(a!==r.rel||t.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||t.getAttribute("title")!==(r.title==null?null:r.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(a=t.getAttribute("src"),(a!==(r.src==null?null:r.src)||t.getAttribute("type")!==(r.type==null?null:r.type)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&a&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var a=r.name==null?null:""+r.name;if(r.type==="hidden"&&t.getAttribute("name")===a)return t}else return t;if(t=Je(t.nextSibling),t===null)break}return null}function Y0(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Je(t.nextSibling),t===null))return null;return t}function vs(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function V0(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var i=function(){e(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function Je(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Es=null;function Xd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function zd(t,e,n){switch(e=ol(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Fr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);bu(t)}var Qe=new Map,qd=new Set;function sl(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Sn=rt.d;rt.d={f:F0,r:G0,D:I0,C:Q0,L:Z0,m:K0,X:J0,S:P0,M:k0};function F0(){var t=Sn.f(),e=tl();return t||e}function G0(t){var e=gi(t);e!==null&&e.tag===5&&e.type==="form"?ah(e):Sn.r(t)}var ki=typeof document>"u"?null:document;function Hd(t,e,n){var i=ki;if(i&&typeof e=="string"&&e){var r=qe(e);r='link[rel="'+t+'"][href="'+r+'"]',typeof n=="string"&&(r+='[crossorigin="'+n+'"]'),qd.has(r)||(qd.add(r),t={rel:t,crossOrigin:n,href:e},i.querySelector(r)===null&&(e=i.createElement("link"),me(e,"link",t),ce(e),i.head.appendChild(e)))}}function I0(t){Sn.D(t),Hd("dns-prefetch",t,null)}function Q0(t,e){Sn.C(t,e),Hd("preconnect",t,e)}function Z0(t,e,n){Sn.L(t,e,n);var i=ki;if(i&&t&&e){var r='link[rel="preload"][as="'+qe(e)+'"]';e==="image"&&n&&n.imageSrcSet?(r+='[imagesrcset="'+qe(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(r+='[imagesizes="'+qe(n.imageSizes)+'"]')):r+='[href="'+qe(t)+'"]';var a=r;switch(e){case"style":a=Wi(t);break;case"script":a=$i(t)}Qe.has(a)||(t=S({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Qe.set(a,t),i.querySelector(r)!==null||e==="style"&&i.querySelector(Gr(a))||e==="script"&&i.querySelector(Ir(a))||(e=i.createElement("link"),me(e,"link",t),ce(e),i.head.appendChild(e)))}}function K0(t,e){Sn.m(t,e);var n=ki;if(n&&t){var i=e&&typeof e.as=="string"?e.as:"script",r='link[rel="modulepreload"][as="'+qe(i)+'"][href="'+qe(t)+'"]',a=r;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=$i(t)}if(!Qe.has(a)&&(t=S({rel:"modulepreload",href:t},e),Qe.set(a,t),n.querySelector(r)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ir(a)))return}i=n.createElement("link"),me(i,"link",t),ce(i),n.head.appendChild(i)}}}function P0(t,e,n){Sn.S(t,e,n);var i=ki;if(i&&t){var r=vi(i).hoistableStyles,a=Wi(t);e=e||"default";var m=r.get(a);if(!m){var D={loading:0,preload:null};if(m=i.querySelector(Gr(a)))D.loading=5;else{t=S({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Qe.get(a))&&Ts(t,n);var L=m=i.createElement("link");ce(L),me(L,"link",t),L._p=new Promise(function(G,k){L.onload=G,L.onerror=k}),L.addEventListener("load",function(){D.loading|=1}),L.addEventListener("error",function(){D.loading|=2}),D.loading|=4,cl(m,e,i)}m={type:"stylesheet",instance:m,count:1,state:D},r.set(a,m)}}}function J0(t,e){Sn.X(t,e);var n=ki;if(n&&t){var i=vi(n).hoistableScripts,r=$i(t),a=i.get(r);a||(a=n.querySelector(Ir(r)),a||(t=S({src:t,async:!0},e),(e=Qe.get(r))&&bs(t,e),a=n.createElement("script"),ce(a),me(a,"link",t),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}}function k0(t,e){Sn.M(t,e);var n=ki;if(n&&t){var i=vi(n).hoistableScripts,r=$i(t),a=i.get(r);a||(a=n.querySelector(Ir(r)),a||(t=S({src:t,async:!0,type:"module"},e),(e=Qe.get(r))&&bs(t,e),a=n.createElement("script"),ce(a),me(a,"link",t),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}}function Yd(t,e,n,i){var r=(r=vt.current)?sl(r):null;if(!r)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Wi(n.href),n=vi(r).hoistableStyles,i=n.get(e),i||(i={type:"style",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Wi(n.href);var a=vi(r).hoistableStyles,m=a.get(t);if(m||(r=r.ownerDocument||r,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},a.set(t,m),(a=r.querySelector(Gr(t)))&&!a._p&&(m.instance=a,m.state.loading=5),Qe.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Qe.set(t,n),a||W0(r,t,n,m.state))),e&&i===null)throw Error(o(528,""));return m}if(e&&i!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$i(n),n=vi(r).hoistableScripts,i=n.get(e),i||(i={type:"script",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Wi(t){return'href="'+qe(t)+'"'}function Gr(t){return'link[rel="stylesheet"]['+t+"]"}function Vd(t){return S({},t,{"data-precedence":t.precedence,precedence:null})}function W0(t,e,n,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),me(e,"link",n),ce(e),t.head.appendChild(e))}function $i(t){return'[src="'+qe(t)+'"]'}function Ir(t){return"script[async]"+t}function Fd(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+qe(n.href)+'"]');if(i)return e.instance=i,ce(i),i;var r=S({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),ce(i),me(i,"style",r),cl(i,n.precedence,t),e.instance=i;case"stylesheet":r=Wi(n.href);var a=t.querySelector(Gr(r));if(a)return e.state.loading|=4,e.instance=a,ce(a),a;i=Vd(n),(r=Qe.get(r))&&Ts(i,r),a=(t.ownerDocument||t).createElement("link"),ce(a);var m=a;return m._p=new Promise(function(D,L){m.onload=D,m.onerror=L}),me(a,"link",i),e.state.loading|=4,cl(a,n.precedence,t),e.instance=a;case"script":return a=$i(n.src),(r=t.querySelector(Ir(a)))?(e.instance=r,ce(r),r):(i=n,(r=Qe.get(a))&&(i=S({},n),bs(i,r)),t=t.ownerDocument||t,r=t.createElement("script"),ce(r),me(r,"link",i),t.head.appendChild(r),e.instance=r);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,cl(i,n.precedence,t));return e.instance}function cl(t,e,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=i.length?i[i.length-1]:null,a=r,m=0;m<i.length;m++){var D=i[m];if(D.dataset.precedence===e)a=D;else if(a!==r)break}a?a.parentNode.insertBefore(t,a.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Ts(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function bs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var fl=null;function Gd(t,e,n){if(fl===null){var i=new Map,r=fl=new Map;r.set(n,i)}else r=fl,i=r.get(n),i||(i=new Map,r.set(n,i));if(i.has(t))return i;for(i.set(t,null),n=n.getElementsByTagName(t),r=0;r<n.length;r++){var a=n[r];if(!(a[rr]||a[ve]||t==="link"&&a.getAttribute("rel")==="stylesheet")&&a.namespaceURI!=="http://www.w3.org/2000/svg"){var m=a.getAttribute(e)||"";m=t+m;var D=i.get(m);D?D.push(a):i.set(m,[a])}}return i}function Id(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function $0(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Qd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Qr=null;function tg(){}function eg(t,e,n){if(Qr===null)throw Error(o(475));var i=Qr;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var r=Wi(n.href),a=t.querySelector(Gr(r));if(a){t=a._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=hl.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=a,ce(a);return}a=t.ownerDocument||t,n=Vd(n),(r=Qe.get(r))&&Ts(n,r),a=a.createElement("link"),ce(a);var m=a;m._p=new Promise(function(D,L){m.onload=D,m.onerror=L}),me(a,"link",n),e.instance=a}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=hl.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function ng(){if(Qr===null)throw Error(o(475));var t=Qr;return t.stylesheets&&t.count===0&&Ss(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Ss(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function hl(){if(this.count--,this.count===0){if(this.stylesheets)Ss(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var dl=null;function Ss(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,dl=new Map,e.forEach(ig,t),dl=null,hl.call(t))}function ig(t,e){if(!(e.state.loading&4)){var n=dl.get(t);if(n)var i=n.get(null);else{n=new Map,dl.set(t,n);for(var r=t.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<r.length;a++){var m=r[a];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(n.set(m.dataset.precedence,m),i=m)}i&&n.set(null,i)}r=e.instance,m=r.getAttribute("data-precedence"),a=n.get(m)||i,a===i&&n.set(null,r),n.set(m,r),this.count++,i=hl.bind(this),r.addEventListener("load",i),r.addEventListener("error",i),a?a.parentNode.insertBefore(r,a.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(r,t.firstChild)),e.state.loading|=4}}var Zr={$$typeof:E,Provider:null,Consumer:null,_currentValue:yt,_currentValue2:yt,_threadCount:0};function rg(t,e,n,i,r,a,m,D){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=gu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gu(0),this.hiddenUpdates=gu(null),this.identifierPrefix=i,this.onUncaughtError=r,this.onCaughtError=a,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=D,this.incompleteTransitions=new Map}function Zd(t,e,n,i,r,a,m,D,L,G,k,nt){return t=new rg(t,e,n,m,D,L,G,nt),e=1,a===!0&&(e|=24),a=Ce(3,null,null,e),t.current=a,a.stateNode=t,e=no(),e.refCount++,t.pooledCache=e,e.refCount++,a.memoizedState={element:i,isDehydrated:n,cache:e},lo(a),t}function Kd(t){return t?(t=_i,t):_i}function Pd(t,e,n,i,r,a){r=Kd(r),i.context===null?i.context=r:i.pendingContext=r,i=xn(e),i.payload={element:n},a=a===void 0?null:a,a!==null&&(i.callback=a),n=Mn(t,i,e),n!==null&&(Xe(n,t,e),Dr(n,t,e))}function Jd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Ds(t,e){Jd(t,e),(t=t.alternate)&&Jd(t,e)}function kd(t){if(t.tag===13){var e=Mi(t,67108864);e!==null&&Xe(e,t,67108864),Ds(t,67108864)}}var pl=!0;function ag(t,e,n,i){var r=I.T;I.T=null;var a=rt.p;try{rt.p=2,As(t,e,n,i)}finally{rt.p=a,I.T=r}}function lg(t,e,n,i){var r=I.T;I.T=null;var a=rt.p;try{rt.p=8,As(t,e,n,i)}finally{rt.p=a,I.T=r}}function As(t,e,n,i){if(pl){var r=Os(i);if(r===null)fs(t,e,i,yl,n),$d(t,i);else if(og(r,t,e,n,i))i.stopPropagation();else if($d(t,i),e&4&&-1<ug.indexOf(t)){for(;r!==null;){var a=gi(r);if(a!==null)switch(a.tag){case 3:if(a=a.stateNode,a.current.memoizedState.isDehydrated){var m=Kn(a.pendingLanes);if(m!==0){var D=a;for(D.pendingLanes|=2,D.entangledLanes|=2;m;){var L=1<<31-ge(m);D.entanglements[1]|=L,m&=~L}nn(a),(qt&6)===0&&(Wa=p()+500,Hr(0))}}break;case 13:D=Mi(a,2),D!==null&&Xe(D,a,2),tl(),Ds(a,2)}if(a=Os(i),a===null&&fs(t,e,i,yl,n),a===r)break;r=a}r!==null&&i.stopPropagation()}else fs(t,e,i,null,n)}}function Os(t){return t=Mu(t),ws(t)}var yl=null;function ws(t){if(yl=null,t=mi(t),t!==null){var e=c(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=s(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return yl=t,null}function Wd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(y()){case R:return 2;case V:return 8;case at:case ot:return 32;case Rt:return 268435456;default:return 32}default:return 32}}var Ns=!1,Vn=null,Fn=null,Gn=null,Kr=new Map,Pr=new Map,In=[],ug="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function $d(t,e){switch(t){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":Fn=null;break;case"mouseover":case"mouseout":Gn=null;break;case"pointerover":case"pointerout":Kr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pr.delete(e.pointerId)}}function Jr(t,e,n,i,r,a){return t===null||t.nativeEvent!==a?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:a,targetContainers:[r]},e!==null&&(e=gi(e),e!==null&&kd(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,r!==null&&e.indexOf(r)===-1&&e.push(r),t)}function og(t,e,n,i,r){switch(e){case"focusin":return Vn=Jr(Vn,t,e,n,i,r),!0;case"dragenter":return Fn=Jr(Fn,t,e,n,i,r),!0;case"mouseover":return Gn=Jr(Gn,t,e,n,i,r),!0;case"pointerover":var a=r.pointerId;return Kr.set(a,Jr(Kr.get(a)||null,t,e,n,i,r)),!0;case"gotpointercapture":return a=r.pointerId,Pr.set(a,Jr(Pr.get(a)||null,t,e,n,i,r)),!0}return!1}function tp(t){var e=mi(t.target);if(e!==null){var n=c(e);if(n!==null){if(e=n.tag,e===13){if(e=s(n),e!==null){t.blockedOn=e,em(t.priority,function(){if(n.tag===13){var i=je();i=vu(i);var r=Mi(n,i);r!==null&&Xe(r,n,i),Ds(n,i)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ml(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Os(t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);xu=i,n.target.dispatchEvent(i),xu=null}else return e=gi(n),e!==null&&kd(e),t.blockedOn=n,!1;e.shift()}return!0}function ep(t,e,n){ml(t)&&n.delete(e)}function sg(){Ns=!1,Vn!==null&&ml(Vn)&&(Vn=null),Fn!==null&&ml(Fn)&&(Fn=null),Gn!==null&&ml(Gn)&&(Gn=null),Kr.forEach(ep),Pr.forEach(ep)}function gl(t,e){t.blockedOn===e&&(t.blockedOn=null,Ns||(Ns=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,sg)))}var vl=null;function np(t){vl!==t&&(vl=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){vl===t&&(vl=null);for(var e=0;e<t.length;e+=3){var n=t[e],i=t[e+1],r=t[e+2];if(typeof i!="function"){if(ws(i||n)===null)continue;break}var a=gi(n);a!==null&&(t.splice(e,3),e-=3,wo(a,{pending:!0,data:r,method:n.method,action:i},i,r))}}))}function kr(t){function e(L){return gl(L,t)}Vn!==null&&gl(Vn,t),Fn!==null&&gl(Fn,t),Gn!==null&&gl(Gn,t),Kr.forEach(e),Pr.forEach(e);for(var n=0;n<In.length;n++){var i=In[n];i.blockedOn===t&&(i.blockedOn=null)}for(;0<In.length&&(n=In[0],n.blockedOn===null);)tp(n),n.blockedOn===null&&In.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var r=n[i],a=n[i+1],m=r[De]||null;if(typeof a=="function")m||np(n);else if(m){var D=null;if(a&&a.hasAttribute("formAction")){if(r=a,m=a[De]||null)D=m.formAction;else if(ws(r)!==null)continue}else D=m.action;typeof D=="function"?n[i+1]=D:(n.splice(i,3),i-=3),np(n)}}}function xs(t){this._internalRoot=t}El.prototype.render=xs.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,i=je();Pd(n,i,t,e,null,null)},El.prototype.unmount=xs.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Pd(t.current,2,null,t,null,null),tl(),e[yi]=null}};function El(t){this._internalRoot=t}El.prototype.unstable_scheduleHydration=function(t){if(t){var e=vc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<In.length&&e!==0&&e<In[n].priority;n++);In.splice(n,0,t),n===0&&tp(t)}};var ip=l.version;if(ip!=="19.1.1")throw Error(o(527,ip,"19.1.1"));rt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=g(e),t=t!==null?b(t):null,t=t===null?null:t.stateNode,t};var cg={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:I,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Tl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Tl.isDisabled&&Tl.supportsFiber)try{Wt=Tl.inject(cg),Ct=Tl}catch{}}return $r.createRoot=function(t,e){if(!d(t))throw Error(o(299));var n=!1,i="",r=Eh,a=Th,m=bh,D=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(r=e.onUncaughtError),e.onCaughtError!==void 0&&(a=e.onCaughtError),e.onRecoverableError!==void 0&&(m=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(D=e.unstable_transitionCallbacks)),e=Zd(t,1,!1,null,null,n,i,r,a,m,D,null),t[yi]=e.current,cs(t),new xs(e)},$r.hydrateRoot=function(t,e,n){if(!d(t))throw Error(o(299));var i=!1,r="",a=Eh,m=Th,D=bh,L=null,G=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(a=n.onUncaughtError),n.onCaughtError!==void 0&&(m=n.onCaughtError),n.onRecoverableError!==void 0&&(D=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(L=n.unstable_transitionCallbacks),n.formState!==void 0&&(G=n.formState)),e=Zd(t,1,!0,e,n??null,i,r,a,m,D,L,G),e.context=Kd(null),n=e.current,i=je(),i=vu(i),r=xn(i),r.callback=null,Mn(n,r,i),n=i,e.current.lanes=n,ir(e,n),nn(e),t[yi]=e.current,cs(t),new El(e)},$r.version="19.1.1",$r}var dp;function bg(){if(dp)return Rs.exports;dp=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(l){console.error(l)}}return u(),Rs.exports=Tg(),Rs.exports}var Sg=bg();const Dg=()=>{const[u,l]=Kt.useState(navigator.onLine),[h,o]=Kt.useState(new Date);return Kt.useEffect(()=>{const d=()=>{l(!0),o(new Date)},c=()=>{l(!1),o(new Date)},s=async()=>{try{const g=await fetch("https://www.google.com/favicon.ico",{method:"HEAD",mode:"no-cors",cache:"no-cache"});l(!0)}catch{l(!1)}o(new Date)};window.addEventListener("online",d),window.addEventListener("offline",c);const f=setInterval(s,3e4);return s(),()=>{window.removeEventListener("online",d),window.removeEventListener("offline",c),clearInterval(f)}},[]),lt.jsxs("div",{className:"status-container",children:[lt.jsx("div",{className:"status-header",children:lt.jsx("h3",{children:"Internet Connection"})}),lt.jsxs("div",{className:`status-indicator ${u?"online":"offline"}`,children:[lt.jsx("div",{className:"status-icon",children:u?"🌐":"❌"}),lt.jsxs("div",{className:"status-details",children:[lt.jsx("div",{className:"status-text",children:u?"Connected":"Disconnected"}),lt.jsxs("div",{className:"status-timestamp",children:["Last checked: ",h.toLocaleTimeString()]})]})]})]})};function vy(u,l){return function(){return u.apply(l,arguments)}}const{toString:Ag}=Object.prototype,{getPrototypeOf:Ps}=Object,{iterator:su,toStringTag:Ey}=Symbol,cu=(u=>l=>{const h=Ag.call(l);return u[h]||(u[h]=h.slice(8,-1).toLowerCase())})(Object.create(null)),ke=u=>(u=u.toLowerCase(),l=>cu(l)===u),fu=u=>l=>typeof l===u,{isArray:tr}=Array,na=fu("undefined");function ia(u){return u!==null&&!na(u)&&u.constructor!==null&&!na(u.constructor)&&xe(u.constructor.isBuffer)&&u.constructor.isBuffer(u)}const Ty=ke("ArrayBuffer");function Og(u){let l;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?l=ArrayBuffer.isView(u):l=u&&u.buffer&&Ty(u.buffer),l}const wg=fu("string"),xe=fu("function"),by=fu("number"),ra=u=>u!==null&&typeof u=="object",Ng=u=>u===!0||u===!1,Nl=u=>{if(cu(u)!=="object")return!1;const l=Ps(u);return(l===null||l===Object.prototype||Object.getPrototypeOf(l)===null)&&!(Ey in u)&&!(su in u)},xg=u=>{if(!ra(u)||ia(u))return!1;try{return Object.keys(u).length===0&&Object.getPrototypeOf(u)===Object.prototype}catch{return!1}},Mg=ke("Date"),_g=ke("File"),Rg=ke("Blob"),Cg=ke("FileList"),Lg=u=>ra(u)&&xe(u.pipe),Ug=u=>{let l;return u&&(typeof FormData=="function"&&u instanceof FormData||xe(u.append)&&((l=cu(u))==="formdata"||l==="object"&&xe(u.toString)&&u.toString()==="[object FormData]"))},Bg=ke("URLSearchParams"),[jg,Xg,zg,qg]=["ReadableStream","Request","Response","Headers"].map(ke),Hg=u=>u.trim?u.trim():u.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function aa(u,l,{allOwnKeys:h=!1}={}){if(u===null||typeof u>"u")return;let o,d;if(typeof u!="object"&&(u=[u]),tr(u))for(o=0,d=u.length;o<d;o++)l.call(null,u[o],o,u);else{if(ia(u))return;const c=h?Object.getOwnPropertyNames(u):Object.keys(u),s=c.length;let f;for(o=0;o<s;o++)f=c[o],l.call(null,u[f],f,u)}}function Sy(u,l){if(ia(u))return null;l=l.toLowerCase();const h=Object.keys(u);let o=h.length,d;for(;o-- >0;)if(d=h[o],l===d.toLowerCase())return d;return null}const hi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Dy=u=>!na(u)&&u!==hi;function Ys(){const{caseless:u}=Dy(this)&&this||{},l={},h=(o,d)=>{const c=u&&Sy(l,d)||d;Nl(l[c])&&Nl(o)?l[c]=Ys(l[c],o):Nl(o)?l[c]=Ys({},o):tr(o)?l[c]=o.slice():l[c]=o};for(let o=0,d=arguments.length;o<d;o++)arguments[o]&&aa(arguments[o],h);return l}const Yg=(u,l,h,{allOwnKeys:o}={})=>(aa(l,(d,c)=>{h&&xe(d)?u[c]=vy(d,h):u[c]=d},{allOwnKeys:o}),u),Vg=u=>(u.charCodeAt(0)===65279&&(u=u.slice(1)),u),Fg=(u,l,h,o)=>{u.prototype=Object.create(l.prototype,o),u.prototype.constructor=u,Object.defineProperty(u,"super",{value:l.prototype}),h&&Object.assign(u.prototype,h)},Gg=(u,l,h,o)=>{let d,c,s;const f={};if(l=l||{},u==null)return l;do{for(d=Object.getOwnPropertyNames(u),c=d.length;c-- >0;)s=d[c],(!o||o(s,u,l))&&!f[s]&&(l[s]=u[s],f[s]=!0);u=h!==!1&&Ps(u)}while(u&&(!h||h(u,l))&&u!==Object.prototype);return l},Ig=(u,l,h)=>{u=String(u),(h===void 0||h>u.length)&&(h=u.length),h-=l.length;const o=u.indexOf(l,h);return o!==-1&&o===h},Qg=u=>{if(!u)return null;if(tr(u))return u;let l=u.length;if(!by(l))return null;const h=new Array(l);for(;l-- >0;)h[l]=u[l];return h},Zg=(u=>l=>u&&l instanceof u)(typeof Uint8Array<"u"&&Ps(Uint8Array)),Kg=(u,l)=>{const o=(u&&u[su]).call(u);let d;for(;(d=o.next())&&!d.done;){const c=d.value;l.call(u,c[0],c[1])}},Pg=(u,l)=>{let h;const o=[];for(;(h=u.exec(l))!==null;)o.push(h);return o},Jg=ke("HTMLFormElement"),kg=u=>u.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(h,o,d){return o.toUpperCase()+d}),pp=(({hasOwnProperty:u})=>(l,h)=>u.call(l,h))(Object.prototype),Wg=ke("RegExp"),Ay=(u,l)=>{const h=Object.getOwnPropertyDescriptors(u),o={};aa(h,(d,c)=>{let s;(s=l(d,c,u))!==!1&&(o[c]=s||d)}),Object.defineProperties(u,o)},$g=u=>{Ay(u,(l,h)=>{if(xe(u)&&["arguments","caller","callee"].indexOf(h)!==-1)return!1;const o=u[h];if(xe(o)){if(l.enumerable=!1,"writable"in l){l.writable=!1;return}l.set||(l.set=()=>{throw Error("Can not rewrite read-only method '"+h+"'")})}})},t1=(u,l)=>{const h={},o=d=>{d.forEach(c=>{h[c]=!0})};return tr(u)?o(u):o(String(u).split(l)),h},e1=()=>{},n1=(u,l)=>u!=null&&Number.isFinite(u=+u)?u:l;function i1(u){return!!(u&&xe(u.append)&&u[Ey]==="FormData"&&u[su])}const r1=u=>{const l=new Array(10),h=(o,d)=>{if(ra(o)){if(l.indexOf(o)>=0)return;if(ia(o))return o;if(!("toJSON"in o)){l[d]=o;const c=tr(o)?[]:{};return aa(o,(s,f)=>{const g=h(s,d+1);!na(g)&&(c[f]=g)}),l[d]=void 0,c}}return o};return h(u,0)},a1=ke("AsyncFunction"),l1=u=>u&&(ra(u)||xe(u))&&xe(u.then)&&xe(u.catch),Oy=((u,l)=>u?setImmediate:l?((h,o)=>(hi.addEventListener("message",({source:d,data:c})=>{d===hi&&c===h&&o.length&&o.shift()()},!1),d=>{o.push(d),hi.postMessage(h,"*")}))(`axios@${Math.random()}`,[]):h=>setTimeout(h))(typeof setImmediate=="function",xe(hi.postMessage)),u1=typeof queueMicrotask<"u"?queueMicrotask.bind(hi):typeof process<"u"&&process.nextTick||Oy,o1=u=>u!=null&&xe(u[su]),K={isArray:tr,isArrayBuffer:Ty,isBuffer:ia,isFormData:Ug,isArrayBufferView:Og,isString:wg,isNumber:by,isBoolean:Ng,isObject:ra,isPlainObject:Nl,isEmptyObject:xg,isReadableStream:jg,isRequest:Xg,isResponse:zg,isHeaders:qg,isUndefined:na,isDate:Mg,isFile:_g,isBlob:Rg,isRegExp:Wg,isFunction:xe,isStream:Lg,isURLSearchParams:Bg,isTypedArray:Zg,isFileList:Cg,forEach:aa,merge:Ys,extend:Yg,trim:Hg,stripBOM:Vg,inherits:Fg,toFlatObject:Gg,kindOf:cu,kindOfTest:ke,endsWith:Ig,toArray:Qg,forEachEntry:Kg,matchAll:Pg,isHTMLForm:Jg,hasOwnProperty:pp,hasOwnProp:pp,reduceDescriptors:Ay,freezeMethods:$g,toObjectSet:t1,toCamelCase:kg,noop:e1,toFiniteNumber:n1,findKey:Sy,global:hi,isContextDefined:Dy,isSpecCompliantForm:i1,toJSONObject:r1,isAsyncFn:a1,isThenable:l1,setImmediate:Oy,asap:u1,isIterable:o1};function Dt(u,l,h,o,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=u,this.name="AxiosError",l&&(this.code=l),h&&(this.config=h),o&&(this.request=o),d&&(this.response=d,this.status=d.status?d.status:null)}K.inherits(Dt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:K.toJSONObject(this.config),code:this.code,status:this.status}}});const wy=Dt.prototype,Ny={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(u=>{Ny[u]={value:u}});Object.defineProperties(Dt,Ny);Object.defineProperty(wy,"isAxiosError",{value:!0});Dt.from=(u,l,h,o,d,c)=>{const s=Object.create(wy);return K.toFlatObject(u,s,function(g){return g!==Error.prototype},f=>f!=="isAxiosError"),Dt.call(s,u.message,l,h,o,d),s.cause=u,s.name=u.name,c&&Object.assign(s,c),s};const s1=null;function Vs(u){return K.isPlainObject(u)||K.isArray(u)}function xy(u){return K.endsWith(u,"[]")?u.slice(0,-2):u}function yp(u,l,h){return u?u.concat(l).map(function(d,c){return d=xy(d),!h&&c?"["+d+"]":d}).join(h?".":""):l}function c1(u){return K.isArray(u)&&!u.some(Vs)}const f1=K.toFlatObject(K,{},null,function(l){return/^is[A-Z]/.test(l)});function hu(u,l,h){if(!K.isObject(u))throw new TypeError("target must be an object");l=l||new FormData,h=K.toFlatObject(h,{metaTokens:!0,dots:!1,indexes:!1},!1,function(X,B){return!K.isUndefined(B[X])});const o=h.metaTokens,d=h.visitor||S,c=h.dots,s=h.indexes,g=(h.Blob||typeof Blob<"u"&&Blob)&&K.isSpecCompliantForm(l);if(!K.isFunction(d))throw new TypeError("visitor must be a function");function b(N){if(N===null)return"";if(K.isDate(N))return N.toISOString();if(K.isBoolean(N))return N.toString();if(!g&&K.isBlob(N))throw new Dt("Blob is not supported. Use a Buffer instead.");return K.isArrayBuffer(N)||K.isTypedArray(N)?g&&typeof Blob=="function"?new Blob([N]):Buffer.from(N):N}function S(N,X,B){let C=N;if(N&&!B&&typeof N=="object"){if(K.endsWith(X,"{}"))X=o?X:X.slice(0,-2),N=JSON.stringify(N);else if(K.isArray(N)&&c1(N)||(K.isFileList(N)||K.endsWith(X,"[]"))&&(C=K.toArray(N)))return X=xy(X),C.forEach(function(E,U){!(K.isUndefined(E)||E===null)&&l.append(s===!0?yp([X],U,c):s===null?X:X+"[]",b(E))}),!1}return Vs(N)?!0:(l.append(yp(B,X,c),b(N)),!1)}const A=[],x=Object.assign(f1,{defaultVisitor:S,convertValue:b,isVisitable:Vs});function M(N,X){if(!K.isUndefined(N)){if(A.indexOf(N)!==-1)throw Error("Circular reference detected in "+X.join("."));A.push(N),K.forEach(N,function(C,_){(!(K.isUndefined(C)||C===null)&&d.call(l,C,K.isString(_)?_.trim():_,X,x))===!0&&M(C,X?X.concat(_):[_])}),A.pop()}}if(!K.isObject(u))throw new TypeError("data must be an object");return M(u),l}function mp(u){const l={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(u).replace(/[!'()~]|%20|%00/g,function(o){return l[o]})}function Js(u,l){this._pairs=[],u&&hu(u,this,l)}const My=Js.prototype;My.append=function(l,h){this._pairs.push([l,h])};My.toString=function(l){const h=l?function(o){return l.call(this,o,mp)}:mp;return this._pairs.map(function(d){return h(d[0])+"="+h(d[1])},"").join("&")};function h1(u){return encodeURIComponent(u).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function _y(u,l,h){if(!l)return u;const o=h&&h.encode||h1;K.isFunction(h)&&(h={serialize:h});const d=h&&h.serialize;let c;if(d?c=d(l,h):c=K.isURLSearchParams(l)?l.toString():new Js(l,h).toString(o),c){const s=u.indexOf("#");s!==-1&&(u=u.slice(0,s)),u+=(u.indexOf("?")===-1?"?":"&")+c}return u}class gp{constructor(){this.handlers=[]}use(l,h,o){return this.handlers.push({fulfilled:l,rejected:h,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(l){this.handlers[l]&&(this.handlers[l]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(l){K.forEach(this.handlers,function(o){o!==null&&l(o)})}}const Ry={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},d1=typeof URLSearchParams<"u"?URLSearchParams:Js,p1=typeof FormData<"u"?FormData:null,y1=typeof Blob<"u"?Blob:null,m1={isBrowser:!0,classes:{URLSearchParams:d1,FormData:p1,Blob:y1},protocols:["http","https","file","blob","url","data"]},ks=typeof window<"u"&&typeof document<"u",Fs=typeof navigator=="object"&&navigator||void 0,g1=ks&&(!Fs||["ReactNative","NativeScript","NS"].indexOf(Fs.product)<0),v1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",E1=ks&&window.location.href||"http://localhost",T1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ks,hasStandardBrowserEnv:g1,hasStandardBrowserWebWorkerEnv:v1,navigator:Fs,origin:E1},Symbol.toStringTag,{value:"Module"})),be={...T1,...m1};function b1(u,l){return hu(u,new be.classes.URLSearchParams,{visitor:function(h,o,d,c){return be.isNode&&K.isBuffer(h)?(this.append(o,h.toString("base64")),!1):c.defaultVisitor.apply(this,arguments)},...l})}function S1(u){return K.matchAll(/\w+|\[(\w*)]/g,u).map(l=>l[0]==="[]"?"":l[1]||l[0])}function D1(u){const l={},h=Object.keys(u);let o;const d=h.length;let c;for(o=0;o<d;o++)c=h[o],l[c]=u[c];return l}function Cy(u){function l(h,o,d,c){let s=h[c++];if(s==="__proto__")return!0;const f=Number.isFinite(+s),g=c>=h.length;return s=!s&&K.isArray(d)?d.length:s,g?(K.hasOwnProp(d,s)?d[s]=[d[s],o]:d[s]=o,!f):((!d[s]||!K.isObject(d[s]))&&(d[s]=[]),l(h,o,d[s],c)&&K.isArray(d[s])&&(d[s]=D1(d[s])),!f)}if(K.isFormData(u)&&K.isFunction(u.entries)){const h={};return K.forEachEntry(u,(o,d)=>{l(S1(o),d,h,0)}),h}return null}function A1(u,l,h){if(K.isString(u))try{return(l||JSON.parse)(u),K.trim(u)}catch(o){if(o.name!=="SyntaxError")throw o}return(h||JSON.stringify)(u)}const la={transitional:Ry,adapter:["xhr","http","fetch"],transformRequest:[function(l,h){const o=h.getContentType()||"",d=o.indexOf("application/json")>-1,c=K.isObject(l);if(c&&K.isHTMLForm(l)&&(l=new FormData(l)),K.isFormData(l))return d?JSON.stringify(Cy(l)):l;if(K.isArrayBuffer(l)||K.isBuffer(l)||K.isStream(l)||K.isFile(l)||K.isBlob(l)||K.isReadableStream(l))return l;if(K.isArrayBufferView(l))return l.buffer;if(K.isURLSearchParams(l))return h.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),l.toString();let f;if(c){if(o.indexOf("application/x-www-form-urlencoded")>-1)return b1(l,this.formSerializer).toString();if((f=K.isFileList(l))||o.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return hu(f?{"files[]":l}:l,g&&new g,this.formSerializer)}}return c||d?(h.setContentType("application/json",!1),A1(l)):l}],transformResponse:[function(l){const h=this.transitional||la.transitional,o=h&&h.forcedJSONParsing,d=this.responseType==="json";if(K.isResponse(l)||K.isReadableStream(l))return l;if(l&&K.isString(l)&&(o&&!this.responseType||d)){const s=!(h&&h.silentJSONParsing)&&d;try{return JSON.parse(l)}catch(f){if(s)throw f.name==="SyntaxError"?Dt.from(f,Dt.ERR_BAD_RESPONSE,this,null,this.response):f}}return l}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:be.classes.FormData,Blob:be.classes.Blob},validateStatus:function(l){return l>=200&&l<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};K.forEach(["delete","get","head","post","put","patch"],u=>{la.headers[u]={}});const O1=K.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),w1=u=>{const l={};let h,o,d;return u&&u.split(`
`).forEach(function(s){d=s.indexOf(":"),h=s.substring(0,d).trim().toLowerCase(),o=s.substring(d+1).trim(),!(!h||l[h]&&O1[h])&&(h==="set-cookie"?l[h]?l[h].push(o):l[h]=[o]:l[h]=l[h]?l[h]+", "+o:o)}),l},vp=Symbol("internals");function ta(u){return u&&String(u).trim().toLowerCase()}function xl(u){return u===!1||u==null?u:K.isArray(u)?u.map(xl):String(u)}function N1(u){const l=Object.create(null),h=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=h.exec(u);)l[o[1]]=o[2];return l}const x1=u=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(u.trim());function Bs(u,l,h,o,d){if(K.isFunction(o))return o.call(this,l,h);if(d&&(l=h),!!K.isString(l)){if(K.isString(o))return l.indexOf(o)!==-1;if(K.isRegExp(o))return o.test(l)}}function M1(u){return u.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(l,h,o)=>h.toUpperCase()+o)}function _1(u,l){const h=K.toCamelCase(" "+l);["get","set","has"].forEach(o=>{Object.defineProperty(u,o+h,{value:function(d,c,s){return this[o].call(this,l,d,c,s)},configurable:!0})})}let Me=class{constructor(l){l&&this.set(l)}set(l,h,o){const d=this;function c(f,g,b){const S=ta(g);if(!S)throw new Error("header name must be a non-empty string");const A=K.findKey(d,S);(!A||d[A]===void 0||b===!0||b===void 0&&d[A]!==!1)&&(d[A||g]=xl(f))}const s=(f,g)=>K.forEach(f,(b,S)=>c(b,S,g));if(K.isPlainObject(l)||l instanceof this.constructor)s(l,h);else if(K.isString(l)&&(l=l.trim())&&!x1(l))s(w1(l),h);else if(K.isObject(l)&&K.isIterable(l)){let f={},g,b;for(const S of l){if(!K.isArray(S))throw TypeError("Object iterator must return a key-value pair");f[b=S[0]]=(g=f[b])?K.isArray(g)?[...g,S[1]]:[g,S[1]]:S[1]}s(f,h)}else l!=null&&c(h,l,o);return this}get(l,h){if(l=ta(l),l){const o=K.findKey(this,l);if(o){const d=this[o];if(!h)return d;if(h===!0)return N1(d);if(K.isFunction(h))return h.call(this,d,o);if(K.isRegExp(h))return h.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(l,h){if(l=ta(l),l){const o=K.findKey(this,l);return!!(o&&this[o]!==void 0&&(!h||Bs(this,this[o],o,h)))}return!1}delete(l,h){const o=this;let d=!1;function c(s){if(s=ta(s),s){const f=K.findKey(o,s);f&&(!h||Bs(o,o[f],f,h))&&(delete o[f],d=!0)}}return K.isArray(l)?l.forEach(c):c(l),d}clear(l){const h=Object.keys(this);let o=h.length,d=!1;for(;o--;){const c=h[o];(!l||Bs(this,this[c],c,l,!0))&&(delete this[c],d=!0)}return d}normalize(l){const h=this,o={};return K.forEach(this,(d,c)=>{const s=K.findKey(o,c);if(s){h[s]=xl(d),delete h[c];return}const f=l?M1(c):String(c).trim();f!==c&&delete h[c],h[f]=xl(d),o[f]=!0}),this}concat(...l){return this.constructor.concat(this,...l)}toJSON(l){const h=Object.create(null);return K.forEach(this,(o,d)=>{o!=null&&o!==!1&&(h[d]=l&&K.isArray(o)?o.join(", "):o)}),h}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([l,h])=>l+": "+h).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(l){return l instanceof this?l:new this(l)}static concat(l,...h){const o=new this(l);return h.forEach(d=>o.set(d)),o}static accessor(l){const o=(this[vp]=this[vp]={accessors:{}}).accessors,d=this.prototype;function c(s){const f=ta(s);o[f]||(_1(d,s),o[f]=!0)}return K.isArray(l)?l.forEach(c):c(l),this}};Me.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);K.reduceDescriptors(Me.prototype,({value:u},l)=>{let h=l[0].toUpperCase()+l.slice(1);return{get:()=>u,set(o){this[h]=o}}});K.freezeMethods(Me);function js(u,l){const h=this||la,o=l||h,d=Me.from(o.headers);let c=o.data;return K.forEach(u,function(f){c=f.call(h,c,d.normalize(),l?l.status:void 0)}),d.normalize(),c}function Ly(u){return!!(u&&u.__CANCEL__)}function er(u,l,h){Dt.call(this,u??"canceled",Dt.ERR_CANCELED,l,h),this.name="CanceledError"}K.inherits(er,Dt,{__CANCEL__:!0});function Uy(u,l,h){const o=h.config.validateStatus;!h.status||!o||o(h.status)?u(h):l(new Dt("Request failed with status code "+h.status,[Dt.ERR_BAD_REQUEST,Dt.ERR_BAD_RESPONSE][Math.floor(h.status/100)-4],h.config,h.request,h))}function R1(u){const l=/^([-+\w]{1,25})(:?\/\/|:)/.exec(u);return l&&l[1]||""}function C1(u,l){u=u||10;const h=new Array(u),o=new Array(u);let d=0,c=0,s;return l=l!==void 0?l:1e3,function(g){const b=Date.now(),S=o[c];s||(s=b),h[d]=g,o[d]=b;let A=c,x=0;for(;A!==d;)x+=h[A++],A=A%u;if(d=(d+1)%u,d===c&&(c=(c+1)%u),b-s<l)return;const M=S&&b-S;return M?Math.round(x*1e3/M):void 0}}function L1(u,l){let h=0,o=1e3/l,d,c;const s=(b,S=Date.now())=>{h=S,d=null,c&&(clearTimeout(c),c=null),u(...b)};return[(...b)=>{const S=Date.now(),A=S-h;A>=o?s(b,S):(d=b,c||(c=setTimeout(()=>{c=null,s(d)},o-A)))},()=>d&&s(d)]}const uu=(u,l,h=3)=>{let o=0;const d=C1(50,250);return L1(c=>{const s=c.loaded,f=c.lengthComputable?c.total:void 0,g=s-o,b=d(g),S=s<=f;o=s;const A={loaded:s,total:f,progress:f?s/f:void 0,bytes:g,rate:b||void 0,estimated:b&&f&&S?(f-s)/b:void 0,event:c,lengthComputable:f!=null,[l?"download":"upload"]:!0};u(A)},h)},Ep=(u,l)=>{const h=u!=null;return[o=>l[0]({lengthComputable:h,total:u,loaded:o}),l[1]]},Tp=u=>(...l)=>K.asap(()=>u(...l)),U1=be.hasStandardBrowserEnv?((u,l)=>h=>(h=new URL(h,be.origin),u.protocol===h.protocol&&u.host===h.host&&(l||u.port===h.port)))(new URL(be.origin),be.navigator&&/(msie|trident)/i.test(be.navigator.userAgent)):()=>!0,B1=be.hasStandardBrowserEnv?{write(u,l,h,o,d,c){const s=[u+"="+encodeURIComponent(l)];K.isNumber(h)&&s.push("expires="+new Date(h).toGMTString()),K.isString(o)&&s.push("path="+o),K.isString(d)&&s.push("domain="+d),c===!0&&s.push("secure"),document.cookie=s.join("; ")},read(u){const l=document.cookie.match(new RegExp("(^|;\\s*)("+u+")=([^;]*)"));return l?decodeURIComponent(l[3]):null},remove(u){this.write(u,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function j1(u){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(u)}function X1(u,l){return l?u.replace(/\/?\/$/,"")+"/"+l.replace(/^\/+/,""):u}function By(u,l,h){let o=!j1(l);return u&&(o||h==!1)?X1(u,l):l}const bp=u=>u instanceof Me?{...u}:u;function pi(u,l){l=l||{};const h={};function o(b,S,A,x){return K.isPlainObject(b)&&K.isPlainObject(S)?K.merge.call({caseless:x},b,S):K.isPlainObject(S)?K.merge({},S):K.isArray(S)?S.slice():S}function d(b,S,A,x){if(K.isUndefined(S)){if(!K.isUndefined(b))return o(void 0,b,A,x)}else return o(b,S,A,x)}function c(b,S){if(!K.isUndefined(S))return o(void 0,S)}function s(b,S){if(K.isUndefined(S)){if(!K.isUndefined(b))return o(void 0,b)}else return o(void 0,S)}function f(b,S,A){if(A in l)return o(b,S);if(A in u)return o(void 0,b)}const g={url:c,method:c,data:c,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:f,headers:(b,S,A)=>d(bp(b),bp(S),A,!0)};return K.forEach(Object.keys({...u,...l}),function(S){const A=g[S]||d,x=A(u[S],l[S],S);K.isUndefined(x)&&A!==f||(h[S]=x)}),h}const jy=u=>{const l=pi({},u);let{data:h,withXSRFToken:o,xsrfHeaderName:d,xsrfCookieName:c,headers:s,auth:f}=l;l.headers=s=Me.from(s),l.url=_y(By(l.baseURL,l.url,l.allowAbsoluteUrls),u.params,u.paramsSerializer),f&&s.set("Authorization","Basic "+btoa((f.username||"")+":"+(f.password?unescape(encodeURIComponent(f.password)):"")));let g;if(K.isFormData(h)){if(be.hasStandardBrowserEnv||be.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((g=s.getContentType())!==!1){const[b,...S]=g?g.split(";").map(A=>A.trim()).filter(Boolean):[];s.setContentType([b||"multipart/form-data",...S].join("; "))}}if(be.hasStandardBrowserEnv&&(o&&K.isFunction(o)&&(o=o(l)),o||o!==!1&&U1(l.url))){const b=d&&c&&B1.read(c);b&&s.set(d,b)}return l},z1=typeof XMLHttpRequest<"u",q1=z1&&function(u){return new Promise(function(h,o){const d=jy(u);let c=d.data;const s=Me.from(d.headers).normalize();let{responseType:f,onUploadProgress:g,onDownloadProgress:b}=d,S,A,x,M,N;function X(){M&&M(),N&&N(),d.cancelToken&&d.cancelToken.unsubscribe(S),d.signal&&d.signal.removeEventListener("abort",S)}let B=new XMLHttpRequest;B.open(d.method.toUpperCase(),d.url,!0),B.timeout=d.timeout;function C(){if(!B)return;const E=Me.from("getAllResponseHeaders"in B&&B.getAllResponseHeaders()),j={data:!f||f==="text"||f==="json"?B.responseText:B.response,status:B.status,statusText:B.statusText,headers:E,config:u,request:B};Uy(function(ut){h(ut),X()},function(ut){o(ut),X()},j),B=null}"onloadend"in B?B.onloadend=C:B.onreadystatechange=function(){!B||B.readyState!==4||B.status===0&&!(B.responseURL&&B.responseURL.indexOf("file:")===0)||setTimeout(C)},B.onabort=function(){B&&(o(new Dt("Request aborted",Dt.ECONNABORTED,u,B)),B=null)},B.onerror=function(){o(new Dt("Network Error",Dt.ERR_NETWORK,u,B)),B=null},B.ontimeout=function(){let U=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const j=d.transitional||Ry;d.timeoutErrorMessage&&(U=d.timeoutErrorMessage),o(new Dt(U,j.clarifyTimeoutError?Dt.ETIMEDOUT:Dt.ECONNABORTED,u,B)),B=null},c===void 0&&s.setContentType(null),"setRequestHeader"in B&&K.forEach(s.toJSON(),function(U,j){B.setRequestHeader(j,U)}),K.isUndefined(d.withCredentials)||(B.withCredentials=!!d.withCredentials),f&&f!=="json"&&(B.responseType=d.responseType),b&&([x,N]=uu(b,!0),B.addEventListener("progress",x)),g&&B.upload&&([A,M]=uu(g),B.upload.addEventListener("progress",A),B.upload.addEventListener("loadend",M)),(d.cancelToken||d.signal)&&(S=E=>{B&&(o(!E||E.type?new er(null,u,B):E),B.abort(),B=null)},d.cancelToken&&d.cancelToken.subscribe(S),d.signal&&(d.signal.aborted?S():d.signal.addEventListener("abort",S)));const _=R1(d.url);if(_&&be.protocols.indexOf(_)===-1){o(new Dt("Unsupported protocol "+_+":",Dt.ERR_BAD_REQUEST,u));return}B.send(c||null)})},H1=(u,l)=>{const{length:h}=u=u?u.filter(Boolean):[];if(l||h){let o=new AbortController,d;const c=function(b){if(!d){d=!0,f();const S=b instanceof Error?b:this.reason;o.abort(S instanceof Dt?S:new er(S instanceof Error?S.message:S))}};let s=l&&setTimeout(()=>{s=null,c(new Dt(`timeout ${l} of ms exceeded`,Dt.ETIMEDOUT))},l);const f=()=>{u&&(s&&clearTimeout(s),s=null,u.forEach(b=>{b.unsubscribe?b.unsubscribe(c):b.removeEventListener("abort",c)}),u=null)};u.forEach(b=>b.addEventListener("abort",c));const{signal:g}=o;return g.unsubscribe=()=>K.asap(f),g}},Y1=function*(u,l){let h=u.byteLength;if(h<l){yield u;return}let o=0,d;for(;o<h;)d=o+l,yield u.slice(o,d),o=d},V1=async function*(u,l){for await(const h of F1(u))yield*Y1(h,l)},F1=async function*(u){if(u[Symbol.asyncIterator]){yield*u;return}const l=u.getReader();try{for(;;){const{done:h,value:o}=await l.read();if(h)break;yield o}}finally{await l.cancel()}},Sp=(u,l,h,o)=>{const d=V1(u,l);let c=0,s,f=g=>{s||(s=!0,o&&o(g))};return new ReadableStream({async pull(g){try{const{done:b,value:S}=await d.next();if(b){f(),g.close();return}let A=S.byteLength;if(h){let x=c+=A;h(x)}g.enqueue(new Uint8Array(S))}catch(b){throw f(b),b}},cancel(g){return f(g),d.return()}},{highWaterMark:2})},du=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Xy=du&&typeof ReadableStream=="function",G1=du&&(typeof TextEncoder=="function"?(u=>l=>u.encode(l))(new TextEncoder):async u=>new Uint8Array(await new Response(u).arrayBuffer())),zy=(u,...l)=>{try{return!!u(...l)}catch{return!1}},I1=Xy&&zy(()=>{let u=!1;const l=new Request(be.origin,{body:new ReadableStream,method:"POST",get duplex(){return u=!0,"half"}}).headers.has("Content-Type");return u&&!l}),Dp=64*1024,Gs=Xy&&zy(()=>K.isReadableStream(new Response("").body)),ou={stream:Gs&&(u=>u.body)};du&&(u=>{["text","arrayBuffer","blob","formData","stream"].forEach(l=>{!ou[l]&&(ou[l]=K.isFunction(u[l])?h=>h[l]():(h,o)=>{throw new Dt(`Response type '${l}' is not supported`,Dt.ERR_NOT_SUPPORT,o)})})})(new Response);const Q1=async u=>{if(u==null)return 0;if(K.isBlob(u))return u.size;if(K.isSpecCompliantForm(u))return(await new Request(be.origin,{method:"POST",body:u}).arrayBuffer()).byteLength;if(K.isArrayBufferView(u)||K.isArrayBuffer(u))return u.byteLength;if(K.isURLSearchParams(u)&&(u=u+""),K.isString(u))return(await G1(u)).byteLength},Z1=async(u,l)=>{const h=K.toFiniteNumber(u.getContentLength());return h??Q1(l)},K1=du&&(async u=>{let{url:l,method:h,data:o,signal:d,cancelToken:c,timeout:s,onDownloadProgress:f,onUploadProgress:g,responseType:b,headers:S,withCredentials:A="same-origin",fetchOptions:x}=jy(u);b=b?(b+"").toLowerCase():"text";let M=H1([d,c&&c.toAbortSignal()],s),N;const X=M&&M.unsubscribe&&(()=>{M.unsubscribe()});let B;try{if(g&&I1&&h!=="get"&&h!=="head"&&(B=await Z1(S,o))!==0){let j=new Request(l,{method:"POST",body:o,duplex:"half"}),W;if(K.isFormData(o)&&(W=j.headers.get("content-type"))&&S.setContentType(W),j.body){const[ut,$]=Ep(B,uu(Tp(g)));o=Sp(j.body,Dp,ut,$)}}K.isString(A)||(A=A?"include":"omit");const C="credentials"in Request.prototype;N=new Request(l,{...x,signal:M,method:h.toUpperCase(),headers:S.normalize().toJSON(),body:o,duplex:"half",credentials:C?A:void 0});let _=await fetch(N,x);const E=Gs&&(b==="stream"||b==="response");if(Gs&&(f||E&&X)){const j={};["status","statusText","headers"].forEach(v=>{j[v]=_[v]});const W=K.toFiniteNumber(_.headers.get("content-length")),[ut,$]=f&&Ep(W,uu(Tp(f),!0))||[];_=new Response(Sp(_.body,Dp,ut,()=>{$&&$(),X&&X()}),j)}b=b||"text";let U=await ou[K.findKey(ou,b)||"text"](_,u);return!E&&X&&X(),await new Promise((j,W)=>{Uy(j,W,{data:U,headers:Me.from(_.headers),status:_.status,statusText:_.statusText,config:u,request:N})})}catch(C){throw X&&X(),C&&C.name==="TypeError"&&/Load failed|fetch/i.test(C.message)?Object.assign(new Dt("Network Error",Dt.ERR_NETWORK,u,N),{cause:C.cause||C}):Dt.from(C,C&&C.code,u,N)}}),Is={http:s1,xhr:q1,fetch:K1};K.forEach(Is,(u,l)=>{if(u){try{Object.defineProperty(u,"name",{value:l})}catch{}Object.defineProperty(u,"adapterName",{value:l})}});const Ap=u=>`- ${u}`,P1=u=>K.isFunction(u)||u===null||u===!1,qy={getAdapter:u=>{u=K.isArray(u)?u:[u];const{length:l}=u;let h,o;const d={};for(let c=0;c<l;c++){h=u[c];let s;if(o=h,!P1(h)&&(o=Is[(s=String(h)).toLowerCase()],o===void 0))throw new Dt(`Unknown adapter '${s}'`);if(o)break;d[s||"#"+c]=o}if(!o){const c=Object.entries(d).map(([f,g])=>`adapter ${f} `+(g===!1?"is not supported by the environment":"is not available in the build"));let s=l?c.length>1?`since :
`+c.map(Ap).join(`
`):" "+Ap(c[0]):"as no adapter specified";throw new Dt("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return o},adapters:Is};function Xs(u){if(u.cancelToken&&u.cancelToken.throwIfRequested(),u.signal&&u.signal.aborted)throw new er(null,u)}function Op(u){return Xs(u),u.headers=Me.from(u.headers),u.data=js.call(u,u.transformRequest),["post","put","patch"].indexOf(u.method)!==-1&&u.headers.setContentType("application/x-www-form-urlencoded",!1),qy.getAdapter(u.adapter||la.adapter)(u).then(function(o){return Xs(u),o.data=js.call(u,u.transformResponse,o),o.headers=Me.from(o.headers),o},function(o){return Ly(o)||(Xs(u),o&&o.response&&(o.response.data=js.call(u,u.transformResponse,o.response),o.response.headers=Me.from(o.response.headers))),Promise.reject(o)})}const Hy="1.11.0",pu={};["object","boolean","number","function","string","symbol"].forEach((u,l)=>{pu[u]=function(o){return typeof o===u||"a"+(l<1?"n ":" ")+u}});const wp={};pu.transitional=function(l,h,o){function d(c,s){return"[Axios v"+Hy+"] Transitional option '"+c+"'"+s+(o?". "+o:"")}return(c,s,f)=>{if(l===!1)throw new Dt(d(s," has been removed"+(h?" in "+h:"")),Dt.ERR_DEPRECATED);return h&&!wp[s]&&(wp[s]=!0,console.warn(d(s," has been deprecated since v"+h+" and will be removed in the near future"))),l?l(c,s,f):!0}};pu.spelling=function(l){return(h,o)=>(console.warn(`${o} is likely a misspelling of ${l}`),!0)};function J1(u,l,h){if(typeof u!="object")throw new Dt("options must be an object",Dt.ERR_BAD_OPTION_VALUE);const o=Object.keys(u);let d=o.length;for(;d-- >0;){const c=o[d],s=l[c];if(s){const f=u[c],g=f===void 0||s(f,c,u);if(g!==!0)throw new Dt("option "+c+" must be "+g,Dt.ERR_BAD_OPTION_VALUE);continue}if(h!==!0)throw new Dt("Unknown option "+c,Dt.ERR_BAD_OPTION)}}const Ml={assertOptions:J1,validators:pu},rn=Ml.validators;let di=class{constructor(l){this.defaults=l||{},this.interceptors={request:new gp,response:new gp}}async request(l,h){try{return await this._request(l,h)}catch(o){if(o instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const c=d.stack?d.stack.replace(/^.+\n/,""):"";try{o.stack?c&&!String(o.stack).endsWith(c.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+c):o.stack=c}catch{}}throw o}}_request(l,h){typeof l=="string"?(h=h||{},h.url=l):h=l||{},h=pi(this.defaults,h);const{transitional:o,paramsSerializer:d,headers:c}=h;o!==void 0&&Ml.assertOptions(o,{silentJSONParsing:rn.transitional(rn.boolean),forcedJSONParsing:rn.transitional(rn.boolean),clarifyTimeoutError:rn.transitional(rn.boolean)},!1),d!=null&&(K.isFunction(d)?h.paramsSerializer={serialize:d}:Ml.assertOptions(d,{encode:rn.function,serialize:rn.function},!0)),h.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?h.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:h.allowAbsoluteUrls=!0),Ml.assertOptions(h,{baseUrl:rn.spelling("baseURL"),withXsrfToken:rn.spelling("withXSRFToken")},!0),h.method=(h.method||this.defaults.method||"get").toLowerCase();let s=c&&K.merge(c.common,c[h.method]);c&&K.forEach(["delete","get","head","post","put","patch","common"],N=>{delete c[N]}),h.headers=Me.concat(s,c);const f=[];let g=!0;this.interceptors.request.forEach(function(X){typeof X.runWhen=="function"&&X.runWhen(h)===!1||(g=g&&X.synchronous,f.unshift(X.fulfilled,X.rejected))});const b=[];this.interceptors.response.forEach(function(X){b.push(X.fulfilled,X.rejected)});let S,A=0,x;if(!g){const N=[Op.bind(this),void 0];for(N.unshift(...f),N.push(...b),x=N.length,S=Promise.resolve(h);A<x;)S=S.then(N[A++],N[A++]);return S}x=f.length;let M=h;for(A=0;A<x;){const N=f[A++],X=f[A++];try{M=N(M)}catch(B){X.call(this,B);break}}try{S=Op.call(this,M)}catch(N){return Promise.reject(N)}for(A=0,x=b.length;A<x;)S=S.then(b[A++],b[A++]);return S}getUri(l){l=pi(this.defaults,l);const h=By(l.baseURL,l.url,l.allowAbsoluteUrls);return _y(h,l.params,l.paramsSerializer)}};K.forEach(["delete","get","head","options"],function(l){di.prototype[l]=function(h,o){return this.request(pi(o||{},{method:l,url:h,data:(o||{}).data}))}});K.forEach(["post","put","patch"],function(l){function h(o){return function(c,s,f){return this.request(pi(f||{},{method:l,headers:o?{"Content-Type":"multipart/form-data"}:{},url:c,data:s}))}}di.prototype[l]=h(),di.prototype[l+"Form"]=h(!0)});let k1=class Yy{constructor(l){if(typeof l!="function")throw new TypeError("executor must be a function.");let h;this.promise=new Promise(function(c){h=c});const o=this;this.promise.then(d=>{if(!o._listeners)return;let c=o._listeners.length;for(;c-- >0;)o._listeners[c](d);o._listeners=null}),this.promise.then=d=>{let c;const s=new Promise(f=>{o.subscribe(f),c=f}).then(d);return s.cancel=function(){o.unsubscribe(c)},s},l(function(c,s,f){o.reason||(o.reason=new er(c,s,f),h(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(l){if(this.reason){l(this.reason);return}this._listeners?this._listeners.push(l):this._listeners=[l]}unsubscribe(l){if(!this._listeners)return;const h=this._listeners.indexOf(l);h!==-1&&this._listeners.splice(h,1)}toAbortSignal(){const l=new AbortController,h=o=>{l.abort(o)};return this.subscribe(h),l.signal.unsubscribe=()=>this.unsubscribe(h),l.signal}static source(){let l;return{token:new Yy(function(d){l=d}),cancel:l}}};function W1(u){return function(h){return u.apply(null,h)}}function $1(u){return K.isObject(u)&&u.isAxiosError===!0}const Qs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qs).forEach(([u,l])=>{Qs[l]=u});function Vy(u){const l=new di(u),h=vy(di.prototype.request,l);return K.extend(h,di.prototype,l,{allOwnKeys:!0}),K.extend(h,l,null,{allOwnKeys:!0}),h.create=function(d){return Vy(pi(u,d))},h}const ee=Vy(la);ee.Axios=di;ee.CanceledError=er;ee.CancelToken=k1;ee.isCancel=Ly;ee.VERSION=Hy;ee.toFormData=hu;ee.AxiosError=Dt;ee.Cancel=ee.CanceledError;ee.all=function(l){return Promise.all(l)};ee.spread=W1;ee.isAxiosError=$1;ee.mergeConfig=pi;ee.AxiosHeaders=Me;ee.formToJSON=u=>Cy(K.isHTMLForm(u)?new FormData(u):u);ee.getAdapter=qy.getAdapter;ee.HttpStatusCode=Qs;ee.default=ee;const{Axios:uE,AxiosError:oE,CanceledError:sE,isCancel:cE,CancelToken:fE,VERSION:hE,all:dE,Cancel:pE,isAxiosError:yE,spread:mE,toFormData:gE,AxiosHeaders:vE,HttpStatusCode:EE,formToJSON:TE,getAdapter:bE,mergeConfig:SE}=ee,Fy=Kt.createContext(),Ws=()=>{const u=Kt.useContext(Fy);if(!u)throw new Error("useSettings must be used within a SettingsProvider");return u},tv=({children:u})=>{const[l,h]=Kt.useState({host:"localhost",port:9e3});Kt.useEffect(()=>{const f=localStorage.getItem("tallySettings");if(f)try{const g=JSON.parse(f);h(g)}catch(g){console.error("Failed to parse saved settings:",g)}},[]);const s={tallySettings:l,updateTallySettings:f=>{h(f),localStorage.setItem("tallySettings",JSON.stringify(f))},resetTallySettings:()=>{const f={host:"localhost",port:9e3};h(f),localStorage.setItem("tallySettings",JSON.stringify(f))},getTallyUrl:()=>`http://${l.host}:${l.port}`};return lt.jsx(Fy.Provider,{value:s,children:u})},ev=({onConnectionChange:u})=>{const{tallySettings:l}=Ws(),[h,o]=Kt.useState("connecting"),[d,c]=Kt.useState(new Date),[s,f]=Kt.useState(null),[g,b]=Kt.useState(null),S=`http://${l.host}:${l.port}`,A=async()=>{try{o("connecting"),f(null);const X=await ee.post(S,`
        <ENVELOPE>
          <HEADER>
            <TALLYREQUEST>Export Data</TALLYREQUEST>
          </HEADER>
          <BODY>
            <EXPORTDATA>
              <REQUESTDESC>
                <REPORTNAME>List of Companies</REPORTNAME>
              </REQUESTDESC>
            </EXPORTDATA>
          </BODY>
        </ENVELOPE>
      `,{headers:{"Content-Type":"application/xml"},timeout:5e3});if(X.status===200)o("online"),b({host:l.host,port:l.port,responseTime:Date.now()-startTime}),u&&u(!0);else throw new Error(`HTTP ${X.status}`)}catch(N){o("offline"),f(N.message),b(null),u&&u(!1)}finally{c(new Date)}};Kt.useEffect(()=>{const N=()=>{A()};N();const X=setInterval(N,1e4);return()=>{clearInterval(X)}},[l.host,l.port]);const x=()=>{switch(h){case"online":return"✅";case"offline":return"❌";case"connecting":return"🔄";default:return"❓"}},M=()=>{switch(h){case"online":return"Connected to Tally Prime";case"offline":return"Tally Prime not accessible";case"connecting":return"Checking connection...";default:return"Unknown status"}};return lt.jsxs("div",{className:"status-container",children:[lt.jsx("div",{className:"status-header",children:lt.jsx("h3",{children:"Tally Prime Connection"})}),lt.jsxs("div",{className:`status-indicator ${h}`,children:[lt.jsx("div",{className:"status-icon",children:x()}),lt.jsxs("div",{className:"status-details",children:[lt.jsx("div",{className:"status-text",children:M()}),lt.jsxs("div",{className:"status-timestamp",children:["Last checked: ",d.toLocaleTimeString()]}),g&&lt.jsxs("div",{className:"status-timestamp",children:[l.host,":",l.port," (",g.responseTime,"ms)"]}),s&&lt.jsxs("div",{className:"status-error",children:["Error: ",s]})]})]}),lt.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"#666"},children:["Make sure Tally Prime is running with Gateway enabled on port"," ",l.port]})]})};var an={},bl={},Np;function $s(){return Np||(Np=1,(function(){bl.defaults={"0.1":{explicitCharkey:!1,trim:!0,normalize:!0,normalizeTags:!1,attrkey:"@",charkey:"#",explicitArray:!1,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!1,validator:null,xmlns:!1,explicitChildren:!1,childkey:"@@",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,emptyTag:""},"0.2":{explicitCharkey:!1,trim:!1,normalize:!1,normalizeTags:!1,attrkey:"$",charkey:"_",explicitArray:!0,ignoreAttrs:!1,mergeAttrs:!1,explicitRoot:!0,validator:null,xmlns:!1,explicitChildren:!1,preserveChildrenOrder:!1,childkey:"$$",charsAsChildren:!1,includeWhiteChars:!1,async:!1,strict:!0,attrNameProcessors:null,attrValueProcessors:null,tagNameProcessors:null,valueProcessors:null,rootName:"root",xmldec:{version:"1.0",encoding:"UTF-8",standalone:!0},doctype:null,renderOpts:{pretty:!0,indent:"  ",newline:`
`},headless:!1,chunkSize:1e4,emptyTag:"",cdata:!1}}}).call(bl)),bl}var Sl={},ln={},un={},xp;function Dn(){return xp||(xp=1,(function(){var u,l,h,o,d,c,s,f=[].slice,g={}.hasOwnProperty;u=function(){var b,S,A,x,M,N;if(N=arguments[0],M=2<=arguments.length?f.call(arguments,1):[],d(Object.assign))Object.assign.apply(null,arguments);else for(b=0,A=M.length;b<A;b++)if(x=M[b],x!=null)for(S in x)g.call(x,S)&&(N[S]=x[S]);return N},d=function(b){return!!b&&Object.prototype.toString.call(b)==="[object Function]"},c=function(b){var S;return!!b&&((S=typeof b)=="function"||S==="object")},h=function(b){return d(Array.isArray)?Array.isArray(b):Object.prototype.toString.call(b)==="[object Array]"},o=function(b){var S;if(h(b))return!b.length;for(S in b)if(g.call(b,S))return!1;return!0},s=function(b){var S,A;return c(b)&&(A=Object.getPrototypeOf(b))&&(S=A.constructor)&&typeof S=="function"&&S instanceof S&&Function.prototype.toString.call(S)===Function.prototype.toString.call(Object)},l=function(b){return d(b.valueOf)?b.valueOf():b},un.assign=u,un.isFunction=d,un.isObject=c,un.isArray=h,un.isEmpty=o,un.isPlainObject=s,un.getValue=l}).call(un)),un}var _l={exports:{}},nv=_l.exports,Mp;function Gy(){return Mp||(Mp=1,(function(){_l.exports=(function(){function u(){}return u.prototype.hasFeature=function(l,h){return!0},u.prototype.createDocumentType=function(l,h,o){throw new Error("This DOM method is not implemented.")},u.prototype.createDocument=function(l,h,o){throw new Error("This DOM method is not implemented.")},u.prototype.createHTMLDocument=function(l){throw new Error("This DOM method is not implemented.")},u.prototype.getFeature=function(l,h){throw new Error("This DOM method is not implemented.")},u})()}).call(nv)),_l.exports}var Rl={exports:{}},Cl={exports:{}},Ll={exports:{}},iv=Ll.exports,_p;function rv(){return _p||(_p=1,(function(){Ll.exports=(function(){function u(){}return u.prototype.handleError=function(l){throw new Error(l)},u})()}).call(iv)),Ll.exports}var Ul={exports:{}},av=Ul.exports,Rp;function lv(){return Rp||(Rp=1,(function(){Ul.exports=(function(){function u(l){this.arr=l||[]}return Object.defineProperty(u.prototype,"length",{get:function(){return this.arr.length}}),u.prototype.item=function(l){return this.arr[l]||null},u.prototype.contains=function(l){return this.arr.indexOf(l)!==-1},u})()}).call(av)),Ul.exports}var uv=Cl.exports,Cp;function ov(){return Cp||(Cp=1,(function(){var u,l;u=rv(),l=lv(),Cl.exports=(function(){function h(){this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new u,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Object.create(this.defaultParams)}return Object.defineProperty(h.prototype,"parameterNames",{get:function(){return new l(Object.keys(this.defaultParams))}}),h.prototype.getParameter=function(o){return this.params.hasOwnProperty(o)?this.params[o]:null},h.prototype.canSetParameter=function(o,d){return!0},h.prototype.setParameter=function(o,d){return d!=null?this.params[o]=d:delete this.params[o]},h})()}).call(uv)),Cl.exports}var Bl={exports:{}},jl={exports:{}},Xl={exports:{}},sv=Xl.exports,Lp;function le(){return Lp||(Lp=1,(function(){Xl.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(sv)),Xl.exports}var zl={exports:{}},cv=zl.exports,Up;function Iy(){return Up||(Up=1,(function(){var u;u=le(),Ze(),zl.exports=(function(){function l(h,o,d){if(this.parent=h,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),o==null)throw new Error("Missing attribute name. "+this.debugInfo(o));this.name=this.stringify.name(o),this.value=this.stringify.attValue(d),this.type=u.Attribute,this.isId=!1,this.schemaTypeInfo=null}return Object.defineProperty(l.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(l.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(l.prototype,"textContent",{get:function(){return this.value},set:function(h){return this.value=h||""}}),Object.defineProperty(l.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(l.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(l.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(l.prototype,"specified",{get:function(){return!0}}),l.prototype.clone=function(){return Object.create(this)},l.prototype.toString=function(h){return this.options.writer.attribute(this,this.options.writer.filterOptions(h))},l.prototype.debugInfo=function(h){return h=h||this.name,h==null?"parent: <"+this.parent.name+">":"attribute: {"+h+"}, parent: <"+this.parent.name+">"},l.prototype.isEqualNode=function(h){return!(h.namespaceURI!==this.namespaceURI||h.prefix!==this.prefix||h.localName!==this.localName||h.value!==this.value)},l})()}).call(cv)),zl.exports}var ql={exports:{}},fv=ql.exports,Bp;function tc(){return Bp||(Bp=1,(function(){ql.exports=(function(){function u(l){this.nodes=l}return Object.defineProperty(u.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),u.prototype.clone=function(){return this.nodes=null},u.prototype.getNamedItem=function(l){return this.nodes[l]},u.prototype.setNamedItem=function(l){var h;return h=this.nodes[l.nodeName],this.nodes[l.nodeName]=l,h||null},u.prototype.removeNamedItem=function(l){var h;return h=this.nodes[l],delete this.nodes[l],h||null},u.prototype.item=function(l){return this.nodes[Object.keys(this.nodes)[l]]||null},u.prototype.getNamedItemNS=function(l,h){throw new Error("This DOM method is not implemented.")},u.prototype.setNamedItemNS=function(l){throw new Error("This DOM method is not implemented.")},u.prototype.removeNamedItemNS=function(l,h){throw new Error("This DOM method is not implemented.")},u})()}).call(fv)),ql.exports}var hv=jl.exports,jp;function ec(){return jp||(jp=1,(function(){var u,l,h,o,d,c,s,f,g=function(S,A){for(var x in A)b.call(A,x)&&(S[x]=A[x]);function M(){this.constructor=S}return M.prototype=A.prototype,S.prototype=new M,S.__super__=A.prototype,S},b={}.hasOwnProperty;f=Dn(),s=f.isObject,c=f.isFunction,d=f.getValue,o=Ze(),u=le(),l=Iy(),h=tc(),jl.exports=(function(S){g(A,S);function A(x,M,N){var X,B,C,_;if(A.__super__.constructor.call(this,x),M==null)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(M),this.type=u.Element,this.attribs={},this.schemaTypeInfo=null,N!=null&&this.attribute(N),x.type===u.Document&&(this.isRoot=!0,this.documentObject=x,x.rootObject=this,x.children)){for(_=x.children,B=0,C=_.length;B<C;B++)if(X=_[B],X.type===u.DocType){X.name=this.name;break}}}return Object.defineProperty(A.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(A.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(A.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(A.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(A.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(A.prototype,"attributes",{get:function(){return(!this.attributeMap||!this.attributeMap.nodes)&&(this.attributeMap=new h(this.attribs)),this.attributeMap}}),A.prototype.clone=function(){var x,M,N,X;N=Object.create(this),N.isRoot&&(N.documentObject=null),N.attribs={},X=this.attribs;for(M in X)b.call(X,M)&&(x=X[M],N.attribs[M]=x.clone());return N.children=[],this.children.forEach(function(B){var C;return C=B.clone(),C.parent=N,N.children.push(C)}),N},A.prototype.attribute=function(x,M){var N,X;if(x!=null&&(x=d(x)),s(x))for(N in x)b.call(x,N)&&(X=x[N],this.attribute(N,X));else c(M)&&(M=M.apply()),this.options.keepNullAttributes&&M==null?this.attribs[x]=new l(this,x,""):M!=null&&(this.attribs[x]=new l(this,x,M));return this},A.prototype.removeAttribute=function(x){var M,N,X;if(x==null)throw new Error("Missing attribute name. "+this.debugInfo());if(x=d(x),Array.isArray(x))for(N=0,X=x.length;N<X;N++)M=x[N],delete this.attribs[M];else delete this.attribs[x];return this},A.prototype.toString=function(x){return this.options.writer.element(this,this.options.writer.filterOptions(x))},A.prototype.att=function(x,M){return this.attribute(x,M)},A.prototype.a=function(x,M){return this.attribute(x,M)},A.prototype.getAttribute=function(x){return this.attribs.hasOwnProperty(x)?this.attribs[x].value:null},A.prototype.setAttribute=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNode=function(x){return this.attribs.hasOwnProperty(x)?this.attribs[x]:null},A.prototype.setAttributeNode=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.removeAttributeNode=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagName=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setAttributeNS=function(x,M,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.removeAttributeNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getAttributeNodeNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setAttributeNodeNS=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagNameNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.hasAttribute=function(x){return this.attribs.hasOwnProperty(x)},A.prototype.hasAttributeNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setIdAttribute=function(x,M){return this.attribs.hasOwnProperty(x)?this.attribs[x].isId:M},A.prototype.setIdAttributeNS=function(x,M,N){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.setIdAttributeNode=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagName=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByTagNameNS=function(x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.getElementsByClassName=function(x){throw new Error("This DOM method is not implemented."+this.debugInfo())},A.prototype.isEqualNode=function(x){var M,N,X;if(!A.__super__.isEqualNode.apply(this,arguments).isEqualNode(x)||x.namespaceURI!==this.namespaceURI||x.prefix!==this.prefix||x.localName!==this.localName||x.attribs.length!==this.attribs.length)return!1;for(M=N=0,X=this.attribs.length-1;0<=X?N<=X:N>=X;M=0<=X?++N:--N)if(!this.attribs[M].isEqualNode(x.attribs[M]))return!1;return!0},A})(o)}).call(hv)),jl.exports}var Hl={exports:{}},Yl={exports:{}},dv=Yl.exports,Xp;function yu(){return Xp||(Xp=1,(function(){var u,l=function(o,d){for(var c in d)h.call(d,c)&&(o[c]=d[c]);function s(){this.constructor=o}return s.prototype=d.prototype,o.prototype=new s,o.__super__=d.prototype,o},h={}.hasOwnProperty;u=Ze(),Yl.exports=(function(o){l(d,o);function d(c){d.__super__.constructor.call(this,c),this.value=""}return Object.defineProperty(d.prototype,"data",{get:function(){return this.value},set:function(c){return this.value=c||""}}),Object.defineProperty(d.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(d.prototype,"textContent",{get:function(){return this.value},set:function(c){return this.value=c||""}}),d.prototype.clone=function(){return Object.create(this)},d.prototype.substringData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.appendData=function(c){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.insertData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.deleteData=function(c,s){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.replaceData=function(c,s,f){throw new Error("This DOM method is not implemented."+this.debugInfo())},d.prototype.isEqualNode=function(c){return!(!d.__super__.isEqualNode.apply(this,arguments).isEqualNode(c)||c.data!==this.data)},d})(u)}).call(dv)),Yl.exports}var pv=Hl.exports,zp;function nc(){return zp||(zp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=le(),l=yu(),Hl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=u.CData,this.value=this.stringify.cdata(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.cdata(this,this.options.writer.filterOptions(s))},c})(l)}).call(pv)),Hl.exports}var Vl={exports:{}},yv=Vl.exports,qp;function ic(){return qp||(qp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=le(),l=yu(),Vl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=u.Comment,this.value=this.stringify.comment(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.comment(this,this.options.writer.filterOptions(s))},c})(l)}).call(yv)),Vl.exports}var Fl={exports:{}},mv=Fl.exports,Hp;function rc(){return Hp||(Hp=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;h=Dn().isObject,l=Ze(),u=le(),Fl.exports=(function(c){o(s,c);function s(f,g,b,S){var A;s.__super__.constructor.call(this,f),h(g)&&(A=g,g=A.version,b=A.encoding,S=A.standalone),g||(g="1.0"),this.type=u.Declaration,this.version=this.stringify.xmlVersion(g),b!=null&&(this.encoding=this.stringify.xmlEncoding(b)),S!=null&&(this.standalone=this.stringify.xmlStandalone(S))}return s.prototype.toString=function(f){return this.options.writer.declaration(this,this.options.writer.filterOptions(f))},s})(l)}).call(mv)),Fl.exports}var Gl={exports:{}},Il={exports:{}},gv=Il.exports,Yp;function ac(){return Yp||(Yp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=le(),Il.exports=(function(d){h(c,d);function c(s,f,g,b,S,A){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD element name. "+this.debugInfo());if(g==null)throw new Error("Missing DTD attribute name. "+this.debugInfo(f));if(!b)throw new Error("Missing DTD attribute type. "+this.debugInfo(f));if(!S)throw new Error("Missing DTD attribute default. "+this.debugInfo(f));if(S.indexOf("#")!==0&&(S="#"+S),!S.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(f));if(A&&!S.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(f));this.elementName=this.stringify.name(f),this.type=u.AttributeDeclaration,this.attributeName=this.stringify.name(g),this.attributeType=this.stringify.dtdAttType(b),A&&(this.defaultValue=this.stringify.dtdAttDefault(A)),this.defaultValueType=S}return c.prototype.toString=function(s){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(s))},c})(l)}).call(gv)),Il.exports}var Ql={exports:{}},vv=Ql.exports,Vp;function lc(){return Vp||(Vp=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;h=Dn().isObject,l=Ze(),u=le(),Ql.exports=(function(c){o(s,c);function s(f,g,b,S){if(s.__super__.constructor.call(this,f),b==null)throw new Error("Missing DTD entity name. "+this.debugInfo(b));if(S==null)throw new Error("Missing DTD entity value. "+this.debugInfo(b));if(this.pe=!!g,this.name=this.stringify.name(b),this.type=u.EntityDeclaration,!h(S))this.value=this.stringify.dtdEntityValue(S),this.internal=!0;else{if(!S.pubID&&!S.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(b));if(S.pubID&&!S.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(b));if(this.internal=!1,S.pubID!=null&&(this.pubID=this.stringify.dtdPubID(S.pubID)),S.sysID!=null&&(this.sysID=this.stringify.dtdSysID(S.sysID)),S.nData!=null&&(this.nData=this.stringify.dtdNData(S.nData)),this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(b))}}return Object.defineProperty(s.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(s.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(s.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(s.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(s.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(s.prototype,"xmlVersion",{get:function(){return null}}),s.prototype.toString=function(f){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(f))},s})(l)}).call(vv)),Ql.exports}var Zl={exports:{}},Ev=Zl.exports,Fp;function uc(){return Fp||(Fp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=le(),Zl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD element name. "+this.debugInfo());g||(g="(#PCDATA)"),Array.isArray(g)&&(g="("+g.join(",")+")"),this.name=this.stringify.name(f),this.type=u.ElementDeclaration,this.value=this.stringify.dtdElementValue(g)}return c.prototype.toString=function(s){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(s))},c})(l)}).call(Ev)),Zl.exports}var Kl={exports:{}},Tv=Kl.exports,Gp;function oc(){return Gp||(Gp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=le(),Kl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing DTD notation name. "+this.debugInfo(f));if(!g.pubID&&!g.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(f));this.name=this.stringify.name(f),this.type=u.NotationDeclaration,g.pubID!=null&&(this.pubID=this.stringify.dtdPubID(g.pubID)),g.sysID!=null&&(this.sysID=this.stringify.dtdSysID(g.sysID))}return Object.defineProperty(c.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(c.prototype,"systemId",{get:function(){return this.sysID}}),c.prototype.toString=function(s){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(s))},c})(l)}).call(Tv)),Kl.exports}var bv=Gl.exports,Ip;function sc(){return Ip||(Ip=1,(function(){var u,l,h,o,d,c,s,f,g=function(S,A){for(var x in A)b.call(A,x)&&(S[x]=A[x]);function M(){this.constructor=S}return M.prototype=A.prototype,S.prototype=new M,S.__super__=A.prototype,S},b={}.hasOwnProperty;f=Dn().isObject,s=Ze(),u=le(),l=ac(),o=lc(),h=uc(),d=oc(),c=tc(),Gl.exports=(function(S){g(A,S);function A(x,M,N){var X,B,C,_,E,U;if(A.__super__.constructor.call(this,x),this.type=u.DocType,x.children){for(_=x.children,B=0,C=_.length;B<C;B++)if(X=_[B],X.type===u.Element){this.name=X.name;break}}this.documentObject=x,f(M)&&(E=M,M=E.pubID,N=E.sysID),N==null&&(U=[M,N],N=U[0],M=U[1]),M!=null&&(this.pubID=this.stringify.dtdPubID(M)),N!=null&&(this.sysID=this.stringify.dtdSysID(N))}return Object.defineProperty(A.prototype,"entities",{get:function(){var x,M,N,X,B;for(X={},B=this.children,M=0,N=B.length;M<N;M++)x=B[M],x.type===u.EntityDeclaration&&!x.pe&&(X[x.name]=x);return new c(X)}}),Object.defineProperty(A.prototype,"notations",{get:function(){var x,M,N,X,B;for(X={},B=this.children,M=0,N=B.length;M<N;M++)x=B[M],x.type===u.NotationDeclaration&&(X[x.name]=x);return new c(X)}}),Object.defineProperty(A.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(A.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(A.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),A.prototype.element=function(x,M){var N;return N=new h(this,x,M),this.children.push(N),this},A.prototype.attList=function(x,M,N,X,B){var C;return C=new l(this,x,M,N,X,B),this.children.push(C),this},A.prototype.entity=function(x,M){var N;return N=new o(this,!1,x,M),this.children.push(N),this},A.prototype.pEntity=function(x,M){var N;return N=new o(this,!0,x,M),this.children.push(N),this},A.prototype.notation=function(x,M){var N;return N=new d(this,x,M),this.children.push(N),this},A.prototype.toString=function(x){return this.options.writer.docType(this,this.options.writer.filterOptions(x))},A.prototype.ele=function(x,M){return this.element(x,M)},A.prototype.att=function(x,M,N,X,B){return this.attList(x,M,N,X,B)},A.prototype.ent=function(x,M){return this.entity(x,M)},A.prototype.pent=function(x,M){return this.pEntity(x,M)},A.prototype.not=function(x,M){return this.notation(x,M)},A.prototype.up=function(){return this.root()||this.documentObject},A.prototype.isEqualNode=function(x){return!(!A.__super__.isEqualNode.apply(this,arguments).isEqualNode(x)||x.name!==this.name||x.publicId!==this.publicId||x.systemId!==this.systemId)},A})(s)}).call(bv)),Gl.exports}var Pl={exports:{}},Sv=Pl.exports,Qp;function cc(){return Qp||(Qp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=le(),l=Ze(),Pl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing raw text. "+this.debugInfo());this.type=u.Raw,this.value=this.stringify.raw(f)}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.raw(this,this.options.writer.filterOptions(s))},c})(l)}).call(Sv)),Pl.exports}var Jl={exports:{}},Dv=Jl.exports,Zp;function fc(){return Zp||(Zp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=le(),l=yu(),Jl.exports=(function(d){h(c,d);function c(s,f){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=u.Text,this.value=this.stringify.text(f)}return Object.defineProperty(c.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(c.prototype,"wholeText",{get:function(){var s,f,g;for(g="",f=this.previousSibling;f;)g=f.data+g,f=f.previousSibling;for(g+=this.data,s=this.nextSibling;s;)g=g+s.data,s=s.nextSibling;return g}}),c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.text(this,this.options.writer.filterOptions(s))},c.prototype.splitText=function(s){throw new Error("This DOM method is not implemented."+this.debugInfo())},c.prototype.replaceWholeText=function(s){throw new Error("This DOM method is not implemented."+this.debugInfo())},c})(l)}).call(Dv)),Jl.exports}var kl={exports:{}},Av=kl.exports,Kp;function hc(){return Kp||(Kp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;u=le(),l=yu(),kl.exports=(function(d){h(c,d);function c(s,f,g){if(c.__super__.constructor.call(this,s),f==null)throw new Error("Missing instruction target. "+this.debugInfo());this.type=u.ProcessingInstruction,this.target=this.stringify.insTarget(f),this.name=this.target,g&&(this.value=this.stringify.insValue(g))}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(s))},c.prototype.isEqualNode=function(s){return!(!c.__super__.isEqualNode.apply(this,arguments).isEqualNode(s)||s.target!==this.target)},c})(l)}).call(Av)),kl.exports}var Wl={exports:{}},Ov=Wl.exports,Pp;function Qy(){return Pp||(Pp=1,(function(){var u,l,h=function(d,c){for(var s in c)o.call(c,s)&&(d[s]=c[s]);function f(){this.constructor=d}return f.prototype=c.prototype,d.prototype=new f,d.__super__=c.prototype,d},o={}.hasOwnProperty;l=Ze(),u=le(),Wl.exports=(function(d){h(c,d);function c(s){c.__super__.constructor.call(this,s),this.type=u.Dummy}return c.prototype.clone=function(){return Object.create(this)},c.prototype.toString=function(s){return""},c})(l)}).call(Ov)),Wl.exports}var $l={exports:{}},wv=$l.exports,Jp;function Nv(){return Jp||(Jp=1,(function(){$l.exports=(function(){function u(l){this.nodes=l}return Object.defineProperty(u.prototype,"length",{get:function(){return this.nodes.length||0}}),u.prototype.clone=function(){return this.nodes=null},u.prototype.item=function(l){return this.nodes[l]||null},u})()}).call(wv)),$l.exports}var tu={exports:{}},xv=tu.exports,kp;function Mv(){return kp||(kp=1,(function(){tu.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(xv)),tu.exports}var _v=Bl.exports,Wp;function Ze(){return Wp||(Wp=1,(function(){var u,l,h,o,d,c,s,f,g,b,S,A,x,M,N,X,B,C={}.hasOwnProperty;B=Dn(),X=B.isObject,N=B.isFunction,M=B.isEmpty,x=B.getValue,f=null,h=null,o=null,d=null,c=null,S=null,A=null,b=null,s=null,l=null,g=null,u=null,Bl.exports=(function(){function _(E){this.parent=E,this.parent&&(this.options=this.parent.options,this.stringify=this.parent.stringify),this.value=null,this.children=[],this.baseURI=null,f||(f=ec(),h=nc(),o=ic(),d=rc(),c=sc(),S=cc(),A=fc(),b=hc(),s=Qy(),l=le(),g=Nv(),tc(),u=Mv())}return Object.defineProperty(_.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(_.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(_.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(_.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(_.prototype,"childNodes",{get:function(){return(!this.childNodeList||!this.childNodeList.nodes)&&(this.childNodeList=new g(this.children)),this.childNodeList}}),Object.defineProperty(_.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(_.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(_.prototype,"previousSibling",{get:function(){var E;return E=this.parent.children.indexOf(this),this.parent.children[E-1]||null}}),Object.defineProperty(_.prototype,"nextSibling",{get:function(){var E;return E=this.parent.children.indexOf(this),this.parent.children[E+1]||null}}),Object.defineProperty(_.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(_.prototype,"textContent",{get:function(){var E,U,j,W,ut;if(this.nodeType===l.Element||this.nodeType===l.DocumentFragment){for(ut="",W=this.children,U=0,j=W.length;U<j;U++)E=W[U],E.textContent&&(ut+=E.textContent);return ut}else return null},set:function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),_.prototype.setParent=function(E){var U,j,W,ut,$;for(this.parent=E,E&&(this.options=E.options,this.stringify=E.stringify),ut=this.children,$=[],j=0,W=ut.length;j<W;j++)U=ut[j],$.push(U.setParent(this));return $},_.prototype.element=function(E,U,j){var W,ut,$,v,J,it,st,Tt,bt,St,I;if(it=null,U===null&&j==null&&(bt=[{},null],U=bt[0],j=bt[1]),U==null&&(U={}),U=x(U),X(U)||(St=[U,j],j=St[0],U=St[1]),E!=null&&(E=x(E)),Array.isArray(E))for($=0,st=E.length;$<st;$++)ut=E[$],it=this.element(ut);else if(N(E))it=this.element(E.apply());else if(X(E)){for(J in E)if(C.call(E,J))if(I=E[J],N(I)&&(I=I.apply()),!this.options.ignoreDecorators&&this.stringify.convertAttKey&&J.indexOf(this.stringify.convertAttKey)===0)it=this.attribute(J.substr(this.stringify.convertAttKey.length),I);else if(!this.options.separateArrayItems&&Array.isArray(I)&&M(I))it=this.dummy();else if(X(I)&&M(I))it=this.element(J);else if(!this.options.keepNullNodes&&I==null)it=this.dummy();else if(!this.options.separateArrayItems&&Array.isArray(I))for(v=0,Tt=I.length;v<Tt;v++)ut=I[v],W={},W[J]=ut,it=this.element(W);else X(I)?!this.options.ignoreDecorators&&this.stringify.convertTextKey&&J.indexOf(this.stringify.convertTextKey)===0?it=this.element(I):(it=this.element(J),it.element(I)):it=this.element(J,I)}else!this.options.keepNullNodes&&j===null?it=this.dummy():!this.options.ignoreDecorators&&this.stringify.convertTextKey&&E.indexOf(this.stringify.convertTextKey)===0?it=this.text(j):!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&E.indexOf(this.stringify.convertCDataKey)===0?it=this.cdata(j):!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&E.indexOf(this.stringify.convertCommentKey)===0?it=this.comment(j):!this.options.ignoreDecorators&&this.stringify.convertRawKey&&E.indexOf(this.stringify.convertRawKey)===0?it=this.raw(j):!this.options.ignoreDecorators&&this.stringify.convertPIKey&&E.indexOf(this.stringify.convertPIKey)===0?it=this.instruction(E.substr(this.stringify.convertPIKey.length),j):it=this.node(E,U,j);if(it==null)throw new Error("Could not create any elements with: "+E+". "+this.debugInfo());return it},_.prototype.insertBefore=function(E,U,j){var W,ut,$,v,J;if(E?.type)return $=E,v=U,$.setParent(this),v?(ut=children.indexOf(v),J=children.splice(ut),children.push($),Array.prototype.push.apply(children,J)):children.push($),$;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(E));return ut=this.parent.children.indexOf(this),J=this.parent.children.splice(ut),W=this.parent.element(E,U,j),Array.prototype.push.apply(this.parent.children,J),W},_.prototype.insertAfter=function(E,U,j){var W,ut,$;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(E));return ut=this.parent.children.indexOf(this),$=this.parent.children.splice(ut+1),W=this.parent.element(E,U,j),Array.prototype.push.apply(this.parent.children,$),W},_.prototype.remove=function(){var E;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return E=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[E,E-E+1].concat([])),this.parent},_.prototype.node=function(E,U,j){var W,ut;return E!=null&&(E=x(E)),U||(U={}),U=x(U),X(U)||(ut=[U,j],j=ut[0],U=ut[1]),W=new f(this,E,U),j!=null&&W.text(j),this.children.push(W),W},_.prototype.text=function(E){var U;return X(E)&&this.element(E),U=new A(this,E),this.children.push(U),this},_.prototype.cdata=function(E){var U;return U=new h(this,E),this.children.push(U),this},_.prototype.comment=function(E){var U;return U=new o(this,E),this.children.push(U),this},_.prototype.commentBefore=function(E){var U,j;return U=this.parent.children.indexOf(this),j=this.parent.children.splice(U),this.parent.comment(E),Array.prototype.push.apply(this.parent.children,j),this},_.prototype.commentAfter=function(E){var U,j;return U=this.parent.children.indexOf(this),j=this.parent.children.splice(U+1),this.parent.comment(E),Array.prototype.push.apply(this.parent.children,j),this},_.prototype.raw=function(E){var U;return U=new S(this,E),this.children.push(U),this},_.prototype.dummy=function(){var E;return E=new s(this),E},_.prototype.instruction=function(E,U){var j,W,ut,$,v;if(E!=null&&(E=x(E)),U!=null&&(U=x(U)),Array.isArray(E))for($=0,v=E.length;$<v;$++)j=E[$],this.instruction(j);else if(X(E))for(j in E)C.call(E,j)&&(W=E[j],this.instruction(j,W));else N(U)&&(U=U.apply()),ut=new b(this,E,U),this.children.push(ut);return this},_.prototype.instructionBefore=function(E,U){var j,W;return j=this.parent.children.indexOf(this),W=this.parent.children.splice(j),this.parent.instruction(E,U),Array.prototype.push.apply(this.parent.children,W),this},_.prototype.instructionAfter=function(E,U){var j,W;return j=this.parent.children.indexOf(this),W=this.parent.children.splice(j+1),this.parent.instruction(E,U),Array.prototype.push.apply(this.parent.children,W),this},_.prototype.declaration=function(E,U,j){var W,ut;return W=this.document(),ut=new d(W,E,U,j),W.children.length===0?W.children.unshift(ut):W.children[0].type===l.Declaration?W.children[0]=ut:W.children.unshift(ut),W.root()||W},_.prototype.dtd=function(E,U){var j,W,ut,$,v,J,it,st,Tt,bt;for(W=this.document(),ut=new c(W,E,U),Tt=W.children,$=v=0,it=Tt.length;v<it;$=++v)if(j=Tt[$],j.type===l.DocType)return W.children[$]=ut,ut;for(bt=W.children,$=J=0,st=bt.length;J<st;$=++J)if(j=bt[$],j.isRoot)return W.children.splice($,0,ut),ut;return W.children.push(ut),ut},_.prototype.up=function(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent},_.prototype.root=function(){var E;for(E=this;E;){if(E.type===l.Document)return E.rootObject;if(E.isRoot)return E;E=E.parent}},_.prototype.document=function(){var E;for(E=this;E;){if(E.type===l.Document)return E;E=E.parent}},_.prototype.end=function(E){return this.document().end(E)},_.prototype.prev=function(){var E;if(E=this.parent.children.indexOf(this),E<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[E-1]},_.prototype.next=function(){var E;if(E=this.parent.children.indexOf(this),E===-1||E===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[E+1]},_.prototype.importDocument=function(E){var U;return U=E.root().clone(),U.parent=this,U.isRoot=!1,this.children.push(U),this},_.prototype.debugInfo=function(E){var U,j;return E=E||this.name,E==null&&!((U=this.parent)!=null&&U.name)?"":E==null?"parent: <"+this.parent.name+">":(j=this.parent)!=null&&j.name?"node: <"+E+">, parent: <"+this.parent.name+">":"node: <"+E+">"},_.prototype.ele=function(E,U,j){return this.element(E,U,j)},_.prototype.nod=function(E,U,j){return this.node(E,U,j)},_.prototype.txt=function(E){return this.text(E)},_.prototype.dat=function(E){return this.cdata(E)},_.prototype.com=function(E){return this.comment(E)},_.prototype.ins=function(E,U){return this.instruction(E,U)},_.prototype.doc=function(){return this.document()},_.prototype.dec=function(E,U,j){return this.declaration(E,U,j)},_.prototype.e=function(E,U,j){return this.element(E,U,j)},_.prototype.n=function(E,U,j){return this.node(E,U,j)},_.prototype.t=function(E){return this.text(E)},_.prototype.d=function(E){return this.cdata(E)},_.prototype.c=function(E){return this.comment(E)},_.prototype.r=function(E){return this.raw(E)},_.prototype.i=function(E,U){return this.instruction(E,U)},_.prototype.u=function(){return this.up()},_.prototype.importXMLBuilder=function(E){return this.importDocument(E)},_.prototype.replaceChild=function(E,U){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.removeChild=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.appendChild=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.hasChildNodes=function(){return this.children.length!==0},_.prototype.cloneNode=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.normalize=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isSupported=function(E,U){return!0},_.prototype.hasAttributes=function(){return this.attribs.length!==0},_.prototype.compareDocumentPosition=function(E){var U,j;return U=this,U===E?0:this.document()!==E.document()?(j=u.Disconnected|u.ImplementationSpecific,Math.random()<.5?j|=u.Preceding:j|=u.Following,j):U.isAncestor(E)?u.Contains|u.Preceding:U.isDescendant(E)?u.Contains|u.Following:U.isPreceding(E)?u.Preceding:u.Following},_.prototype.isSameNode=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.lookupPrefix=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isDefaultNamespace=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.lookupNamespaceURI=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.isEqualNode=function(E){var U,j,W;if(E.nodeType!==this.nodeType||E.children.length!==this.children.length)return!1;for(U=j=0,W=this.children.length-1;0<=W?j<=W:j>=W;U=0<=W?++j:--j)if(!this.children[U].isEqualNode(E.children[U]))return!1;return!0},_.prototype.getFeature=function(E,U){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.setUserData=function(E,U,j){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.getUserData=function(E){throw new Error("This DOM method is not implemented."+this.debugInfo())},_.prototype.contains=function(E){return E?E===this||this.isDescendant(E):!1},_.prototype.isDescendant=function(E){var U,j,W,ut,$;for($=this.children,W=0,ut=$.length;W<ut;W++)if(U=$[W],E===U||(j=U.isDescendant(E),j))return!0;return!1},_.prototype.isAncestor=function(E){return E.isDescendant(this)},_.prototype.isPreceding=function(E){var U,j;return U=this.treePosition(E),j=this.treePosition(this),U===-1||j===-1?!1:U<j},_.prototype.isFollowing=function(E){var U,j;return U=this.treePosition(E),j=this.treePosition(this),U===-1||j===-1?!1:U>j},_.prototype.treePosition=function(E){var U,j;return j=0,U=!1,this.foreachTreeNode(this.document(),function(W){if(j++,!U&&W===E)return U=!0}),U?j:-1},_.prototype.foreachTreeNode=function(E,U){var j,W,ut,$,v;for(E||(E=this.document()),$=E.children,W=0,ut=$.length;W<ut;W++){if(j=$[W],v=U(j))return v;if(v=this.foreachTreeNode(j,U),v)return v}},_})()}).call(_v)),Bl.exports}var eu={exports:{}},Rv=eu.exports,$p;function Zy(){return $p||($p=1,(function(){var u=function(h,o){return function(){return h.apply(o,arguments)}},l={}.hasOwnProperty;eu.exports=(function(){function h(o){this.assertLegalName=u(this.assertLegalName,this),this.assertLegalChar=u(this.assertLegalChar,this);var d,c,s;o||(o={}),this.options=o,this.options.version||(this.options.version="1.0"),c=o.stringify||{};for(d in c)l.call(c,d)&&(s=c[d],this[d]=s)}return h.prototype.name=function(o){return this.options.noValidation?o:this.assertLegalName(""+o||"")},h.prototype.text=function(o){return this.options.noValidation?o:this.assertLegalChar(this.textEscape(""+o||""))},h.prototype.cdata=function(o){return this.options.noValidation?o:(o=""+o||"",o=o.replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(o))},h.prototype.comment=function(o){if(this.options.noValidation)return o;if(o=""+o||"",o.match(/--/))throw new Error("Comment text cannot contain double-hypen: "+o);return this.assertLegalChar(o)},h.prototype.raw=function(o){return this.options.noValidation?o:""+o||""},h.prototype.attValue=function(o){return this.options.noValidation?o:this.assertLegalChar(this.attEscape(o=""+o||""))},h.prototype.insTarget=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.insValue=function(o){if(this.options.noValidation)return o;if(o=""+o||"",o.match(/\?>/))throw new Error("Invalid processing instruction value: "+o);return this.assertLegalChar(o)},h.prototype.xmlVersion=function(o){if(this.options.noValidation)return o;if(o=""+o||"",!o.match(/1\.[0-9]+/))throw new Error("Invalid version number: "+o);return o},h.prototype.xmlEncoding=function(o){if(this.options.noValidation)return o;if(o=""+o||"",!o.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+o);return this.assertLegalChar(o)},h.prototype.xmlStandalone=function(o){return this.options.noValidation?o:o?"yes":"no"},h.prototype.dtdPubID=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdSysID=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdElementValue=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdAttType=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdAttDefault=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdEntityValue=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.dtdNData=function(o){return this.options.noValidation?o:this.assertLegalChar(""+o||"")},h.prototype.convertAttKey="@",h.prototype.convertPIKey="?",h.prototype.convertTextKey="#text",h.prototype.convertCDataKey="#cdata",h.prototype.convertCommentKey="#comment",h.prototype.convertRawKey="#raw",h.prototype.assertLegalChar=function(o){var d,c;if(this.options.noValidation)return o;if(d="",this.options.version==="1.0"){if(d=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,c=o.match(d))throw new Error("Invalid character in string: "+o+" at index "+c.index)}else if(this.options.version==="1.1"&&(d=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,c=o.match(d)))throw new Error("Invalid character in string: "+o+" at index "+c.index);return o},h.prototype.assertLegalName=function(o){var d;if(this.options.noValidation)return o;if(this.assertLegalChar(o),d=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!o.match(d))throw new Error("Invalid character in name");return o},h.prototype.textEscape=function(o){var d;return this.options.noValidation?o:(d=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,o.replace(d,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;"))},h.prototype.attEscape=function(o){var d;return this.options.noValidation?o:(d=this.options.noDoubleEncoding?/(?!&\S+;)&/g:/&/g,o.replace(d,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;"))},h})()}).call(Rv)),eu.exports}var nu={exports:{}},iu={exports:{}},ru={exports:{}},Cv=ru.exports,ty;function mu(){return ty||(ty=1,(function(){ru.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(Cv)),ru.exports}var Lv=iu.exports,ey;function Ky(){return ey||(ey=1,(function(){var u,l,h,o={}.hasOwnProperty;h=Dn().assign,u=le(),rc(),sc(),nc(),ic(),ec(),cc(),fc(),hc(),Qy(),ac(),uc(),lc(),oc(),l=mu(),iu.exports=(function(){function d(c){var s,f,g;c||(c={}),this.options=c,f=c.writer||{};for(s in f)o.call(f,s)&&(g=f[s],this["_"+s]=this[s],this[s]=g)}return d.prototype.filterOptions=function(c){var s,f,g,b,S,A,x,M;return c||(c={}),c=h({},this.options,c),s={writer:this},s.pretty=c.pretty||!1,s.allowEmpty=c.allowEmpty||!1,s.indent=(f=c.indent)!=null?f:"  ",s.newline=(g=c.newline)!=null?g:`
`,s.offset=(b=c.offset)!=null?b:0,s.dontPrettyTextNodes=(S=(A=c.dontPrettyTextNodes)!=null?A:c.dontprettytextnodes)!=null?S:0,s.spaceBeforeSlash=(x=(M=c.spaceBeforeSlash)!=null?M:c.spacebeforeslash)!=null?x:"",s.spaceBeforeSlash===!0&&(s.spaceBeforeSlash=" "),s.suppressPrettyCount=0,s.user={},s.state=l.None,s},d.prototype.indent=function(c,s,f){var g;return!s.pretty||s.suppressPrettyCount?"":s.pretty&&(g=(f||0)+s.offset+1,g>0)?new Array(g).join(s.indent):""},d.prototype.endline=function(c,s,f){return!s.pretty||s.suppressPrettyCount?"":s.newline},d.prototype.attribute=function(c,s,f){var g;return this.openAttribute(c,s,f),g=" "+c.name+'="'+c.value+'"',this.closeAttribute(c,s,f),g},d.prototype.cdata=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<![CDATA[",s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+="]]>"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.comment=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!-- ",s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=" -->"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.declaration=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<?xml",s.state=l.InsideTag,g+=' version="'+c.version+'"',c.encoding!=null&&(g+=' encoding="'+c.encoding+'"'),c.standalone!=null&&(g+=' standalone="'+c.standalone+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+"?>",g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.docType=function(c,s,f){var g,b,S,A,x;if(f||(f=0),this.openNode(c,s,f),s.state=l.OpenTag,A=this.indent(c,s,f),A+="<!DOCTYPE "+c.root().name,c.pubID&&c.sysID?A+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.sysID&&(A+=' SYSTEM "'+c.sysID+'"'),c.children.length>0){for(A+=" [",A+=this.endline(c,s,f),s.state=l.InsideTag,x=c.children,b=0,S=x.length;b<S;b++)g=x[b],A+=this.writeChildNode(g,s,f+1);s.state=l.CloseTag,A+="]"}return s.state=l.CloseTag,A+=s.spaceBeforeSlash+">",A+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),A},d.prototype.element=function(c,s,f){var g,b,S,A,x,M,N,X,B,C,_,E,U,j;f||(f=0),C=!1,_="",this.openNode(c,s,f),s.state=l.OpenTag,_+=this.indent(c,s,f)+"<"+c.name,E=c.attribs;for(B in E)o.call(E,B)&&(g=E[B],_+=this.attribute(g,s,f));if(S=c.children.length,A=S===0?null:c.children[0],S===0||c.children.every(function(W){return(W.type===u.Text||W.type===u.Raw)&&W.value===""}))s.allowEmpty?(_+=">",s.state=l.CloseTag,_+="</"+c.name+">"+this.endline(c,s,f)):(s.state=l.CloseTag,_+=s.spaceBeforeSlash+"/>"+this.endline(c,s,f));else if(s.pretty&&S===1&&(A.type===u.Text||A.type===u.Raw)&&A.value!=null)_+=">",s.state=l.InsideTag,s.suppressPrettyCount++,C=!0,_+=this.writeChildNode(A,s,f+1),s.suppressPrettyCount--,C=!1,s.state=l.CloseTag,_+="</"+c.name+">"+this.endline(c,s,f);else{if(s.dontPrettyTextNodes){for(U=c.children,x=0,N=U.length;x<N;x++)if(b=U[x],(b.type===u.Text||b.type===u.Raw)&&b.value!=null){s.suppressPrettyCount++,C=!0;break}}for(_+=">"+this.endline(c,s,f),s.state=l.InsideTag,j=c.children,M=0,X=j.length;M<X;M++)b=j[M],_+=this.writeChildNode(b,s,f+1);s.state=l.CloseTag,_+=this.indent(c,s,f)+"</"+c.name+">",C&&s.suppressPrettyCount--,_+=this.endline(c,s,f),s.state=l.None}return this.closeNode(c,s,f),_},d.prototype.writeChildNode=function(c,s,f){switch(c.type){case u.CData:return this.cdata(c,s,f);case u.Comment:return this.comment(c,s,f);case u.Element:return this.element(c,s,f);case u.Raw:return this.raw(c,s,f);case u.Text:return this.text(c,s,f);case u.ProcessingInstruction:return this.processingInstruction(c,s,f);case u.Dummy:return"";case u.Declaration:return this.declaration(c,s,f);case u.DocType:return this.docType(c,s,f);case u.AttributeDeclaration:return this.dtdAttList(c,s,f);case u.ElementDeclaration:return this.dtdElement(c,s,f);case u.EntityDeclaration:return this.dtdEntity(c,s,f);case u.NotationDeclaration:return this.dtdNotation(c,s,f);default:throw new Error("Unknown XML node type: "+c.constructor.name)}},d.prototype.processingInstruction=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<?",s.state=l.InsideTag,g+=c.target,c.value&&(g+=" "+c.value),s.state=l.CloseTag,g+=s.spaceBeforeSlash+"?>",g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.raw=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f),s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.text=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f),s.state=l.InsideTag,g+=c.value,s.state=l.CloseTag,g+=this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdAttList=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ATTLIST",s.state=l.InsideTag,g+=" "+c.elementName+" "+c.attributeName+" "+c.attributeType,c.defaultValueType!=="#DEFAULT"&&(g+=" "+c.defaultValueType),c.defaultValue&&(g+=' "'+c.defaultValue+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdElement=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ELEMENT",s.state=l.InsideTag,g+=" "+c.name+" "+c.value,s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdEntity=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!ENTITY",s.state=l.InsideTag,c.pe&&(g+=" %"),g+=" "+c.name,c.value?g+=' "'+c.value+'"':(c.pubID&&c.sysID?g+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.sysID&&(g+=' SYSTEM "'+c.sysID+'"'),c.nData&&(g+=" NDATA "+c.nData)),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.dtdNotation=function(c,s,f){var g;return this.openNode(c,s,f),s.state=l.OpenTag,g=this.indent(c,s,f)+"<!NOTATION",s.state=l.InsideTag,g+=" "+c.name,c.pubID&&c.sysID?g+=' PUBLIC "'+c.pubID+'" "'+c.sysID+'"':c.pubID?g+=' PUBLIC "'+c.pubID+'"':c.sysID&&(g+=' SYSTEM "'+c.sysID+'"'),s.state=l.CloseTag,g+=s.spaceBeforeSlash+">"+this.endline(c,s,f),s.state=l.None,this.closeNode(c,s,f),g},d.prototype.openNode=function(c,s,f){},d.prototype.closeNode=function(c,s,f){},d.prototype.openAttribute=function(c,s,f){},d.prototype.closeAttribute=function(c,s,f){},d})()}).call(Lv)),iu.exports}var Uv=nu.exports,ny;function dc(){return ny||(ny=1,(function(){var u,l=function(o,d){for(var c in d)h.call(d,c)&&(o[c]=d[c]);function s(){this.constructor=o}return s.prototype=d.prototype,o.prototype=new s,o.__super__=d.prototype,o},h={}.hasOwnProperty;u=Ky(),nu.exports=(function(o){l(d,o);function d(c){d.__super__.constructor.call(this,c)}return d.prototype.document=function(c,s){var f,g,b,S,A;for(s=this.filterOptions(s),S="",A=c.children,g=0,b=A.length;g<b;g++)f=A[g],S+=this.writeChildNode(f,s,0);return s.pretty&&S.slice(-s.newline.length)===s.newline&&(S=S.slice(0,-s.newline.length)),S},d})(u)}).call(Uv)),nu.exports}var Bv=Rl.exports,iy;function Py(){return iy||(iy=1,(function(){var u,l,h,o,d,c,s,f=function(b,S){for(var A in S)g.call(S,A)&&(b[A]=S[A]);function x(){this.constructor=b}return x.prototype=S.prototype,b.prototype=new x,b.__super__=S.prototype,b},g={}.hasOwnProperty;s=Dn().isPlainObject,h=Gy(),l=ov(),o=Ze(),u=le(),c=Zy(),d=dc(),Rl.exports=(function(b){f(S,b);function S(A){S.__super__.constructor.call(this,null),this.name="#document",this.type=u.Document,this.documentURI=null,this.domConfig=new l,A||(A={}),A.writer||(A.writer=new d),this.options=A,this.stringify=new c(A)}return Object.defineProperty(S.prototype,"implementation",{value:new h}),Object.defineProperty(S.prototype,"doctype",{get:function(){var A,x,M,N;for(N=this.children,x=0,M=N.length;x<M;x++)if(A=N[x],A.type===u.DocType)return A;return null}}),Object.defineProperty(S.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(S.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(S.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(S.prototype,"xmlEncoding",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].encoding:null}}),Object.defineProperty(S.prototype,"xmlStandalone",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].standalone==="yes":!1}}),Object.defineProperty(S.prototype,"xmlVersion",{get:function(){return this.children.length!==0&&this.children[0].type===u.Declaration?this.children[0].version:"1.0"}}),Object.defineProperty(S.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(S.prototype,"origin",{get:function(){return null}}),Object.defineProperty(S.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(S.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(S.prototype,"contentType",{get:function(){return null}}),S.prototype.end=function(A){var x;return x={},A?s(A)&&(x=A,A=this.options.writer):A=this.options.writer,A.document(this,A.filterOptions(x))},S.prototype.toString=function(A){return this.options.writer.document(this,this.options.writer.filterOptions(A))},S.prototype.createElement=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createDocumentFragment=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createTextNode=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createComment=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createCDATASection=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createProcessingInstruction=function(A,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createAttribute=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createEntityReference=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.getElementsByTagName=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.importNode=function(A,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createElementNS=function(A,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createAttributeNS=function(A,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.getElementsByTagNameNS=function(A,x){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.getElementById=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.adoptNode=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.normalizeDocument=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.renameNode=function(A,x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.getElementsByClassName=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createEvent=function(A){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createRange=function(){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createNodeIterator=function(A,x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},S.prototype.createTreeWalker=function(A,x,M){throw new Error("This DOM method is not implemented."+this.debugInfo())},S})(o)}).call(Bv)),Rl.exports}var au={exports:{}},jv=au.exports,ry;function Xv(){return ry||(ry=1,(function(){var u,l,h,o,d,c,s,f,g,b,S,A,x,M,N,X,B,C,_,E,U,j,W,ut={}.hasOwnProperty;W=Dn(),U=W.isObject,E=W.isFunction,j=W.isPlainObject,_=W.getValue,u=le(),A=Py(),x=ec(),o=nc(),d=ic(),N=cc(),C=fc(),M=hc(),b=rc(),S=sc(),c=ac(),f=lc(),s=uc(),g=oc(),h=Iy(),B=Zy(),X=dc(),l=mu(),au.exports=(function(){function $(v,J,it){var st;this.name="?xml",this.type=u.Document,v||(v={}),st={},v.writer?j(v.writer)&&(st=v.writer,v.writer=new X):v.writer=new X,this.options=v,this.writer=v.writer,this.writerOptions=this.writer.filterOptions(st),this.stringify=new B(v),this.onDataCallback=J||function(){},this.onEndCallback=it||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}return $.prototype.createChildNode=function(v){var J,it,st,Tt,bt,St,I,rt;switch(v.type){case u.CData:this.cdata(v.value);break;case u.Comment:this.comment(v.value);break;case u.Element:st={},I=v.attribs;for(it in I)ut.call(I,it)&&(J=I[it],st[it]=J.value);this.node(v.name,st);break;case u.Dummy:this.dummy();break;case u.Raw:this.raw(v.value);break;case u.Text:this.text(v.value);break;case u.ProcessingInstruction:this.instruction(v.target,v.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+v.constructor.name)}for(rt=v.children,bt=0,St=rt.length;bt<St;bt++)Tt=rt[bt],this.createChildNode(Tt),Tt.type===u.Element&&this.up();return this},$.prototype.dummy=function(){return this},$.prototype.node=function(v,J,it){var st;if(v==null)throw new Error("Missing node name.");if(this.root&&this.currentLevel===-1)throw new Error("Document can only have one root node. "+this.debugInfo(v));return this.openCurrent(),v=_(v),J==null&&(J={}),J=_(J),U(J)||(st=[J,it],it=st[0],J=st[1]),this.currentNode=new x(this,v,J),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,it!=null&&this.text(it),this},$.prototype.element=function(v,J,it){var st,Tt,bt,St,I,rt;if(this.currentNode&&this.currentNode.type===u.DocType)this.dtdElement.apply(this,arguments);else if(Array.isArray(v)||U(v)||E(v))for(St=this.options.noValidation,this.options.noValidation=!0,rt=new A(this.options).element("TEMP_ROOT"),rt.element(v),this.options.noValidation=St,I=rt.children,Tt=0,bt=I.length;Tt<bt;Tt++)st=I[Tt],this.createChildNode(st),st.type===u.Element&&this.up();else this.node(v,J,it);return this},$.prototype.attribute=function(v,J){var it,st;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(v));if(v!=null&&(v=_(v)),U(v))for(it in v)ut.call(v,it)&&(st=v[it],this.attribute(it,st));else E(J)&&(J=J.apply()),this.options.keepNullAttributes&&J==null?this.currentNode.attribs[v]=new h(this,v,""):J!=null&&(this.currentNode.attribs[v]=new h(this,v,J));return this},$.prototype.text=function(v){var J;return this.openCurrent(),J=new C(this,v),this.onData(this.writer.text(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.cdata=function(v){var J;return this.openCurrent(),J=new o(this,v),this.onData(this.writer.cdata(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.comment=function(v){var J;return this.openCurrent(),J=new d(this,v),this.onData(this.writer.comment(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.raw=function(v){var J;return this.openCurrent(),J=new N(this,v),this.onData(this.writer.raw(J,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.instruction=function(v,J){var it,st,Tt,bt,St;if(this.openCurrent(),v!=null&&(v=_(v)),J!=null&&(J=_(J)),Array.isArray(v))for(it=0,bt=v.length;it<bt;it++)st=v[it],this.instruction(st);else if(U(v))for(st in v)ut.call(v,st)&&(Tt=v[st],this.instruction(st,Tt));else E(J)&&(J=J.apply()),St=new M(this,v,J),this.onData(this.writer.processingInstruction(St,this.writerOptions,this.currentLevel+1),this.currentLevel+1);return this},$.prototype.declaration=function(v,J,it){var st;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return st=new b(this,v,J,it),this.onData(this.writer.declaration(st,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.doctype=function(v,J,it){if(this.openCurrent(),v==null)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new S(this,J,it),this.currentNode.rootNodeName=v,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this},$.prototype.dtdElement=function(v,J){var it;return this.openCurrent(),it=new s(this,v,J),this.onData(this.writer.dtdElement(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.attList=function(v,J,it,st,Tt){var bt;return this.openCurrent(),bt=new c(this,v,J,it,st,Tt),this.onData(this.writer.dtdAttList(bt,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.entity=function(v,J){var it;return this.openCurrent(),it=new f(this,!1,v,J),this.onData(this.writer.dtdEntity(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.pEntity=function(v,J){var it;return this.openCurrent(),it=new f(this,!0,v,J),this.onData(this.writer.dtdEntity(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.notation=function(v,J){var it;return this.openCurrent(),it=new g(this,v,J),this.onData(this.writer.dtdNotation(it,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this},$.prototype.up=function(){if(this.currentLevel<0)throw new Error("The document node has no parent.");return this.currentNode?(this.currentNode.children?this.closeNode(this.currentNode):this.openNode(this.currentNode),this.currentNode=null):this.closeNode(this.openTags[this.currentLevel]),delete this.openTags[this.currentLevel],this.currentLevel--,this},$.prototype.end=function(){for(;this.currentLevel>=0;)this.up();return this.onEnd()},$.prototype.openCurrent=function(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)},$.prototype.openNode=function(v){var J,it,st,Tt;if(!v.isOpen){if(!this.root&&this.currentLevel===0&&v.type===u.Element&&(this.root=v),it="",v.type===u.Element){this.writerOptions.state=l.OpenTag,it=this.writer.indent(v,this.writerOptions,this.currentLevel)+"<"+v.name,Tt=v.attribs;for(st in Tt)ut.call(Tt,st)&&(J=Tt[st],it+=this.writer.attribute(J,this.writerOptions,this.currentLevel));it+=(v.children?">":"/>")+this.writer.endline(v,this.writerOptions,this.currentLevel),this.writerOptions.state=l.InsideTag}else this.writerOptions.state=l.OpenTag,it=this.writer.indent(v,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+v.rootNodeName,v.pubID&&v.sysID?it+=' PUBLIC "'+v.pubID+'" "'+v.sysID+'"':v.sysID&&(it+=' SYSTEM "'+v.sysID+'"'),v.children?(it+=" [",this.writerOptions.state=l.InsideTag):(this.writerOptions.state=l.CloseTag,it+=">"),it+=this.writer.endline(v,this.writerOptions,this.currentLevel);return this.onData(it,this.currentLevel),v.isOpen=!0}},$.prototype.closeNode=function(v){var J;if(!v.isClosed)return J="",this.writerOptions.state=l.CloseTag,v.type===u.Element?J=this.writer.indent(v,this.writerOptions,this.currentLevel)+"</"+v.name+">"+this.writer.endline(v,this.writerOptions,this.currentLevel):J=this.writer.indent(v,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(v,this.writerOptions,this.currentLevel),this.writerOptions.state=l.None,this.onData(J,this.currentLevel),v.isClosed=!0},$.prototype.onData=function(v,J){return this.documentStarted=!0,this.onDataCallback(v,J+1)},$.prototype.onEnd=function(){return this.documentCompleted=!0,this.onEndCallback()},$.prototype.debugInfo=function(v){return v==null?"":"node: <"+v+">"},$.prototype.ele=function(){return this.element.apply(this,arguments)},$.prototype.nod=function(v,J,it){return this.node(v,J,it)},$.prototype.txt=function(v){return this.text(v)},$.prototype.dat=function(v){return this.cdata(v)},$.prototype.com=function(v){return this.comment(v)},$.prototype.ins=function(v,J){return this.instruction(v,J)},$.prototype.dec=function(v,J,it){return this.declaration(v,J,it)},$.prototype.dtd=function(v,J,it){return this.doctype(v,J,it)},$.prototype.e=function(v,J,it){return this.element(v,J,it)},$.prototype.n=function(v,J,it){return this.node(v,J,it)},$.prototype.t=function(v){return this.text(v)},$.prototype.d=function(v){return this.cdata(v)},$.prototype.c=function(v){return this.comment(v)},$.prototype.r=function(v){return this.raw(v)},$.prototype.i=function(v,J){return this.instruction(v,J)},$.prototype.att=function(){return this.currentNode&&this.currentNode.type===u.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},$.prototype.a=function(){return this.currentNode&&this.currentNode.type===u.DocType?this.attList.apply(this,arguments):this.attribute.apply(this,arguments)},$.prototype.ent=function(v,J){return this.entity(v,J)},$.prototype.pent=function(v,J){return this.pEntity(v,J)},$.prototype.not=function(v,J){return this.notation(v,J)},$})()}).call(jv)),au.exports}var lu={exports:{}},zv=lu.exports,ay;function qv(){return ay||(ay=1,(function(){var u,l,h,o=function(c,s){for(var f in s)d.call(s,f)&&(c[f]=s[f]);function g(){this.constructor=c}return g.prototype=s.prototype,c.prototype=new g,c.__super__=s.prototype,c},d={}.hasOwnProperty;u=le(),h=Ky(),l=mu(),lu.exports=(function(c){o(s,c);function s(f,g){this.stream=f,s.__super__.constructor.call(this,g)}return s.prototype.endline=function(f,g,b){return f.isLastRootNode&&g.state===l.CloseTag?"":s.__super__.endline.call(this,f,g,b)},s.prototype.document=function(f,g){var b,S,A,x,M,N,X,B,C;for(X=f.children,S=A=0,M=X.length;A<M;S=++A)b=X[S],b.isLastRootNode=S===f.children.length-1;for(g=this.filterOptions(g),B=f.children,C=[],x=0,N=B.length;x<N;x++)b=B[x],C.push(this.writeChildNode(b,g,0));return C},s.prototype.attribute=function(f,g,b){return this.stream.write(s.__super__.attribute.call(this,f,g,b))},s.prototype.cdata=function(f,g,b){return this.stream.write(s.__super__.cdata.call(this,f,g,b))},s.prototype.comment=function(f,g,b){return this.stream.write(s.__super__.comment.call(this,f,g,b))},s.prototype.declaration=function(f,g,b){return this.stream.write(s.__super__.declaration.call(this,f,g,b))},s.prototype.docType=function(f,g,b){var S,A,x,M;if(b||(b=0),this.openNode(f,g,b),g.state=l.OpenTag,this.stream.write(this.indent(f,g,b)),this.stream.write("<!DOCTYPE "+f.root().name),f.pubID&&f.sysID?this.stream.write(' PUBLIC "'+f.pubID+'" "'+f.sysID+'"'):f.sysID&&this.stream.write(' SYSTEM "'+f.sysID+'"'),f.children.length>0){for(this.stream.write(" ["),this.stream.write(this.endline(f,g,b)),g.state=l.InsideTag,M=f.children,A=0,x=M.length;A<x;A++)S=M[A],this.writeChildNode(S,g,b+1);g.state=l.CloseTag,this.stream.write("]")}return g.state=l.CloseTag,this.stream.write(g.spaceBeforeSlash+">"),this.stream.write(this.endline(f,g,b)),g.state=l.None,this.closeNode(f,g,b)},s.prototype.element=function(f,g,b){var S,A,x,M,N,X,B,C,_;b||(b=0),this.openNode(f,g,b),g.state=l.OpenTag,this.stream.write(this.indent(f,g,b)+"<"+f.name),C=f.attribs;for(B in C)d.call(C,B)&&(S=C[B],this.attribute(S,g,b));if(x=f.children.length,M=x===0?null:f.children[0],x===0||f.children.every(function(E){return(E.type===u.Text||E.type===u.Raw)&&E.value===""}))g.allowEmpty?(this.stream.write(">"),g.state=l.CloseTag,this.stream.write("</"+f.name+">")):(g.state=l.CloseTag,this.stream.write(g.spaceBeforeSlash+"/>"));else if(g.pretty&&x===1&&(M.type===u.Text||M.type===u.Raw)&&M.value!=null)this.stream.write(">"),g.state=l.InsideTag,g.suppressPrettyCount++,this.writeChildNode(M,g,b+1),g.suppressPrettyCount--,g.state=l.CloseTag,this.stream.write("</"+f.name+">");else{for(this.stream.write(">"+this.endline(f,g,b)),g.state=l.InsideTag,_=f.children,N=0,X=_.length;N<X;N++)A=_[N],this.writeChildNode(A,g,b+1);g.state=l.CloseTag,this.stream.write(this.indent(f,g,b)+"</"+f.name+">")}return this.stream.write(this.endline(f,g,b)),g.state=l.None,this.closeNode(f,g,b)},s.prototype.processingInstruction=function(f,g,b){return this.stream.write(s.__super__.processingInstruction.call(this,f,g,b))},s.prototype.raw=function(f,g,b){return this.stream.write(s.__super__.raw.call(this,f,g,b))},s.prototype.text=function(f,g,b){return this.stream.write(s.__super__.text.call(this,f,g,b))},s.prototype.dtdAttList=function(f,g,b){return this.stream.write(s.__super__.dtdAttList.call(this,f,g,b))},s.prototype.dtdElement=function(f,g,b){return this.stream.write(s.__super__.dtdElement.call(this,f,g,b))},s.prototype.dtdEntity=function(f,g,b){return this.stream.write(s.__super__.dtdEntity.call(this,f,g,b))},s.prototype.dtdNotation=function(f,g,b){return this.stream.write(s.__super__.dtdNotation.call(this,f,g,b))},s})(h)}).call(zv)),lu.exports}var ly;function Hv(){return ly||(ly=1,(function(){var u,l,h,o,d,c,s,f,g,b;b=Dn(),f=b.assign,g=b.isFunction,h=Gy(),o=Py(),d=Xv(),s=dc(),c=qv(),u=le(),l=mu(),ln.create=function(S,A,x,M){var N,X;if(S==null)throw new Error("Root element needs a name.");return M=f({},A,x,M),N=new o(M),X=N.element(S),M.headless||(N.declaration(M),(M.pubID!=null||M.sysID!=null)&&N.dtd(M)),X},ln.begin=function(S,A,x){var M;return g(S)&&(M=[S,A],A=M[0],x=M[1],S={}),A?new d(S,A,x):new o(S)},ln.stringWriter=function(S){return new s(S)},ln.streamWriter=function(S,A){return new c(S,A)},ln.implementation=new h,ln.nodeType=u,ln.writerState=l}).call(ln)),ln}var uy;function Yv(){return uy||(uy=1,(function(){var u,l,h,o,d,c={}.hasOwnProperty;u=Hv(),l=$s().defaults,o=function(s){return typeof s=="string"&&(s.indexOf("&")>=0||s.indexOf(">")>=0||s.indexOf("<")>=0)},d=function(s){return"<![CDATA["+h(s)+"]]>"},h=function(s){return s.replace("]]>","]]]]><![CDATA[>")},Sl.Builder=(function(){function s(f){var g,b,S;this.options={},b=l["0.2"];for(g in b)c.call(b,g)&&(S=b[g],this.options[g]=S);for(g in f)c.call(f,g)&&(S=f[g],this.options[g]=S)}return s.prototype.buildObject=function(f){var g,b,S,A,x;return g=this.options.attrkey,b=this.options.charkey,Object.keys(f).length===1&&this.options.rootName===l["0.2"].rootName?(x=Object.keys(f)[0],f=f[x]):x=this.options.rootName,S=(function(M){return function(N,X){var B,C,_,E,U,j;if(typeof X!="object")M.options.cdata&&o(X)?N.raw(d(X)):N.txt(X);else if(Array.isArray(X)){for(E in X)if(c.call(X,E)){C=X[E];for(U in C)_=C[U],N=S(N.ele(U),_).up()}}else for(U in X)if(c.call(X,U))if(C=X[U],U===g){if(typeof C=="object")for(B in C)j=C[B],N=N.att(B,j)}else if(U===b)M.options.cdata&&o(C)?N=N.raw(d(C)):N=N.txt(C);else if(Array.isArray(C))for(E in C)c.call(C,E)&&(_=C[E],typeof _=="string"?M.options.cdata&&o(_)?N=N.ele(U).raw(d(_)).up():N=N.ele(U,_).up():N=S(N.ele(U),_).up());else typeof C=="object"?N=S(N.ele(U),C).up():typeof C=="string"&&M.options.cdata&&o(C)?N=N.ele(U).raw(d(C)).up():(C==null&&(C=""),N=N.ele(U,C.toString()).up());return N}})(this),A=u.create(x,this.options.xmldec,this.options.doctype,{headless:this.options.headless,allowSurrogateChars:this.options.allowSurrogateChars}),S(A,f).end(this.options.renderOpts)},s})()}).call(Sl)),Sl}var Dl={},zs={};const Vv={},Fv=Object.freeze(Object.defineProperty({__proto__:null,default:Vv},Symbol.toStringTag,{value:"Module"})),Zs=hg(Fv);var qs={},Al={exports:{}},Hs={},ea={},oy;function Gv(){if(oy)return ea;oy=1,ea.byteLength=f,ea.toByteArray=b,ea.fromByteArray=x;for(var u=[],l=[],h=typeof Uint8Array<"u"?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,c=o.length;d<c;++d)u[d]=o[d],l[o.charCodeAt(d)]=d;l[45]=62,l[95]=63;function s(M){var N=M.length;if(N%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var X=M.indexOf("=");X===-1&&(X=N);var B=X===N?0:4-X%4;return[X,B]}function f(M){var N=s(M),X=N[0],B=N[1];return(X+B)*3/4-B}function g(M,N,X){return(N+X)*3/4-X}function b(M){var N,X=s(M),B=X[0],C=X[1],_=new h(g(M,B,C)),E=0,U=C>0?B-4:B,j;for(j=0;j<U;j+=4)N=l[M.charCodeAt(j)]<<18|l[M.charCodeAt(j+1)]<<12|l[M.charCodeAt(j+2)]<<6|l[M.charCodeAt(j+3)],_[E++]=N>>16&255,_[E++]=N>>8&255,_[E++]=N&255;return C===2&&(N=l[M.charCodeAt(j)]<<2|l[M.charCodeAt(j+1)]>>4,_[E++]=N&255),C===1&&(N=l[M.charCodeAt(j)]<<10|l[M.charCodeAt(j+1)]<<4|l[M.charCodeAt(j+2)]>>2,_[E++]=N>>8&255,_[E++]=N&255),_}function S(M){return u[M>>18&63]+u[M>>12&63]+u[M>>6&63]+u[M&63]}function A(M,N,X){for(var B,C=[],_=N;_<X;_+=3)B=(M[_]<<16&16711680)+(M[_+1]<<8&65280)+(M[_+2]&255),C.push(S(B));return C.join("")}function x(M){for(var N,X=M.length,B=X%3,C=[],_=16383,E=0,U=X-B;E<U;E+=_)C.push(A(M,E,E+_>U?U:E+_));return B===1?(N=M[X-1],C.push(u[N>>2]+u[N<<4&63]+"==")):B===2&&(N=(M[X-2]<<8)+M[X-1],C.push(u[N>>10]+u[N>>4&63]+u[N<<2&63]+"=")),C.join("")}return ea}var Ol={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */var sy;function Iv(){return sy||(sy=1,Ol.read=function(u,l,h,o,d){var c,s,f=d*8-o-1,g=(1<<f)-1,b=g>>1,S=-7,A=h?d-1:0,x=h?-1:1,M=u[l+A];for(A+=x,c=M&(1<<-S)-1,M>>=-S,S+=f;S>0;c=c*256+u[l+A],A+=x,S-=8);for(s=c&(1<<-S)-1,c>>=-S,S+=o;S>0;s=s*256+u[l+A],A+=x,S-=8);if(c===0)c=1-b;else{if(c===g)return s?NaN:(M?-1:1)*(1/0);s=s+Math.pow(2,o),c=c-b}return(M?-1:1)*s*Math.pow(2,c-o)},Ol.write=function(u,l,h,o,d,c){var s,f,g,b=c*8-d-1,S=(1<<b)-1,A=S>>1,x=d===23?Math.pow(2,-24)-Math.pow(2,-77):0,M=o?0:c-1,N=o?1:-1,X=l<0||l===0&&1/l<0?1:0;for(l=Math.abs(l),isNaN(l)||l===1/0?(f=isNaN(l)?1:0,s=S):(s=Math.floor(Math.log(l)/Math.LN2),l*(g=Math.pow(2,-s))<1&&(s--,g*=2),s+A>=1?l+=x/g:l+=x*Math.pow(2,1-A),l*g>=2&&(s++,g/=2),s+A>=S?(f=0,s=S):s+A>=1?(f=(l*g-1)*Math.pow(2,d),s=s+A):(f=l*Math.pow(2,A-1)*Math.pow(2,d),s=0));d>=8;u[h+M]=f&255,M+=N,f/=256,d-=8);for(s=s<<d|f,b+=d;b>0;u[h+M]=s&255,M+=N,s/=256,b-=8);u[h+M-N]|=X*128}),Ol}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var cy;function Qv(){return cy||(cy=1,(function(u){var l=Gv(),h=Iv(),o=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;u.Buffer=f,u.SlowBuffer=_,u.INSPECT_MAX_BYTES=50;var d=**********;u.kMaxLength=d,f.TYPED_ARRAY_SUPPORT=c(),!f.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function c(){try{var w=new Uint8Array(1),p={foo:function(){return 42}};return Object.setPrototypeOf(p,Uint8Array.prototype),Object.setPrototypeOf(w,p),w.foo()===42}catch{return!1}}Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}});function s(w){if(w>d)throw new RangeError('The value "'+w+'" is invalid for option "size"');var p=new Uint8Array(w);return Object.setPrototypeOf(p,f.prototype),p}function f(w,p,y){if(typeof w=="number"){if(typeof p=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return A(w)}return g(w,p,y)}f.poolSize=8192;function g(w,p,y){if(typeof w=="string")return x(w,p);if(ArrayBuffer.isView(w))return N(w);if(w==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof w);if(Lt(w,ArrayBuffer)||w&&Lt(w.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Lt(w,SharedArrayBuffer)||w&&Lt(w.buffer,SharedArrayBuffer)))return X(w,p,y);if(typeof w=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var R=w.valueOf&&w.valueOf();if(R!=null&&R!==w)return f.from(R,p,y);var V=B(w);if(V)return V;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof w[Symbol.toPrimitive]=="function")return f.from(w[Symbol.toPrimitive]("string"),p,y);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof w)}f.from=function(w,p,y){return g(w,p,y)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array);function b(w){if(typeof w!="number")throw new TypeError('"size" argument must be of type number');if(w<0)throw new RangeError('The value "'+w+'" is invalid for option "size"')}function S(w,p,y){return b(w),w<=0?s(w):p!==void 0?typeof y=="string"?s(w).fill(p,y):s(w).fill(p):s(w)}f.alloc=function(w,p,y){return S(w,p,y)};function A(w){return b(w),s(w<0?0:C(w)|0)}f.allocUnsafe=function(w){return A(w)},f.allocUnsafeSlow=function(w){return A(w)};function x(w,p){if((typeof p!="string"||p==="")&&(p="utf8"),!f.isEncoding(p))throw new TypeError("Unknown encoding: "+p);var y=E(w,p)|0,R=s(y),V=R.write(w,p);return V!==y&&(R=R.slice(0,V)),R}function M(w){for(var p=w.length<0?0:C(w.length)|0,y=s(p),R=0;R<p;R+=1)y[R]=w[R]&255;return y}function N(w){if(Lt(w,Uint8Array)){var p=new Uint8Array(w);return X(p.buffer,p.byteOffset,p.byteLength)}return M(w)}function X(w,p,y){if(p<0||w.byteLength<p)throw new RangeError('"offset" is outside of buffer bounds');if(w.byteLength<p+(y||0))throw new RangeError('"length" is outside of buffer bounds');var R;return p===void 0&&y===void 0?R=new Uint8Array(w):y===void 0?R=new Uint8Array(w,p):R=new Uint8Array(w,p,y),Object.setPrototypeOf(R,f.prototype),R}function B(w){if(f.isBuffer(w)){var p=C(w.length)|0,y=s(p);return y.length===0||w.copy(y,0,0,p),y}if(w.length!==void 0)return typeof w.length!="number"||kt(w.length)?s(0):M(w);if(w.type==="Buffer"&&Array.isArray(w.data))return M(w.data)}function C(w){if(w>=d)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+d.toString(16)+" bytes");return w|0}function _(w){return+w!=w&&(w=0),f.alloc(+w)}f.isBuffer=function(p){return p!=null&&p._isBuffer===!0&&p!==f.prototype},f.compare=function(p,y){if(Lt(p,Uint8Array)&&(p=f.from(p,p.offset,p.byteLength)),Lt(y,Uint8Array)&&(y=f.from(y,y.offset,y.byteLength)),!f.isBuffer(p)||!f.isBuffer(y))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(p===y)return 0;for(var R=p.length,V=y.length,at=0,ot=Math.min(R,V);at<ot;++at)if(p[at]!==y[at]){R=p[at],V=y[at];break}return R<V?-1:V<R?1:0},f.isEncoding=function(p){switch(String(p).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(p,y){if(!Array.isArray(p))throw new TypeError('"list" argument must be an Array of Buffers');if(p.length===0)return f.alloc(0);var R;if(y===void 0)for(y=0,R=0;R<p.length;++R)y+=p[R].length;var V=f.allocUnsafe(y),at=0;for(R=0;R<p.length;++R){var ot=p[R];if(Lt(ot,Uint8Array))at+ot.length>V.length?f.from(ot).copy(V,at):Uint8Array.prototype.set.call(V,ot,at);else if(f.isBuffer(ot))ot.copy(V,at);else throw new TypeError('"list" argument must be an Array of Buffers');at+=ot.length}return V};function E(w,p){if(f.isBuffer(w))return w.length;if(ArrayBuffer.isView(w)||Lt(w,ArrayBuffer))return w.byteLength;if(typeof w!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof w);var y=w.length,R=arguments.length>2&&arguments[2]===!0;if(!R&&y===0)return 0;for(var V=!1;;)switch(p){case"ascii":case"latin1":case"binary":return y;case"utf8":case"utf-8":return T(w).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return y*2;case"hex":return y>>>1;case"base64":return xt(w).length;default:if(V)return R?-1:T(w).length;p=(""+p).toLowerCase(),V=!0}}f.byteLength=E;function U(w,p,y){var R=!1;if((p===void 0||p<0)&&(p=0),p>this.length||((y===void 0||y>this.length)&&(y=this.length),y<=0)||(y>>>=0,p>>>=0,y<=p))return"";for(w||(w="utf8");;)switch(w){case"hex":return Bt(this,p,y);case"utf8":case"utf-8":return bt(this,p,y);case"ascii":return rt(this,p,y);case"latin1":case"binary":return yt(this,p,y);case"base64":return Tt(this,p,y);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return z(this,p,y);default:if(R)throw new TypeError("Unknown encoding: "+w);w=(w+"").toLowerCase(),R=!0}}f.prototype._isBuffer=!0;function j(w,p,y){var R=w[p];w[p]=w[y],w[y]=R}f.prototype.swap16=function(){var p=this.length;if(p%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var y=0;y<p;y+=2)j(this,y,y+1);return this},f.prototype.swap32=function(){var p=this.length;if(p%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var y=0;y<p;y+=4)j(this,y,y+3),j(this,y+1,y+2);return this},f.prototype.swap64=function(){var p=this.length;if(p%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var y=0;y<p;y+=8)j(this,y,y+7),j(this,y+1,y+6),j(this,y+2,y+5),j(this,y+3,y+4);return this},f.prototype.toString=function(){var p=this.length;return p===0?"":arguments.length===0?bt(this,0,p):U.apply(this,arguments)},f.prototype.toLocaleString=f.prototype.toString,f.prototype.equals=function(p){if(!f.isBuffer(p))throw new TypeError("Argument must be a Buffer");return this===p?!0:f.compare(this,p)===0},f.prototype.inspect=function(){var p="",y=u.INSPECT_MAX_BYTES;return p=this.toString("hex",0,y).replace(/(.{2})/g,"$1 ").trim(),this.length>y&&(p+=" ... "),"<Buffer "+p+">"},o&&(f.prototype[o]=f.prototype.inspect),f.prototype.compare=function(p,y,R,V,at){if(Lt(p,Uint8Array)&&(p=f.from(p,p.offset,p.byteLength)),!f.isBuffer(p))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof p);if(y===void 0&&(y=0),R===void 0&&(R=p?p.length:0),V===void 0&&(V=0),at===void 0&&(at=this.length),y<0||R>p.length||V<0||at>this.length)throw new RangeError("out of range index");if(V>=at&&y>=R)return 0;if(V>=at)return-1;if(y>=R)return 1;if(y>>>=0,R>>>=0,V>>>=0,at>>>=0,this===p)return 0;for(var ot=at-V,Rt=R-y,zt=Math.min(ot,Rt),Qt=this.slice(V,at),Wt=p.slice(y,R),Ct=0;Ct<zt;++Ct)if(Qt[Ct]!==Wt[Ct]){ot=Qt[Ct],Rt=Wt[Ct];break}return ot<Rt?-1:Rt<ot?1:0};function W(w,p,y,R,V){if(w.length===0)return-1;if(typeof y=="string"?(R=y,y=0):y>**********?y=**********:y<-2147483648&&(y=-2147483648),y=+y,kt(y)&&(y=V?0:w.length-1),y<0&&(y=w.length+y),y>=w.length){if(V)return-1;y=w.length-1}else if(y<0)if(V)y=0;else return-1;if(typeof p=="string"&&(p=f.from(p,R)),f.isBuffer(p))return p.length===0?-1:ut(w,p,y,R,V);if(typeof p=="number")return p=p&255,typeof Uint8Array.prototype.indexOf=="function"?V?Uint8Array.prototype.indexOf.call(w,p,y):Uint8Array.prototype.lastIndexOf.call(w,p,y):ut(w,[p],y,R,V);throw new TypeError("val must be string, number or Buffer")}function ut(w,p,y,R,V){var at=1,ot=w.length,Rt=p.length;if(R!==void 0&&(R=String(R).toLowerCase(),R==="ucs2"||R==="ucs-2"||R==="utf16le"||R==="utf-16le")){if(w.length<2||p.length<2)return-1;at=2,ot/=2,Rt/=2,y/=2}function zt(ge,ua){return at===1?ge[ua]:ge.readUInt16BE(ua*at)}var Qt;if(V){var Wt=-1;for(Qt=y;Qt<ot;Qt++)if(zt(w,Qt)===zt(p,Wt===-1?0:Qt-Wt)){if(Wt===-1&&(Wt=Qt),Qt-Wt+1===Rt)return Wt*at}else Wt!==-1&&(Qt-=Qt-Wt),Wt=-1}else for(y+Rt>ot&&(y=ot-Rt),Qt=y;Qt>=0;Qt--){for(var Ct=!0,_e=0;_e<Rt;_e++)if(zt(w,Qt+_e)!==zt(p,_e)){Ct=!1;break}if(Ct)return Qt}return-1}f.prototype.includes=function(p,y,R){return this.indexOf(p,y,R)!==-1},f.prototype.indexOf=function(p,y,R){return W(this,p,y,R,!0)},f.prototype.lastIndexOf=function(p,y,R){return W(this,p,y,R,!1)};function $(w,p,y,R){y=Number(y)||0;var V=w.length-y;R?(R=Number(R),R>V&&(R=V)):R=V;var at=p.length;R>at/2&&(R=at/2);for(var ot=0;ot<R;++ot){var Rt=parseInt(p.substr(ot*2,2),16);if(kt(Rt))return ot;w[y+ot]=Rt}return ot}function v(w,p,y,R){return Yt(T(p,w.length-y),w,y,R)}function J(w,p,y,R){return Yt(tt(p),w,y,R)}function it(w,p,y,R){return Yt(xt(p),w,y,R)}function st(w,p,y,R){return Yt(Y(p,w.length-y),w,y,R)}f.prototype.write=function(p,y,R,V){if(y===void 0)V="utf8",R=this.length,y=0;else if(R===void 0&&typeof y=="string")V=y,R=this.length,y=0;else if(isFinite(y))y=y>>>0,isFinite(R)?(R=R>>>0,V===void 0&&(V="utf8")):(V=R,R=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var at=this.length-y;if((R===void 0||R>at)&&(R=at),p.length>0&&(R<0||y<0)||y>this.length)throw new RangeError("Attempt to write outside buffer bounds");V||(V="utf8");for(var ot=!1;;)switch(V){case"hex":return $(this,p,y,R);case"utf8":case"utf-8":return v(this,p,y,R);case"ascii":case"latin1":case"binary":return J(this,p,y,R);case"base64":return it(this,p,y,R);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return st(this,p,y,R);default:if(ot)throw new TypeError("Unknown encoding: "+V);V=(""+V).toLowerCase(),ot=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Tt(w,p,y){return p===0&&y===w.length?l.fromByteArray(w):l.fromByteArray(w.slice(p,y))}function bt(w,p,y){y=Math.min(w.length,y);for(var R=[],V=p;V<y;){var at=w[V],ot=null,Rt=at>239?4:at>223?3:at>191?2:1;if(V+Rt<=y){var zt,Qt,Wt,Ct;switch(Rt){case 1:at<128&&(ot=at);break;case 2:zt=w[V+1],(zt&192)===128&&(Ct=(at&31)<<6|zt&63,Ct>127&&(ot=Ct));break;case 3:zt=w[V+1],Qt=w[V+2],(zt&192)===128&&(Qt&192)===128&&(Ct=(at&15)<<12|(zt&63)<<6|Qt&63,Ct>2047&&(Ct<55296||Ct>57343)&&(ot=Ct));break;case 4:zt=w[V+1],Qt=w[V+2],Wt=w[V+3],(zt&192)===128&&(Qt&192)===128&&(Wt&192)===128&&(Ct=(at&15)<<18|(zt&63)<<12|(Qt&63)<<6|Wt&63,Ct>65535&&Ct<1114112&&(ot=Ct))}}ot===null?(ot=65533,Rt=1):ot>65535&&(ot-=65536,R.push(ot>>>10&1023|55296),ot=56320|ot&1023),R.push(ot),V+=Rt}return I(R)}var St=4096;function I(w){var p=w.length;if(p<=St)return String.fromCharCode.apply(String,w);for(var y="",R=0;R<p;)y+=String.fromCharCode.apply(String,w.slice(R,R+=St));return y}function rt(w,p,y){var R="";y=Math.min(w.length,y);for(var V=p;V<y;++V)R+=String.fromCharCode(w[V]&127);return R}function yt(w,p,y){var R="";y=Math.min(w.length,y);for(var V=p;V<y;++V)R+=String.fromCharCode(w[V]);return R}function Bt(w,p,y){var R=w.length;(!p||p<0)&&(p=0),(!y||y<0||y>R)&&(y=R);for(var V="",at=p;at<y;++at)V+=se[w[at]];return V}function z(w,p,y){for(var R=w.slice(p,y),V="",at=0;at<R.length-1;at+=2)V+=String.fromCharCode(R[at]+R[at+1]*256);return V}f.prototype.slice=function(p,y){var R=this.length;p=~~p,y=y===void 0?R:~~y,p<0?(p+=R,p<0&&(p=0)):p>R&&(p=R),y<0?(y+=R,y<0&&(y=0)):y>R&&(y=R),y<p&&(y=p);var V=this.subarray(p,y);return Object.setPrototypeOf(V,f.prototype),V};function P(w,p,y){if(w%1!==0||w<0)throw new RangeError("offset is not uint");if(w+p>y)throw new RangeError("Trying to access beyond buffer length")}f.prototype.readUintLE=f.prototype.readUIntLE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p],at=1,ot=0;++ot<y&&(at*=256);)V+=this[p+ot]*at;return V},f.prototype.readUintBE=f.prototype.readUIntBE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p+--y],at=1;y>0&&(at*=256);)V+=this[p+--y]*at;return V},f.prototype.readUint8=f.prototype.readUInt8=function(p,y){return p=p>>>0,y||P(p,1,this.length),this[p]},f.prototype.readUint16LE=f.prototype.readUInt16LE=function(p,y){return p=p>>>0,y||P(p,2,this.length),this[p]|this[p+1]<<8},f.prototype.readUint16BE=f.prototype.readUInt16BE=function(p,y){return p=p>>>0,y||P(p,2,this.length),this[p]<<8|this[p+1]},f.prototype.readUint32LE=f.prototype.readUInt32LE=function(p,y){return p=p>>>0,y||P(p,4,this.length),(this[p]|this[p+1]<<8|this[p+2]<<16)+this[p+3]*16777216},f.prototype.readUint32BE=f.prototype.readUInt32BE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]*16777216+(this[p+1]<<16|this[p+2]<<8|this[p+3])},f.prototype.readIntLE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=this[p],at=1,ot=0;++ot<y&&(at*=256);)V+=this[p+ot]*at;return at*=128,V>=at&&(V-=Math.pow(2,8*y)),V},f.prototype.readIntBE=function(p,y,R){p=p>>>0,y=y>>>0,R||P(p,y,this.length);for(var V=y,at=1,ot=this[p+--V];V>0&&(at*=256);)ot+=this[p+--V]*at;return at*=128,ot>=at&&(ot-=Math.pow(2,8*y)),ot},f.prototype.readInt8=function(p,y){return p=p>>>0,y||P(p,1,this.length),this[p]&128?(255-this[p]+1)*-1:this[p]},f.prototype.readInt16LE=function(p,y){p=p>>>0,y||P(p,2,this.length);var R=this[p]|this[p+1]<<8;return R&32768?R|4294901760:R},f.prototype.readInt16BE=function(p,y){p=p>>>0,y||P(p,2,this.length);var R=this[p+1]|this[p]<<8;return R&32768?R|4294901760:R},f.prototype.readInt32LE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]|this[p+1]<<8|this[p+2]<<16|this[p+3]<<24},f.prototype.readInt32BE=function(p,y){return p=p>>>0,y||P(p,4,this.length),this[p]<<24|this[p+1]<<16|this[p+2]<<8|this[p+3]},f.prototype.readFloatLE=function(p,y){return p=p>>>0,y||P(p,4,this.length),h.read(this,p,!0,23,4)},f.prototype.readFloatBE=function(p,y){return p=p>>>0,y||P(p,4,this.length),h.read(this,p,!1,23,4)},f.prototype.readDoubleLE=function(p,y){return p=p>>>0,y||P(p,8,this.length),h.read(this,p,!0,52,8)},f.prototype.readDoubleBE=function(p,y){return p=p>>>0,y||P(p,8,this.length),h.read(this,p,!1,52,8)};function ct(w,p,y,R,V,at){if(!f.isBuffer(w))throw new TypeError('"buffer" argument must be a Buffer instance');if(p>V||p<at)throw new RangeError('"value" argument is out of bounds');if(y+R>w.length)throw new RangeError("Index out of range")}f.prototype.writeUintLE=f.prototype.writeUIntLE=function(p,y,R,V){if(p=+p,y=y>>>0,R=R>>>0,!V){var at=Math.pow(2,8*R)-1;ct(this,p,y,R,at,0)}var ot=1,Rt=0;for(this[y]=p&255;++Rt<R&&(ot*=256);)this[y+Rt]=p/ot&255;return y+R},f.prototype.writeUintBE=f.prototype.writeUIntBE=function(p,y,R,V){if(p=+p,y=y>>>0,R=R>>>0,!V){var at=Math.pow(2,8*R)-1;ct(this,p,y,R,at,0)}var ot=R-1,Rt=1;for(this[y+ot]=p&255;--ot>=0&&(Rt*=256);)this[y+ot]=p/Rt&255;return y+R},f.prototype.writeUint8=f.prototype.writeUInt8=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,1,255,0),this[y]=p&255,y+1},f.prototype.writeUint16LE=f.prototype.writeUInt16LE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,2,65535,0),this[y]=p&255,this[y+1]=p>>>8,y+2},f.prototype.writeUint16BE=f.prototype.writeUInt16BE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,2,65535,0),this[y]=p>>>8,this[y+1]=p&255,y+2},f.prototype.writeUint32LE=f.prototype.writeUInt32LE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,4,4294967295,0),this[y+3]=p>>>24,this[y+2]=p>>>16,this[y+1]=p>>>8,this[y]=p&255,y+4},f.prototype.writeUint32BE=f.prototype.writeUInt32BE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,4,4294967295,0),this[y]=p>>>24,this[y+1]=p>>>16,this[y+2]=p>>>8,this[y+3]=p&255,y+4},f.prototype.writeIntLE=function(p,y,R,V){if(p=+p,y=y>>>0,!V){var at=Math.pow(2,8*R-1);ct(this,p,y,R,at-1,-at)}var ot=0,Rt=1,zt=0;for(this[y]=p&255;++ot<R&&(Rt*=256);)p<0&&zt===0&&this[y+ot-1]!==0&&(zt=1),this[y+ot]=(p/Rt>>0)-zt&255;return y+R},f.prototype.writeIntBE=function(p,y,R,V){if(p=+p,y=y>>>0,!V){var at=Math.pow(2,8*R-1);ct(this,p,y,R,at-1,-at)}var ot=R-1,Rt=1,zt=0;for(this[y+ot]=p&255;--ot>=0&&(Rt*=256);)p<0&&zt===0&&this[y+ot+1]!==0&&(zt=1),this[y+ot]=(p/Rt>>0)-zt&255;return y+R},f.prototype.writeInt8=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,1,127,-128),p<0&&(p=255+p+1),this[y]=p&255,y+1},f.prototype.writeInt16LE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,2,32767,-32768),this[y]=p&255,this[y+1]=p>>>8,y+2},f.prototype.writeInt16BE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,2,32767,-32768),this[y]=p>>>8,this[y+1]=p&255,y+2},f.prototype.writeInt32LE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,4,**********,-2147483648),this[y]=p&255,this[y+1]=p>>>8,this[y+2]=p>>>16,this[y+3]=p>>>24,y+4},f.prototype.writeInt32BE=function(p,y,R){return p=+p,y=y>>>0,R||ct(this,p,y,4,**********,-2147483648),p<0&&(p=4294967295+p+1),this[y]=p>>>24,this[y+1]=p>>>16,this[y+2]=p>>>8,this[y+3]=p&255,y+4};function ft(w,p,y,R,V,at){if(y+R>w.length)throw new RangeError("Index out of range");if(y<0)throw new RangeError("Index out of range")}function dt(w,p,y,R,V){return p=+p,y=y>>>0,V||ft(w,p,y,4),h.write(w,p,y,R,23,4),y+4}f.prototype.writeFloatLE=function(p,y,R){return dt(this,p,y,!0,R)},f.prototype.writeFloatBE=function(p,y,R){return dt(this,p,y,!1,R)};function wt(w,p,y,R,V){return p=+p,y=y>>>0,V||ft(w,p,y,8),h.write(w,p,y,R,52,8),y+8}f.prototype.writeDoubleLE=function(p,y,R){return wt(this,p,y,!0,R)},f.prototype.writeDoubleBE=function(p,y,R){return wt(this,p,y,!1,R)},f.prototype.copy=function(p,y,R,V){if(!f.isBuffer(p))throw new TypeError("argument should be a Buffer");if(R||(R=0),!V&&V!==0&&(V=this.length),y>=p.length&&(y=p.length),y||(y=0),V>0&&V<R&&(V=R),V===R||p.length===0||this.length===0)return 0;if(y<0)throw new RangeError("targetStart out of bounds");if(R<0||R>=this.length)throw new RangeError("Index out of range");if(V<0)throw new RangeError("sourceEnd out of bounds");V>this.length&&(V=this.length),p.length-y<V-R&&(V=p.length-y+R);var at=V-R;return this===p&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(y,R,V):Uint8Array.prototype.set.call(p,this.subarray(R,V),y),at},f.prototype.fill=function(p,y,R,V){if(typeof p=="string"){if(typeof y=="string"?(V=y,y=0,R=this.length):typeof R=="string"&&(V=R,R=this.length),V!==void 0&&typeof V!="string")throw new TypeError("encoding must be a string");if(typeof V=="string"&&!f.isEncoding(V))throw new TypeError("Unknown encoding: "+V);if(p.length===1){var at=p.charCodeAt(0);(V==="utf8"&&at<128||V==="latin1")&&(p=at)}}else typeof p=="number"?p=p&255:typeof p=="boolean"&&(p=Number(p));if(y<0||this.length<y||this.length<R)throw new RangeError("Out of range index");if(R<=y)return this;y=y>>>0,R=R===void 0?this.length:R>>>0,p||(p=0);var ot;if(typeof p=="number")for(ot=y;ot<R;++ot)this[ot]=p;else{var Rt=f.isBuffer(p)?p:f.from(p,V),zt=Rt.length;if(zt===0)throw new TypeError('The value "'+p+'" is invalid for argument "value"');for(ot=0;ot<R-y;++ot)this[ot+y]=Rt[ot%zt]}return this};var vt=/[^+/0-9A-Za-z-_]/g;function O(w){if(w=w.split("=")[0],w=w.trim().replace(vt,""),w.length<2)return"";for(;w.length%4!==0;)w=w+"=";return w}function T(w,p){p=p||1/0;for(var y,R=w.length,V=null,at=[],ot=0;ot<R;++ot){if(y=w.charCodeAt(ot),y>55295&&y<57344){if(!V){if(y>56319){(p-=3)>-1&&at.push(239,191,189);continue}else if(ot+1===R){(p-=3)>-1&&at.push(239,191,189);continue}V=y;continue}if(y<56320){(p-=3)>-1&&at.push(239,191,189),V=y;continue}y=(V-55296<<10|y-56320)+65536}else V&&(p-=3)>-1&&at.push(239,191,189);if(V=null,y<128){if((p-=1)<0)break;at.push(y)}else if(y<2048){if((p-=2)<0)break;at.push(y>>6|192,y&63|128)}else if(y<65536){if((p-=3)<0)break;at.push(y>>12|224,y>>6&63|128,y&63|128)}else if(y<1114112){if((p-=4)<0)break;at.push(y>>18|240,y>>12&63|128,y>>6&63|128,y&63|128)}else throw new Error("Invalid code point")}return at}function tt(w){for(var p=[],y=0;y<w.length;++y)p.push(w.charCodeAt(y)&255);return p}function Y(w,p){for(var y,R,V,at=[],ot=0;ot<w.length&&!((p-=2)<0);++ot)y=w.charCodeAt(ot),R=y>>8,V=y%256,at.push(V),at.push(R);return at}function xt(w){return l.toByteArray(O(w))}function Yt(w,p,y,R){for(var V=0;V<R&&!(V+y>=p.length||V>=w.length);++V)p[V+y]=w[V];return V}function Lt(w,p){return w instanceof p||w!=null&&w.constructor!=null&&w.constructor.name!=null&&w.constructor.name===p.name}function kt(w){return w!==w}var se=(function(){for(var w="0123456789abcdef",p=new Array(256),y=0;y<16;++y)for(var R=y*16,V=0;V<16;++V)p[R+V]=w[y]+w[V];return p})()})(Hs)),Hs}/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */var fy;function Zv(){return fy||(fy=1,(function(u,l){var h=Qv(),o=h.Buffer;function d(s,f){for(var g in s)f[g]=s[g]}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?u.exports=h:(d(h,l),l.Buffer=c);function c(s,f,g){return o(s,f,g)}c.prototype=Object.create(o.prototype),d(o,c),c.from=function(s,f,g){if(typeof s=="number")throw new TypeError("Argument must not be a number");return o(s,f,g)},c.alloc=function(s,f,g){if(typeof s!="number")throw new TypeError("Argument must be a number");var b=o(s);return f!==void 0?typeof g=="string"?b.fill(f,g):b.fill(f):b.fill(0),b},c.allocUnsafe=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return o(s)},c.allocUnsafeSlow=function(s){if(typeof s!="number")throw new TypeError("Argument must be a number");return h.SlowBuffer(s)}})(Al,Al.exports)),Al.exports}var hy;function Kv(){if(hy)return qs;hy=1;var u=Zv().Buffer,l=u.isEncoding||function(C){switch(C=""+C,C&&C.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function h(C){if(!C)return"utf8";for(var _;;)switch(C){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return C;default:if(_)return;C=(""+C).toLowerCase(),_=!0}}function o(C){var _=h(C);if(typeof _!="string"&&(u.isEncoding===l||!l(C)))throw new Error("Unknown encoding: "+C);return _||C}qs.StringDecoder=d;function d(C){this.encoding=o(C);var _;switch(this.encoding){case"utf16le":this.text=A,this.end=x,_=4;break;case"utf8":this.fillLast=g,_=4;break;case"base64":this.text=M,this.end=N,_=3;break;default:this.write=X,this.end=B;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=u.allocUnsafe(_)}d.prototype.write=function(C){if(C.length===0)return"";var _,E;if(this.lastNeed){if(_=this.fillLast(C),_===void 0)return"";E=this.lastNeed,this.lastNeed=0}else E=0;return E<C.length?_?_+this.text(C,E):this.text(C,E):_||""},d.prototype.end=S,d.prototype.text=b,d.prototype.fillLast=function(C){if(this.lastNeed<=C.length)return C.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);C.copy(this.lastChar,this.lastTotal-this.lastNeed,0,C.length),this.lastNeed-=C.length};function c(C){return C<=127?0:C>>5===6?2:C>>4===14?3:C>>3===30?4:C>>6===2?-1:-2}function s(C,_,E){var U=_.length-1;if(U<E)return 0;var j=c(_[U]);return j>=0?(j>0&&(C.lastNeed=j-1),j):--U<E||j===-2?0:(j=c(_[U]),j>=0?(j>0&&(C.lastNeed=j-2),j):--U<E||j===-2?0:(j=c(_[U]),j>=0?(j>0&&(j===2?j=0:C.lastNeed=j-3),j):0))}function f(C,_,E){if((_[0]&192)!==128)return C.lastNeed=0,"�";if(C.lastNeed>1&&_.length>1){if((_[1]&192)!==128)return C.lastNeed=1,"�";if(C.lastNeed>2&&_.length>2&&(_[2]&192)!==128)return C.lastNeed=2,"�"}}function g(C){var _=this.lastTotal-this.lastNeed,E=f(this,C);if(E!==void 0)return E;if(this.lastNeed<=C.length)return C.copy(this.lastChar,_,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);C.copy(this.lastChar,_,0,C.length),this.lastNeed-=C.length}function b(C,_){var E=s(this,C,_);if(!this.lastNeed)return C.toString("utf8",_);this.lastTotal=E;var U=C.length-(E-this.lastNeed);return C.copy(this.lastChar,0,U),C.toString("utf8",_,U)}function S(C){var _=C&&C.length?this.write(C):"";return this.lastNeed?_+"�":_}function A(C,_){if((C.length-_)%2===0){var E=C.toString("utf16le",_);if(E){var U=E.charCodeAt(E.length-1);if(U>=55296&&U<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=C[C.length-2],this.lastChar[1]=C[C.length-1],E.slice(0,-1)}return E}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=C[C.length-1],C.toString("utf16le",_,C.length-1)}function x(C){var _=C&&C.length?this.write(C):"";if(this.lastNeed){var E=this.lastTotal-this.lastNeed;return _+this.lastChar.toString("utf16le",0,E)}return _}function M(C,_){var E=(C.length-_)%3;return E===0?C.toString("base64",_):(this.lastNeed=3-E,this.lastTotal=3,E===1?this.lastChar[0]=C[C.length-1]:(this.lastChar[0]=C[C.length-2],this.lastChar[1]=C[C.length-1]),C.toString("base64",_,C.length-E))}function N(C){var _=C&&C.length?this.write(C):"";return this.lastNeed?_+this.lastChar.toString("base64",0,3-this.lastNeed):_}function X(C){return C.toString(this.encoding)}function B(C){return C&&C.length?this.write(C):""}return qs}var dy;function Pv(){return dy||(dy=1,(function(u){(function(l){l.parser=function(O,T){return new o(O,T)},l.SAXParser=o,l.SAXStream=S,l.createStream=b,l.MAX_BUFFER_LENGTH=64*1024;var h=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];l.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function o(O,T){if(!(this instanceof o))return new o(O,T);var tt=this;c(tt),tt.q=tt.c="",tt.bufferCheckPosition=l.MAX_BUFFER_LENGTH,tt.opt=T||{},tt.opt.lowercase=tt.opt.lowercase||tt.opt.lowercasetags,tt.looseCase=tt.opt.lowercase?"toLowerCase":"toUpperCase",tt.tags=[],tt.closed=tt.closedRoot=tt.sawRoot=!1,tt.tag=tt.error=null,tt.strict=!!O,tt.noscript=!!(O||tt.opt.noscript),tt.state=v.BEGIN,tt.strictEntities=tt.opt.strictEntities,tt.ENTITIES=tt.strictEntities?Object.create(l.XML_ENTITIES):Object.create(l.ENTITIES),tt.attribList=[],tt.opt.xmlns&&(tt.ns=Object.create(X)),tt.opt.unquotedAttributeValues===void 0&&(tt.opt.unquotedAttributeValues=!O),tt.trackPosition=tt.opt.position!==!1,tt.trackPosition&&(tt.position=tt.line=tt.column=0),it(tt,"onready")}Object.create||(Object.create=function(O){function T(){}T.prototype=O;var tt=new T;return tt}),Object.keys||(Object.keys=function(O){var T=[];for(var tt in O)O.hasOwnProperty(tt)&&T.push(tt);return T});function d(O){for(var T=Math.max(l.MAX_BUFFER_LENGTH,10),tt=0,Y=0,xt=h.length;Y<xt;Y++){var Yt=O[h[Y]].length;if(Yt>T)switch(h[Y]){case"textNode":Tt(O);break;case"cdata":st(O,"oncdata",O.cdata),O.cdata="";break;case"script":st(O,"onscript",O.script),O.script="";break;default:St(O,"Max buffer length exceeded: "+h[Y])}tt=Math.max(tt,Yt)}var Lt=l.MAX_BUFFER_LENGTH-tt;O.bufferCheckPosition=Lt+O.position}function c(O){for(var T=0,tt=h.length;T<tt;T++)O[h[T]]=""}function s(O){Tt(O),O.cdata!==""&&(st(O,"oncdata",O.cdata),O.cdata=""),O.script!==""&&(st(O,"onscript",O.script),O.script="")}o.prototype={end:function(){I(this)},write:vt,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){s(this)}};var f;try{f=Zs.Stream}catch{f=function(){}}f||(f=function(){});var g=l.EVENTS.filter(function(O){return O!=="error"&&O!=="end"});function b(O,T){return new S(O,T)}function S(O,T){if(!(this instanceof S))return new S(O,T);f.apply(this),this._parser=new o(O,T),this.writable=!0,this.readable=!0;var tt=this;this._parser.onend=function(){tt.emit("end")},this._parser.onerror=function(Y){tt.emit("error",Y),tt._parser.error=null},this._decoder=null,g.forEach(function(Y){Object.defineProperty(tt,"on"+Y,{get:function(){return tt._parser["on"+Y]},set:function(xt){if(!xt)return tt.removeAllListeners(Y),tt._parser["on"+Y]=xt,xt;tt.on(Y,xt)},enumerable:!0,configurable:!1})})}S.prototype=Object.create(f.prototype,{constructor:{value:S}}),S.prototype.write=function(O){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(O)){if(!this._decoder){var T=Kv().StringDecoder;this._decoder=new T("utf8")}O=this._decoder.write(O)}return this._parser.write(O.toString()),this.emit("data",O),!0},S.prototype.end=function(O){return O&&O.length&&this.write(O),this._parser.end(),!0},S.prototype.on=function(O,T){var tt=this;return!tt._parser["on"+O]&&g.indexOf(O)!==-1&&(tt._parser["on"+O]=function(){var Y=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);Y.splice(0,0,O),tt.emit.apply(tt,Y)}),f.prototype.on.call(tt,O,T)};var A="[CDATA[",x="DOCTYPE",M="http://www.w3.org/XML/1998/namespace",N="http://www.w3.org/2000/xmlns/",X={xml:M,xmlns:N},B=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,C=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,_=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,E=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function U(O){return O===" "||O===`
`||O==="\r"||O==="	"}function j(O){return O==='"'||O==="'"}function W(O){return O===">"||U(O)}function ut(O,T){return O.test(T)}function $(O,T){return!ut(O,T)}var v=0;l.STATE={BEGIN:v++,BEGIN_WHITESPACE:v++,TEXT:v++,TEXT_ENTITY:v++,OPEN_WAKA:v++,SGML_DECL:v++,SGML_DECL_QUOTED:v++,DOCTYPE:v++,DOCTYPE_QUOTED:v++,DOCTYPE_DTD:v++,DOCTYPE_DTD_QUOTED:v++,COMMENT_STARTING:v++,COMMENT:v++,COMMENT_ENDING:v++,COMMENT_ENDED:v++,CDATA:v++,CDATA_ENDING:v++,CDATA_ENDING_2:v++,PROC_INST:v++,PROC_INST_BODY:v++,PROC_INST_ENDING:v++,OPEN_TAG:v++,OPEN_TAG_SLASH:v++,ATTRIB:v++,ATTRIB_NAME:v++,ATTRIB_NAME_SAW_WHITE:v++,ATTRIB_VALUE:v++,ATTRIB_VALUE_QUOTED:v++,ATTRIB_VALUE_CLOSED:v++,ATTRIB_VALUE_UNQUOTED:v++,ATTRIB_VALUE_ENTITY_Q:v++,ATTRIB_VALUE_ENTITY_U:v++,CLOSE_TAG:v++,CLOSE_TAG_SAW_WHITE:v++,SCRIPT:v++,SCRIPT_ENDING:v++},l.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},l.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(l.ENTITIES).forEach(function(O){var T=l.ENTITIES[O],tt=typeof T=="number"?String.fromCharCode(T):T;l.ENTITIES[O]=tt});for(var J in l.STATE)l.STATE[l.STATE[J]]=J;v=l.STATE;function it(O,T,tt){O[T]&&O[T](tt)}function st(O,T,tt){O.textNode&&Tt(O),it(O,T,tt)}function Tt(O){O.textNode=bt(O.opt,O.textNode),O.textNode&&it(O,"ontext",O.textNode),O.textNode=""}function bt(O,T){return O.trim&&(T=T.trim()),O.normalize&&(T=T.replace(/\s+/g," ")),T}function St(O,T){return Tt(O),O.trackPosition&&(T+=`
Line: `+O.line+`
Column: `+O.column+`
Char: `+O.c),T=new Error(T),O.error=T,it(O,"onerror",T),O}function I(O){return O.sawRoot&&!O.closedRoot&&rt(O,"Unclosed root tag"),O.state!==v.BEGIN&&O.state!==v.BEGIN_WHITESPACE&&O.state!==v.TEXT&&St(O,"Unexpected end"),Tt(O),O.c="",O.closed=!0,it(O,"onend"),o.call(O,O.strict,O.opt),O}function rt(O,T){if(typeof O!="object"||!(O instanceof o))throw new Error("bad call to strictFail");O.strict&&St(O,T)}function yt(O){O.strict||(O.tagName=O.tagName[O.looseCase]());var T=O.tags[O.tags.length-1]||O,tt=O.tag={name:O.tagName,attributes:{}};O.opt.xmlns&&(tt.ns=T.ns),O.attribList.length=0,st(O,"onopentagstart",tt)}function Bt(O,T){var tt=O.indexOf(":"),Y=tt<0?["",O]:O.split(":"),xt=Y[0],Yt=Y[1];return T&&O==="xmlns"&&(xt="xmlns",Yt=""),{prefix:xt,local:Yt}}function z(O){if(O.strict||(O.attribName=O.attribName[O.looseCase]()),O.attribList.indexOf(O.attribName)!==-1||O.tag.attributes.hasOwnProperty(O.attribName)){O.attribName=O.attribValue="";return}if(O.opt.xmlns){var T=Bt(O.attribName,!0),tt=T.prefix,Y=T.local;if(tt==="xmlns")if(Y==="xml"&&O.attribValue!==M)rt(O,"xml: prefix must be bound to "+M+`
Actual: `+O.attribValue);else if(Y==="xmlns"&&O.attribValue!==N)rt(O,"xmlns: prefix must be bound to "+N+`
Actual: `+O.attribValue);else{var xt=O.tag,Yt=O.tags[O.tags.length-1]||O;xt.ns===Yt.ns&&(xt.ns=Object.create(Yt.ns)),xt.ns[Y]=O.attribValue}O.attribList.push([O.attribName,O.attribValue])}else O.tag.attributes[O.attribName]=O.attribValue,st(O,"onattribute",{name:O.attribName,value:O.attribValue});O.attribName=O.attribValue=""}function P(O,T){if(O.opt.xmlns){var tt=O.tag,Y=Bt(O.tagName);tt.prefix=Y.prefix,tt.local=Y.local,tt.uri=tt.ns[Y.prefix]||"",tt.prefix&&!tt.uri&&(rt(O,"Unbound namespace prefix: "+JSON.stringify(O.tagName)),tt.uri=Y.prefix);var xt=O.tags[O.tags.length-1]||O;tt.ns&&xt.ns!==tt.ns&&Object.keys(tt.ns).forEach(function(ot){st(O,"onopennamespace",{prefix:ot,uri:tt.ns[ot]})});for(var Yt=0,Lt=O.attribList.length;Yt<Lt;Yt++){var kt=O.attribList[Yt],se=kt[0],w=kt[1],p=Bt(se,!0),y=p.prefix,R=p.local,V=y===""?"":tt.ns[y]||"",at={name:se,value:w,prefix:y,local:R,uri:V};y&&y!=="xmlns"&&!V&&(rt(O,"Unbound namespace prefix: "+JSON.stringify(y)),at.uri=y),O.tag.attributes[se]=at,st(O,"onattribute",at)}O.attribList.length=0}O.tag.isSelfClosing=!!T,O.sawRoot=!0,O.tags.push(O.tag),st(O,"onopentag",O.tag),T||(!O.noscript&&O.tagName.toLowerCase()==="script"?O.state=v.SCRIPT:O.state=v.TEXT,O.tag=null,O.tagName=""),O.attribName=O.attribValue="",O.attribList.length=0}function ct(O){if(!O.tagName){rt(O,"Weird empty close tag."),O.textNode+="</>",O.state=v.TEXT;return}if(O.script){if(O.tagName!=="script"){O.script+="</"+O.tagName+">",O.tagName="",O.state=v.SCRIPT;return}st(O,"onscript",O.script),O.script=""}var T=O.tags.length,tt=O.tagName;O.strict||(tt=tt[O.looseCase]());for(var Y=tt;T--;){var xt=O.tags[T];if(xt.name!==Y)rt(O,"Unexpected close tag");else break}if(T<0){rt(O,"Unmatched closing tag: "+O.tagName),O.textNode+="</"+O.tagName+">",O.state=v.TEXT;return}O.tagName=tt;for(var Yt=O.tags.length;Yt-- >T;){var Lt=O.tag=O.tags.pop();O.tagName=O.tag.name,st(O,"onclosetag",O.tagName);var kt={};for(var se in Lt.ns)kt[se]=Lt.ns[se];var w=O.tags[O.tags.length-1]||O;O.opt.xmlns&&Lt.ns!==w.ns&&Object.keys(Lt.ns).forEach(function(p){var y=Lt.ns[p];st(O,"onclosenamespace",{prefix:p,uri:y})})}T===0&&(O.closedRoot=!0),O.tagName=O.attribValue=O.attribName="",O.attribList.length=0,O.state=v.TEXT}function ft(O){var T=O.entity,tt=T.toLowerCase(),Y,xt="";return O.ENTITIES[T]?O.ENTITIES[T]:O.ENTITIES[tt]?O.ENTITIES[tt]:(T=tt,T.charAt(0)==="#"&&(T.charAt(1)==="x"?(T=T.slice(2),Y=parseInt(T,16),xt=Y.toString(16)):(T=T.slice(1),Y=parseInt(T,10),xt=Y.toString(10))),T=T.replace(/^0+/,""),isNaN(Y)||xt.toLowerCase()!==T?(rt(O,"Invalid character entity"),"&"+O.entity+";"):String.fromCodePoint(Y))}function dt(O,T){T==="<"?(O.state=v.OPEN_WAKA,O.startTagPosition=O.position):U(T)||(rt(O,"Non-whitespace before first tag."),O.textNode=T,O.state=v.TEXT)}function wt(O,T){var tt="";return T<O.length&&(tt=O.charAt(T)),tt}function vt(O){var T=this;if(this.error)throw this.error;if(T.closed)return St(T,"Cannot write after close. Assign an onready handler.");if(O===null)return I(T);typeof O=="object"&&(O=O.toString());for(var tt=0,Y="";Y=wt(O,tt++),T.c=Y,!!Y;)switch(T.trackPosition&&(T.position++,Y===`
`?(T.line++,T.column=0):T.column++),T.state){case v.BEGIN:if(T.state=v.BEGIN_WHITESPACE,Y==="\uFEFF")continue;dt(T,Y);continue;case v.BEGIN_WHITESPACE:dt(T,Y);continue;case v.TEXT:if(T.sawRoot&&!T.closedRoot){for(var xt=tt-1;Y&&Y!=="<"&&Y!=="&";)Y=wt(O,tt++),Y&&T.trackPosition&&(T.position++,Y===`
`?(T.line++,T.column=0):T.column++);T.textNode+=O.substring(xt,tt-1)}Y==="<"&&!(T.sawRoot&&T.closedRoot&&!T.strict)?(T.state=v.OPEN_WAKA,T.startTagPosition=T.position):(!U(Y)&&(!T.sawRoot||T.closedRoot)&&rt(T,"Text data outside of root node."),Y==="&"?T.state=v.TEXT_ENTITY:T.textNode+=Y);continue;case v.SCRIPT:Y==="<"?T.state=v.SCRIPT_ENDING:T.script+=Y;continue;case v.SCRIPT_ENDING:Y==="/"?T.state=v.CLOSE_TAG:(T.script+="<"+Y,T.state=v.SCRIPT);continue;case v.OPEN_WAKA:if(Y==="!")T.state=v.SGML_DECL,T.sgmlDecl="";else if(!U(Y))if(ut(B,Y))T.state=v.OPEN_TAG,T.tagName=Y;else if(Y==="/")T.state=v.CLOSE_TAG,T.tagName="";else if(Y==="?")T.state=v.PROC_INST,T.procInstName=T.procInstBody="";else{if(rt(T,"Unencoded <"),T.startTagPosition+1<T.position){var Yt=T.position-T.startTagPosition;Y=new Array(Yt).join(" ")+Y}T.textNode+="<"+Y,T.state=v.TEXT}continue;case v.SGML_DECL:if(T.sgmlDecl+Y==="--"){T.state=v.COMMENT,T.comment="",T.sgmlDecl="";continue}T.doctype&&T.doctype!==!0&&T.sgmlDecl?(T.state=v.DOCTYPE_DTD,T.doctype+="<!"+T.sgmlDecl+Y,T.sgmlDecl=""):(T.sgmlDecl+Y).toUpperCase()===A?(st(T,"onopencdata"),T.state=v.CDATA,T.sgmlDecl="",T.cdata=""):(T.sgmlDecl+Y).toUpperCase()===x?(T.state=v.DOCTYPE,(T.doctype||T.sawRoot)&&rt(T,"Inappropriately located doctype declaration"),T.doctype="",T.sgmlDecl=""):Y===">"?(st(T,"onsgmldeclaration",T.sgmlDecl),T.sgmlDecl="",T.state=v.TEXT):(j(Y)&&(T.state=v.SGML_DECL_QUOTED),T.sgmlDecl+=Y);continue;case v.SGML_DECL_QUOTED:Y===T.q&&(T.state=v.SGML_DECL,T.q=""),T.sgmlDecl+=Y;continue;case v.DOCTYPE:Y===">"?(T.state=v.TEXT,st(T,"ondoctype",T.doctype),T.doctype=!0):(T.doctype+=Y,Y==="["?T.state=v.DOCTYPE_DTD:j(Y)&&(T.state=v.DOCTYPE_QUOTED,T.q=Y));continue;case v.DOCTYPE_QUOTED:T.doctype+=Y,Y===T.q&&(T.q="",T.state=v.DOCTYPE);continue;case v.DOCTYPE_DTD:Y==="]"?(T.doctype+=Y,T.state=v.DOCTYPE):Y==="<"?(T.state=v.OPEN_WAKA,T.startTagPosition=T.position):j(Y)?(T.doctype+=Y,T.state=v.DOCTYPE_DTD_QUOTED,T.q=Y):T.doctype+=Y;continue;case v.DOCTYPE_DTD_QUOTED:T.doctype+=Y,Y===T.q&&(T.state=v.DOCTYPE_DTD,T.q="");continue;case v.COMMENT:Y==="-"?T.state=v.COMMENT_ENDING:T.comment+=Y;continue;case v.COMMENT_ENDING:Y==="-"?(T.state=v.COMMENT_ENDED,T.comment=bt(T.opt,T.comment),T.comment&&st(T,"oncomment",T.comment),T.comment=""):(T.comment+="-"+Y,T.state=v.COMMENT);continue;case v.COMMENT_ENDED:Y!==">"?(rt(T,"Malformed comment"),T.comment+="--"+Y,T.state=v.COMMENT):T.doctype&&T.doctype!==!0?T.state=v.DOCTYPE_DTD:T.state=v.TEXT;continue;case v.CDATA:Y==="]"?T.state=v.CDATA_ENDING:T.cdata+=Y;continue;case v.CDATA_ENDING:Y==="]"?T.state=v.CDATA_ENDING_2:(T.cdata+="]"+Y,T.state=v.CDATA);continue;case v.CDATA_ENDING_2:Y===">"?(T.cdata&&st(T,"oncdata",T.cdata),st(T,"onclosecdata"),T.cdata="",T.state=v.TEXT):Y==="]"?T.cdata+="]":(T.cdata+="]]"+Y,T.state=v.CDATA);continue;case v.PROC_INST:Y==="?"?T.state=v.PROC_INST_ENDING:U(Y)?T.state=v.PROC_INST_BODY:T.procInstName+=Y;continue;case v.PROC_INST_BODY:if(!T.procInstBody&&U(Y))continue;Y==="?"?T.state=v.PROC_INST_ENDING:T.procInstBody+=Y;continue;case v.PROC_INST_ENDING:Y===">"?(st(T,"onprocessinginstruction",{name:T.procInstName,body:T.procInstBody}),T.procInstName=T.procInstBody="",T.state=v.TEXT):(T.procInstBody+="?"+Y,T.state=v.PROC_INST_BODY);continue;case v.OPEN_TAG:ut(C,Y)?T.tagName+=Y:(yt(T),Y===">"?P(T):Y==="/"?T.state=v.OPEN_TAG_SLASH:(U(Y)||rt(T,"Invalid character in tag name"),T.state=v.ATTRIB));continue;case v.OPEN_TAG_SLASH:Y===">"?(P(T,!0),ct(T)):(rt(T,"Forward-slash in opening tag not followed by >"),T.state=v.ATTRIB);continue;case v.ATTRIB:if(U(Y))continue;Y===">"?P(T):Y==="/"?T.state=v.OPEN_TAG_SLASH:ut(B,Y)?(T.attribName=Y,T.attribValue="",T.state=v.ATTRIB_NAME):rt(T,"Invalid attribute name");continue;case v.ATTRIB_NAME:Y==="="?T.state=v.ATTRIB_VALUE:Y===">"?(rt(T,"Attribute without value"),T.attribValue=T.attribName,z(T),P(T)):U(Y)?T.state=v.ATTRIB_NAME_SAW_WHITE:ut(C,Y)?T.attribName+=Y:rt(T,"Invalid attribute name");continue;case v.ATTRIB_NAME_SAW_WHITE:if(Y==="=")T.state=v.ATTRIB_VALUE;else{if(U(Y))continue;rt(T,"Attribute without value"),T.tag.attributes[T.attribName]="",T.attribValue="",st(T,"onattribute",{name:T.attribName,value:""}),T.attribName="",Y===">"?P(T):ut(B,Y)?(T.attribName=Y,T.state=v.ATTRIB_NAME):(rt(T,"Invalid attribute name"),T.state=v.ATTRIB)}continue;case v.ATTRIB_VALUE:if(U(Y))continue;j(Y)?(T.q=Y,T.state=v.ATTRIB_VALUE_QUOTED):(T.opt.unquotedAttributeValues||St(T,"Unquoted attribute value"),T.state=v.ATTRIB_VALUE_UNQUOTED,T.attribValue=Y);continue;case v.ATTRIB_VALUE_QUOTED:if(Y!==T.q){Y==="&"?T.state=v.ATTRIB_VALUE_ENTITY_Q:T.attribValue+=Y;continue}z(T),T.q="",T.state=v.ATTRIB_VALUE_CLOSED;continue;case v.ATTRIB_VALUE_CLOSED:U(Y)?T.state=v.ATTRIB:Y===">"?P(T):Y==="/"?T.state=v.OPEN_TAG_SLASH:ut(B,Y)?(rt(T,"No whitespace between attributes"),T.attribName=Y,T.attribValue="",T.state=v.ATTRIB_NAME):rt(T,"Invalid attribute name");continue;case v.ATTRIB_VALUE_UNQUOTED:if(!W(Y)){Y==="&"?T.state=v.ATTRIB_VALUE_ENTITY_U:T.attribValue+=Y;continue}z(T),Y===">"?P(T):T.state=v.ATTRIB;continue;case v.CLOSE_TAG:if(T.tagName)Y===">"?ct(T):ut(C,Y)?T.tagName+=Y:T.script?(T.script+="</"+T.tagName,T.tagName="",T.state=v.SCRIPT):(U(Y)||rt(T,"Invalid tagname in closing tag"),T.state=v.CLOSE_TAG_SAW_WHITE);else{if(U(Y))continue;$(B,Y)?T.script?(T.script+="</"+Y,T.state=v.SCRIPT):rt(T,"Invalid tagname in closing tag."):T.tagName=Y}continue;case v.CLOSE_TAG_SAW_WHITE:if(U(Y))continue;Y===">"?ct(T):rt(T,"Invalid characters in closing tag");continue;case v.TEXT_ENTITY:case v.ATTRIB_VALUE_ENTITY_Q:case v.ATTRIB_VALUE_ENTITY_U:var Lt,kt;switch(T.state){case v.TEXT_ENTITY:Lt=v.TEXT,kt="textNode";break;case v.ATTRIB_VALUE_ENTITY_Q:Lt=v.ATTRIB_VALUE_QUOTED,kt="attribValue";break;case v.ATTRIB_VALUE_ENTITY_U:Lt=v.ATTRIB_VALUE_UNQUOTED,kt="attribValue";break}if(Y===";"){var se=ft(T);T.opt.unparsedEntities&&!Object.values(l.XML_ENTITIES).includes(se)?(T.entity="",T.state=Lt,T.write(se)):(T[kt]+=se,T.entity="",T.state=Lt)}else ut(T.entity.length?E:_,Y)?T.entity+=Y:(rt(T,"Invalid character in entity name"),T[kt]+="&"+T.entity+Y,T.entity="",T.state=Lt);continue;default:throw new Error(T,"Unknown state: "+T.state)}return T.position>=T.bufferCheckPosition&&d(T),T}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||(function(){var O=String.fromCharCode,T=Math.floor,tt=function(){var Y=16384,xt=[],Yt,Lt,kt=-1,se=arguments.length;if(!se)return"";for(var w="";++kt<se;){var p=Number(arguments[kt]);if(!isFinite(p)||p<0||p>1114111||T(p)!==p)throw RangeError("Invalid code point: "+p);p<=65535?xt.push(p):(p-=65536,Yt=(p>>10)+55296,Lt=p%1024+56320,xt.push(Yt,Lt)),(kt+1===se||xt.length>Y)&&(w+=O.apply(null,xt),xt.length=0)}return w};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:tt,configurable:!0,writable:!0}):String.fromCodePoint=tt})()})(u)})(zs)),zs}var wl={},py;function Jv(){return py||(py=1,(function(){wl.stripBOM=function(u){return u[0]==="\uFEFF"?u.substring(1):u}}).call(wl)),wl}var Zn={},yy;function Jy(){return yy||(yy=1,(function(){var u;u=new RegExp(/(?!xmlns)^.*:/),Zn.normalize=function(l){return l.toLowerCase()},Zn.firstCharLowerCase=function(l){return l.charAt(0).toLowerCase()+l.slice(1)},Zn.stripPrefix=function(l){return l.replace(u,"")},Zn.parseNumbers=function(l){return isNaN(l)||(l=l%1===0?parseInt(l,10):parseFloat(l)),l},Zn.parseBooleans=function(l){return/^(?:true|false)$/i.test(l)&&(l=l.toLowerCase()==="true"),l}}).call(Zn)),Zn}var my;function kv(){return my||(my=1,(function(u){(function(){var l,h,o,d,c,s,f,g,b,S=function(M,N){return function(){return M.apply(N,arguments)}},A=function(M,N){for(var X in N)x.call(N,X)&&(M[X]=N[X]);function B(){this.constructor=M}return B.prototype=N.prototype,M.prototype=new B,M.__super__=N.prototype,M},x={}.hasOwnProperty;g=Pv(),d=Zs,l=Jv(),f=Jy(),b=Zs.setImmediate,h=$s().defaults,c=function(M){return typeof M=="object"&&M!=null&&Object.keys(M).length===0},s=function(M,N,X){var B,C,_;for(B=0,C=M.length;B<C;B++)_=M[B],N=_(N,X);return N},o=function(M,N,X){var B;return B=Object.create(null),B.value=X,B.writable=!0,B.enumerable=!0,B.configurable=!0,Object.defineProperty(M,N,B)},u.Parser=(function(M){A(N,M);function N(X){this.parseStringPromise=S(this.parseStringPromise,this),this.parseString=S(this.parseString,this),this.reset=S(this.reset,this),this.assignOrPush=S(this.assignOrPush,this),this.processAsync=S(this.processAsync,this);var B,C,_;if(!(this instanceof u.Parser))return new u.Parser(X);this.options={},C=h["0.2"];for(B in C)x.call(C,B)&&(_=C[B],this.options[B]=_);for(B in X)x.call(X,B)&&(_=X[B],this.options[B]=_);this.options.xmlns&&(this.options.xmlnskey=this.options.attrkey+"ns"),this.options.normalizeTags&&(this.options.tagNameProcessors||(this.options.tagNameProcessors=[]),this.options.tagNameProcessors.unshift(f.normalize)),this.reset()}return N.prototype.processAsync=function(){var X,B;try{return this.remaining.length<=this.options.chunkSize?(X=this.remaining,this.remaining="",this.saxParser=this.saxParser.write(X),this.saxParser.close()):(X=this.remaining.substr(0,this.options.chunkSize),this.remaining=this.remaining.substr(this.options.chunkSize,this.remaining.length),this.saxParser=this.saxParser.write(X),b(this.processAsync))}catch(C){if(B=C,!this.saxParser.errThrown)return this.saxParser.errThrown=!0,this.emit(B)}},N.prototype.assignOrPush=function(X,B,C){return B in X?(X[B]instanceof Array||o(X,B,[X[B]]),X[B].push(C)):this.options.explicitArray?o(X,B,[C]):o(X,B,C)},N.prototype.reset=function(){var X,B,C,_;return this.removeAllListeners(),this.saxParser=g.parser(this.options.strict,{trim:!1,normalize:!1,xmlns:this.options.xmlns}),this.saxParser.errThrown=!1,this.saxParser.onerror=(function(E){return function(U){if(E.saxParser.resume(),!E.saxParser.errThrown)return E.saxParser.errThrown=!0,E.emit("error",U)}})(this),this.saxParser.onend=(function(E){return function(){if(!E.saxParser.ended)return E.saxParser.ended=!0,E.emit("end",E.resultObject)}})(this),this.saxParser.ended=!1,this.EXPLICIT_CHARKEY=this.options.explicitCharkey,this.resultObject=null,_=[],X=this.options.attrkey,B=this.options.charkey,this.saxParser.onopentag=(function(E){return function(U){var j,W,ut,$,v;if(ut={},ut[B]="",!E.options.ignoreAttrs){v=U.attributes;for(j in v)x.call(v,j)&&(!(X in ut)&&!E.options.mergeAttrs&&(ut[X]={}),W=E.options.attrValueProcessors?s(E.options.attrValueProcessors,U.attributes[j],j):U.attributes[j],$=E.options.attrNameProcessors?s(E.options.attrNameProcessors,j):j,E.options.mergeAttrs?E.assignOrPush(ut,$,W):o(ut[X],$,W))}return ut["#name"]=E.options.tagNameProcessors?s(E.options.tagNameProcessors,U.name):U.name,E.options.xmlns&&(ut[E.options.xmlnskey]={uri:U.uri,local:U.local}),_.push(ut)}})(this),this.saxParser.onclosetag=(function(E){return function(){var U,j,W,ut,$,v,J,it,st,Tt;if(v=_.pop(),$=v["#name"],(!E.options.explicitChildren||!E.options.preserveChildrenOrder)&&delete v["#name"],v.cdata===!0&&(U=v.cdata,delete v.cdata),st=_[_.length-1],v[B].match(/^\s*$/)&&!U?(j=v[B],delete v[B]):(E.options.trim&&(v[B]=v[B].trim()),E.options.normalize&&(v[B]=v[B].replace(/\s{2,}/g," ").trim()),v[B]=E.options.valueProcessors?s(E.options.valueProcessors,v[B],$):v[B],Object.keys(v).length===1&&B in v&&!E.EXPLICIT_CHARKEY&&(v=v[B])),c(v)&&(typeof E.options.emptyTag=="function"?v=E.options.emptyTag():v=E.options.emptyTag!==""?E.options.emptyTag:j),E.options.validator!=null&&(Tt="/"+(function(){var bt,St,I;for(I=[],bt=0,St=_.length;bt<St;bt++)ut=_[bt],I.push(ut["#name"]);return I})().concat($).join("/"),(function(){var bt;try{return v=E.options.validator(Tt,st&&st[$],v)}catch(St){return bt=St,E.emit("error",bt)}})()),E.options.explicitChildren&&!E.options.mergeAttrs&&typeof v=="object"){if(!E.options.preserveChildrenOrder)ut={},E.options.attrkey in v&&(ut[E.options.attrkey]=v[E.options.attrkey],delete v[E.options.attrkey]),!E.options.charsAsChildren&&E.options.charkey in v&&(ut[E.options.charkey]=v[E.options.charkey],delete v[E.options.charkey]),Object.getOwnPropertyNames(v).length>0&&(ut[E.options.childkey]=v),v=ut;else if(st){st[E.options.childkey]=st[E.options.childkey]||[],J={};for(W in v)x.call(v,W)&&o(J,W,v[W]);st[E.options.childkey].push(J),delete v["#name"],Object.keys(v).length===1&&B in v&&!E.EXPLICIT_CHARKEY&&(v=v[B])}}return _.length>0?E.assignOrPush(st,$,v):(E.options.explicitRoot&&(it=v,v={},o(v,$,it)),E.resultObject=v,E.saxParser.ended=!0,E.emit("end",E.resultObject))}})(this),C=(function(E){return function(U){var j,W;if(W=_[_.length-1],W)return W[B]+=U,E.options.explicitChildren&&E.options.preserveChildrenOrder&&E.options.charsAsChildren&&(E.options.includeWhiteChars||U.replace(/\\n/g,"").trim()!=="")&&(W[E.options.childkey]=W[E.options.childkey]||[],j={"#name":"__text__"},j[B]=U,E.options.normalize&&(j[B]=j[B].replace(/\s{2,}/g," ").trim()),W[E.options.childkey].push(j)),W}})(this),this.saxParser.ontext=C,this.saxParser.oncdata=(function(E){return function(U){var j;if(j=C(U),j)return j.cdata=!0}})()},N.prototype.parseString=function(X,B){var C;B!=null&&typeof B=="function"&&(this.on("end",function(_){return this.reset(),B(null,_)}),this.on("error",function(_){return this.reset(),B(_)}));try{return X=X.toString(),X.trim()===""?(this.emit("end",null),!0):(X=l.stripBOM(X),this.options.async?(this.remaining=X,b(this.processAsync),this.saxParser):this.saxParser.write(X).close())}catch(_){if(C=_,this.saxParser.errThrown||this.saxParser.ended){if(this.saxParser.ended)throw C}else return this.emit("error",C),this.saxParser.errThrown=!0}},N.prototype.parseStringPromise=function(X){return new Promise((function(B){return function(C,_){return B.parseString(X,function(E,U){return E?_(E):C(U)})}})(this))},N})(d),u.parseString=function(M,N,X){var B,C,_;return X!=null?(typeof X=="function"&&(B=X),typeof N=="object"&&(C=N)):(typeof N=="function"&&(B=N),C={}),_=new u.Parser(C),_.parseString(M,B)},u.parseStringPromise=function(M,N){var X,B;return typeof N=="object"&&(X=N),B=new u.Parser(X),B.parseStringPromise(M)}}).call(Dl)})(Dl)),Dl}var gy;function Wv(){return gy||(gy=1,(function(){var u,l,h,o,d=function(s,f){for(var g in f)c.call(f,g)&&(s[g]=f[g]);function b(){this.constructor=s}return b.prototype=f.prototype,s.prototype=new b,s.__super__=f.prototype,s},c={}.hasOwnProperty;l=$s(),u=Yv(),h=kv(),o=Jy(),an.defaults=l.defaults,an.processors=o,an.ValidationError=(function(s){d(f,s);function f(g){this.message=g}return f})(Error),an.Builder=u.Builder,an.Parser=h.Parser,an.parseString=h.parseString,an.parseStringPromise=h.parseStringPromise}).call(an)),an}var $v=Wv();class tE{constructor(l="localhost",h=9e3){this.updateConnection(l,h)}updateConnection(l,h){this.host=l,this.port=h,this.baseURL=`http://${l}:${h}`}async sendXMLRequest(l){try{return(await ee.post(this.baseURL,l,{headers:{"Content-Type":"application/xml"},timeout:1e4})).data}catch(h){throw new Error(`Tally request failed: ${h.message}`)}}parseXMLResponse(l){return new Promise((h,o)=>{$v.parseString(l,{explicitArray:!1},(d,c)=>{d?o(new Error(`XML parsing failed: ${d.message}`)):h(c)})})}async testConnection(){const l=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{return{success:!0,data:await this.sendXMLRequest(l)}}catch(h){return{success:!1,error:h.message}}}async getCompanyList(){const l=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{const h=await this.sendXMLRequest(l),o=await this.parseXMLResponse(h);return{success:!0,companies:this.extractCompanyData(o)}}catch(h){return{success:!1,error:h.message}}}extractCompanyData(l){try{const h=[];if(l&&l.ENVELOPE&&l.ENVELOPE.BODY){const o=l.ENVELOPE.BODY;if(o.IMPORTDATA&&o.IMPORTDATA.REQUESTDATA){const d=o.IMPORTDATA.REQUESTDATA;d.TALLYMESSAGE&&(Array.isArray(d.TALLYMESSAGE)?d.TALLYMESSAGE:[d.TALLYMESSAGE]).forEach(s=>{s.COMPANY&&(Array.isArray(s.COMPANY)?s.COMPANY:[s.COMPANY]).forEach(g=>{h.push({name:g.NAME||"Unknown Company",guid:g.GUID||"",remoteid:g.REMOTEID||"",alterid:g.ALTERID||"",masterid:g.MASTERID||""})})})}if(o.DATA&&o.DATA.COLLECTION){const d=o.DATA.COLLECTION;d.COMPANY&&(Array.isArray(d.COMPANY)?d.COMPANY:[d.COMPANY]).forEach(s=>{h.push({name:s.NAME||s.$||"Unknown Company",guid:s.GUID||"",remoteid:s.REMOTEID||"",alterid:s.ALTERID||"",masterid:s.MASTERID||""})})}}return h}catch(h){return console.error("Error extracting company data:",h),[]}}async getCompanyDetails(l){const h=`
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>Company</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                <SVCOMPANY>${l}</SVCOMPANY>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;try{const o=await this.sendXMLRequest(h);return{success:!0,data:await this.parseXMLResponse(o)}}catch(o){return{success:!1,error:o.message}}}}const eE=({tallyConnected:u})=>{const{tallySettings:l}=Ws(),[h,o]=Kt.useState([]),[d,c]=Kt.useState(!1),[s,f]=Kt.useState(null),[g,b]=Kt.useState(null),[S,A]=Kt.useState(null),x=new tE(l.host,l.port),M=async()=>{if(!u){f("Tally Prime is not connected");return}c(!0),f(null);try{const B=await x.getCompanyList();B.success?(o(B.companies),b(new Date),f(null)):(f(B.error),o([]))}catch(B){f(`Failed to fetch companies: ${B.message}`),o([])}finally{c(!1)}};Kt.useEffect(()=>{x.updateConnection(l.host,l.port),u?M():(o([]),f(null))},[u,l.host,l.port]);const N=async B=>{A(B)},X=()=>{M()};return u?lt.jsxs("div",{className:"company-list-container",children:[lt.jsxs("div",{className:"company-list-header",children:[lt.jsx("h3",{children:"Company List"}),lt.jsxs("button",{onClick:X,disabled:d,className:"refresh-button",children:[d?"🔄":"↻"," Refresh"]})]}),s&&lt.jsx("div",{className:"error-message",children:lt.jsxs("p",{children:["❌ ",s]})}),d&&lt.jsx("div",{className:"loading-message",children:lt.jsx("p",{children:"🔄 Loading companies..."})}),g&&lt.jsxs("div",{className:"last-fetched",children:["Last updated: ",g.toLocaleString()]}),h.length>0&&lt.jsxs("div",{className:"company-list",children:[lt.jsxs("div",{className:"company-count",children:["Found ",h.length," companies"]}),lt.jsxs("div",{className:"company-table",children:[lt.jsxs("div",{className:"company-table-header",children:[lt.jsx("div",{className:"company-header-cell",children:"Company Name"}),lt.jsx("div",{className:"company-header-cell",children:"GUID"}),lt.jsx("div",{className:"company-header-cell",children:"Actions"})]}),h.map((B,C)=>lt.jsxs("div",{className:`company-row ${S?.name===B.name?"selected":""}`,onClick:()=>N(B),children:[lt.jsx("div",{className:"company-cell company-name",children:B.name}),lt.jsx("div",{className:"company-cell company-guid",children:B.guid||"N/A"}),lt.jsx("div",{className:"company-cell company-actions",children:lt.jsx("button",{className:"select-button",onClick:_=>{_.stopPropagation(),N(B)},children:"Select"})})]},B.guid||C))]})]}),h.length===0&&!d&&!s&&u&&lt.jsx("div",{className:"no-companies-message",children:lt.jsx("p",{children:"No companies found. Make sure Tally Prime has companies configured."})}),S&&lt.jsxs("div",{className:"selected-company-details",children:[lt.jsx("h4",{children:"Selected Company Details"}),lt.jsxs("div",{className:"company-details",children:[lt.jsxs("p",{children:[lt.jsx("strong",{children:"Name:"})," ",S.name]}),S.guid&&lt.jsxs("p",{children:[lt.jsx("strong",{children:"GUID:"})," ",S.guid]}),S.remoteid&&lt.jsxs("p",{children:[lt.jsx("strong",{children:"Remote ID:"})," ",S.remoteid]}),S.alterid&&lt.jsxs("p",{children:[lt.jsx("strong",{children:"Alter ID:"})," ",S.alterid]}),S.masterid&&lt.jsxs("p",{children:[lt.jsx("strong",{children:"Master ID:"})," ",S.masterid]})]})]})]}):lt.jsxs("div",{className:"company-list-container",children:[lt.jsx("div",{className:"company-list-header",children:lt.jsx("h3",{children:"Company List"})}),lt.jsx("div",{className:"company-list-message",children:lt.jsx("p",{children:"Please ensure Tally Prime is connected to view companies."})})]})},nE=({isOpen:u,onClose:l,onSettingsChange:h,currentSettings:o})=>{const[d,c]=Kt.useState(o?.host||"localhost"),[s,f]=Kt.useState(o?.port||9e3),[g,b]=Kt.useState({});Kt.useEffect(()=>{o&&(c(o.host),f(o.port))},[o]);const S=()=>{const N={};d.trim()?/^[a-zA-Z0-9.-]+$/.test(d.trim())||(N.host="Invalid host format"):N.host="Host is required";const X=parseInt(s);return!s||isNaN(X)?N.port="Port must be a number":(X<1||X>65535)&&(N.port="Port must be between 1 and 65535"),b(N),Object.keys(N).length===0},A=()=>{if(S()){const N={host:d.trim(),port:parseInt(s)};localStorage.setItem("tallySettings",JSON.stringify(N)),h(N),l()}},x=()=>{const N={host:"localhost",port:9e3};c(N.host),f(N.port),b({})},M=async()=>{if(S())try{const N=`http://${d.trim()}:${s}`,X=await fetch(N,{method:"POST",headers:{"Content-Type":"application/xml"},body:"<ENVELOPE><HEADER><TALLYREQUEST>Export Data</TALLYREQUEST></HEADER></ENVELOPE>",signal:AbortSignal.timeout(5e3)});X.ok?alert("✅ Connection successful!"):alert(`❌ Connection failed: HTTP ${X.status}`)}catch(N){alert(`❌ Connection failed: ${N.message}`)}};return u?lt.jsx("div",{className:"settings-overlay",children:lt.jsxs("div",{className:"settings-modal",children:[lt.jsxs("div",{className:"settings-header",children:[lt.jsx("h2",{children:"Tally Prime Settings"}),lt.jsx("button",{className:"close-button",onClick:l,children:"×"})]}),lt.jsxs("div",{className:"settings-content",children:[lt.jsxs("div",{className:"settings-section",children:[lt.jsx("h3",{children:"Connection Settings"}),lt.jsx("p",{className:"settings-description",children:"Configure the host and port for connecting to Tally Prime. Make sure Tally Prime is running with Gateway enabled."})]}),lt.jsxs("div",{className:"form-group",children:[lt.jsx("label",{htmlFor:"host",children:"Host:"}),lt.jsx("input",{type:"text",id:"host",value:d,onChange:N=>c(N.target.value),className:g.host?"error":"",placeholder:"localhost"}),g.host&&lt.jsx("span",{className:"error-text",children:g.host})]}),lt.jsxs("div",{className:"form-group",children:[lt.jsx("label",{htmlFor:"port",children:"Port:"}),lt.jsx("input",{type:"number",id:"port",value:s,onChange:N=>f(N.target.value),className:g.port?"error":"",placeholder:"9000",min:"1",max:"65535"}),g.port&&lt.jsx("span",{className:"error-text",children:g.port})]}),lt.jsxs("div",{className:"current-url",children:[lt.jsx("strong",{children:"Current URL:"})," http://",d,":",s]})]}),lt.jsxs("div",{className:"settings-actions",children:[lt.jsx("button",{className:"test-button",onClick:M,disabled:Object.keys(g).length>0,children:"🔍 Test Connection"}),lt.jsx("button",{className:"reset-button",onClick:x,children:"🔄 Reset to Default"}),lt.jsxs("div",{className:"action-buttons",children:[lt.jsx("button",{className:"cancel-button",onClick:l,children:"Cancel"}),lt.jsx("button",{className:"save-button",onClick:A,disabled:Object.keys(g).length>0,children:"Save Settings"})]})]})]})}):null};function iE(){const{tallySettings:u,updateTallySettings:l}=Ws(),[h,o]=Kt.useState(!1),[d,c]=Kt.useState(!1),s=g=>{o(g)},f=g=>{l(g)};return lt.jsxs("div",{className:"app",children:[lt.jsx("header",{className:"app-header",children:lt.jsxs("div",{className:"header-content",children:[lt.jsxs("div",{children:[lt.jsx("h1",{children:"Tally Prime Middleware"}),lt.jsx("p",{children:"Monitor connections and manage Tally Prime data"})]}),lt.jsx("button",{className:"settings-button",onClick:()=>c(!0),title:"Settings",children:"⚙️"})]})}),lt.jsxs("main",{className:"app-main",children:[lt.jsxs("div",{className:"status-section",children:[lt.jsx(Dg,{}),lt.jsx(ev,{onConnectionChange:s})]}),lt.jsx("div",{className:"content-section",children:lt.jsx(eE,{tallyConnected:h})})]}),lt.jsx("footer",{className:"app-footer",children:lt.jsx("p",{children:"Tally Prime Middleware v1.0.0 - Built with React & Electron"})}),lt.jsx(nE,{isOpen:d,onClose:()=>c(!1),onSettingsChange:f,currentSettings:u})]})}function rE(){return lt.jsx(tv,{children:lt.jsx(iE,{})})}Sg.createRoot(document.getElementById("root")).render(lt.jsx(Kt.StrictMode,{children:lt.jsx(rE,{})}));
