import { createContext, useContext, useState, useEffect } from 'react';

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const [tallySettings, setTallySettings] = useState({
    host: 'localhost',
    port: 9000
  });

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('tallySettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setTallySettings(parsed);
      } catch (error) {
        console.error('Failed to parse saved settings:', error);
        // Keep default settings if parsing fails
      }
    }
  }, []);

  const updateTallySettings = (newSettings) => {
    setTallySettings(newSettings);
    localStorage.setItem('tallySettings', JSON.stringify(newSettings));
  };

  const resetTallySettings = () => {
    const defaultSettings = { host: 'localhost', port: 9000 };
    setTallySettings(defaultSettings);
    localStorage.setItem('tallySettings', JSON.stringify(defaultSettings));
  };

  const getTallyUrl = () => {
    return `http://${tallySettings.host}:${tallySettings.port}`;
  };

  const value = {
    tallySettings,
    updateTallySettings,
    resetTallySettings,
    getTallyUrl
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export default SettingsContext;
