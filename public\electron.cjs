const { app, BrowserWindow, ipcMain, screen } = require("electron");
const path = require("path");
const isDev = process.env.NODE_ENV === "development" || !app.isPackaged;

function createWindow() {
  // Get the primary display's work area size
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;

  // Calculate window size (80% of screen size, with minimum dimensions)
  const windowWidth = Math.max(Math.floor(width * 0.8), 1000);
  const windowHeight = Math.max(Math.floor(height * 0.8), 700);

  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, "preload.cjs"),
    },
    icon: path.join(__dirname, "icon.png"), // Add an icon if you have one
    show: false, // Don't show until ready-to-show
    center: true, // Center the window on screen
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL("http://localhost:5173");
    // DevTools can be opened manually with Ctrl+Shift+I or F12
  } else {
    mainWindow.loadFile(path.join(__dirname, "../dist/index.html"));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once("ready-to-show", () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on("closed", () => {
    // Dereference the window object
    app.quit();
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(createWindow);

// Quit when all windows are closed
app.on("window-all-closed", () => {
  // On macOS it is common for applications to stay active until explicitly quit
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // On macOS re-create a window when the dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on("web-contents-created", (event, contents) => {
  contents.on("new-window", (event, navigationUrl) => {
    event.preventDefault();
  });
});
