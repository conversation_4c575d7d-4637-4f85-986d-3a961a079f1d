import { useState, useEffect } from "react";
import TallyService from "../services/tallyService";
import { useSettings } from "../contexts/SettingsContext";
import "./CompanyList.css";

const CompanyList = ({ tallyConnected }) => {
  const { tallySettings } = useSettings();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetched, setLastFetched] = useState(null);
  const [selectedCompany, setSelectedCompany] = useState(null);

  const tallyService = new TallyService(tallySettings.host, tallySettings.port);

  const fetchCompanies = async () => {
    if (!tallyConnected) {
      setError("Tally Prime is not connected");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await tallyService.getCompanyList();

      if (result.success) {
        setCompanies(result.companies);
        setLastFetched(new Date());
        setError(null);
      } else {
        setError(result.error);
        setCompanies([]);
      }
    } catch (error) {
      setError(`Failed to fetch companies: ${error.message}`);
      setCompanies([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Update the service with new settings
    tallyService.updateConnection(tallySettings.host, tallySettings.port);

    if (tallyConnected) {
      fetchCompanies();
    } else {
      setCompanies([]);
      setError(null);
    }
  }, [tallyConnected, tallySettings.host, tallySettings.port]);

  const handleCompanySelect = async (company) => {
    setSelectedCompany(company);
    // You can add more functionality here, like fetching company details
  };

  const handleRefresh = () => {
    fetchCompanies();
  };

  if (!tallyConnected) {
    return (
      <div className="company-list-container">
        <div className="company-list-header">
          <h3>Company List</h3>
        </div>
        <div className="company-list-message">
          <p>Please ensure Tally Prime is connected to view companies.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="company-list-container">
      <div className="company-list-header">
        <h3>Company List</h3>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="refresh-button"
        >
          {loading ? "🔄" : "↻"} Refresh
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>❌ {error}</p>
        </div>
      )}

      {loading && (
        <div className="loading-message">
          <p>🔄 Loading companies...</p>
        </div>
      )}

      {lastFetched && (
        <div className="last-fetched">
          Last updated: {lastFetched.toLocaleString()}
        </div>
      )}

      {companies.length > 0 && (
        <div className="company-list">
          <div className="company-count">
            Found {companies.length} companies
          </div>

          <div className="company-table">
            <div className="company-table-header">
              <div className="company-header-cell">Company Name</div>
              <div className="company-header-cell">GUID</div>
              <div className="company-header-cell">Actions</div>
            </div>

            {companies.map((company, index) => (
              <div
                key={company.guid || index}
                className={`company-row ${
                  selectedCompany?.name === company.name ? "selected" : ""
                }`}
                onClick={() => handleCompanySelect(company)}
              >
                <div className="company-cell company-name">{company.name}</div>
                <div className="company-cell company-guid">
                  {company.guid || "N/A"}
                </div>
                <div className="company-cell company-actions">
                  <button
                    className="select-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCompanySelect(company);
                    }}
                  >
                    Select
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {companies.length === 0 && !loading && !error && tallyConnected && (
        <div className="no-companies-message">
          <p>
            No companies found. Make sure Tally Prime has companies configured.
          </p>
        </div>
      )}

      {selectedCompany && (
        <div className="selected-company-details">
          <h4>Selected Company Details</h4>
          <div className="company-details">
            <p>
              <strong>Name:</strong> {selectedCompany.name}
            </p>
            {selectedCompany.guid && (
              <p>
                <strong>GUID:</strong> {selectedCompany.guid}
              </p>
            )}
            {selectedCompany.remoteid && (
              <p>
                <strong>Remote ID:</strong> {selectedCompany.remoteid}
              </p>
            )}
            {selectedCompany.alterid && (
              <p>
                <strong>Alter ID:</strong> {selectedCompany.alterid}
              </p>
            )}
            {selectedCompany.masterid && (
              <p>
                <strong>Master ID:</strong> {selectedCompany.masterid}
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyList;
