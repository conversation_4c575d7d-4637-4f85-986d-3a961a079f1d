import { useState, useEffect } from 'react';
import './Settings.css';

const Settings = ({ isOpen, onClose, onSettingsChange, currentSettings }) => {
  const [host, setHost] = useState(currentSettings?.host || 'localhost');
  const [port, setPort] = useState(currentSettings?.port || 9000);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (currentSettings) {
      setHost(currentSettings.host);
      setPort(currentSettings.port);
    }
  }, [currentSettings]);

  const validateSettings = () => {
    const newErrors = {};

    // Validate host
    if (!host.trim()) {
      newErrors.host = 'Host is required';
    } else if (!/^[a-zA-Z0-9.-]+$/.test(host.trim())) {
      newErrors.host = 'Invalid host format';
    }

    // Validate port
    const portNum = parseInt(port);
    if (!port || isNaN(portNum)) {
      newErrors.port = 'Port must be a number';
    } else if (portNum < 1 || portNum > 65535) {
      newErrors.port = 'Port must be between 1 and 65535';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateSettings()) {
      const newSettings = {
        host: host.trim(),
        port: parseInt(port)
      };
      
      // Save to localStorage
      localStorage.setItem('tallySettings', JSON.stringify(newSettings));
      
      // Notify parent component
      onSettingsChange(newSettings);
      onClose();
    }
  };

  const handleReset = () => {
    const defaultSettings = { host: 'localhost', port: 9000 };
    setHost(defaultSettings.host);
    setPort(defaultSettings.port);
    setErrors({});
  };

  const handleTestConnection = async () => {
    if (!validateSettings()) return;

    try {
      const testUrl = `http://${host.trim()}:${port}`;
      const response = await fetch(testUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/xml' },
        body: '<ENVELOPE><HEADER><TALLYREQUEST>Export Data</TALLYREQUEST></HEADER></ENVELOPE>',
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        alert('✅ Connection successful!');
      } else {
        alert(`❌ Connection failed: HTTP ${response.status}`);
      }
    } catch (error) {
      alert(`❌ Connection failed: ${error.message}`);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="settings-overlay">
      <div className="settings-modal">
        <div className="settings-header">
          <h2>Tally Prime Settings</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="settings-content">
          <div className="settings-section">
            <h3>Connection Settings</h3>
            <p className="settings-description">
              Configure the host and port for connecting to Tally Prime. 
              Make sure Tally Prime is running with Gateway enabled.
            </p>
          </div>

          <div className="form-group">
            <label htmlFor="host">Host:</label>
            <input
              type="text"
              id="host"
              value={host}
              onChange={(e) => setHost(e.target.value)}
              className={errors.host ? 'error' : ''}
              placeholder="localhost"
            />
            {errors.host && <span className="error-text">{errors.host}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="port">Port:</label>
            <input
              type="number"
              id="port"
              value={port}
              onChange={(e) => setPort(e.target.value)}
              className={errors.port ? 'error' : ''}
              placeholder="9000"
              min="1"
              max="65535"
            />
            {errors.port && <span className="error-text">{errors.port}</span>}
          </div>

          <div className="current-url">
            <strong>Current URL:</strong> http://{host}:{port}
          </div>
        </div>

        <div className="settings-actions">
          <button 
            className="test-button" 
            onClick={handleTestConnection}
            disabled={Object.keys(errors).length > 0}
          >
            🔍 Test Connection
          </button>
          <button className="reset-button" onClick={handleReset}>
            🔄 Reset to Default
          </button>
          <div className="action-buttons">
            <button className="cancel-button" onClick={onClose}>
              Cancel
            </button>
            <button 
              className="save-button" 
              onClick={handleSave}
              disabled={Object.keys(errors).length > 0}
            >
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
