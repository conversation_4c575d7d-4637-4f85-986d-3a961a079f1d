.company-list-container {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.company-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.company-list-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.refresh-button {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.refresh-button:hover:not(:disabled) {
  background: #1976d2;
}

.refresh-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  background: #ffeaea;
  border: 1px solid #f44336;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-message p {
  margin: 0;
  color: #c62828;
}

.loading-message {
  background: #fff3e0;
  border: 1px solid #ff9800;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  text-align: center;
}

.loading-message p {
  margin: 0;
  color: #ef6c00;
}

.company-list-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.last-fetched {
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
  text-align: right;
}

.company-count {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 500;
}

.company-table {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.company-table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 100px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.company-header-cell {
  padding: 12px;
  font-weight: 600;
  color: #333;
  border-right: 1px solid #e0e0e0;
}

.company-header-cell:last-child {
  border-right: none;
}

.company-row {
  display: grid;
  grid-template-columns: 2fr 1fr 100px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.company-row:hover {
  background: #f9f9f9;
}

.company-row.selected {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.company-row:last-child {
  border-bottom: none;
}

.company-cell {
  padding: 12px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
}

.company-cell:last-child {
  border-right: none;
}

.company-name {
  font-weight: 500;
  color: #333;
}

.company-guid {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.company-actions {
  justify-content: center;
}

.select-button {
  background: #4caf50;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s ease;
}

.select-button:hover {
  background: #45a049;
}

.no-companies-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.selected-company-details {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.selected-company-details h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.company-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #555;
}

.company-details strong {
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .company-table-header,
  .company-row {
    grid-template-columns: 1fr;
  }
  
  .company-header-cell,
  .company-cell {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .company-header-cell:last-child,
  .company-cell:last-child {
    border-bottom: none;
  }
  
  .company-list-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}
