import axios from "axios";
import { parseString } from "xml2js";

class TallyService {
  constructor(host = "localhost", port = 9000) {
    this.updateConnection(host, port);
  }

  updateConnection(host, port) {
    this.host = host;
    this.port = port;
    this.baseURL = `http://${host}:${port}`;
  }

  // Helper method to send XML requests to Tally
  async sendXMLRequest(xmlData) {
    try {
      const response = await axios.post(this.baseURL, xmlData, {
        headers: {
          "Content-Type": "application/xml",
        },
        timeout: 10000, // 10 second timeout
      });
      return response.data;
    } catch (error) {
      throw new Error(`Tally request failed: ${error.message}`);
    }
  }

  // Helper method to parse XML response
  parseXMLResponse(xmlData) {
    return new Promise((resolve, reject) => {
      parseString(xmlData, { explicitArray: false }, (err, result) => {
        if (err) {
          reject(new Error(`XML parsing failed: ${err.message}`));
        } else {
          resolve(result);
        }
      });
    });
  }

  // Test connection to Tally Prime
  async testConnection() {
    const testXML = `
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;

    try {
      const response = await this.sendXMLRequest(testXML);
      return { success: true, data: response };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Fetch list of companies from Tally Prime
  async getCompanyList() {
    const companyListXML = `
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>List of Companies</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;

    try {
      const xmlResponse = await this.sendXMLRequest(companyListXML);
      const parsedData = await this.parseXMLResponse(xmlResponse);

      // Extract company information from the parsed XML
      const companies = this.extractCompanyData(parsedData);
      return { success: true, companies };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Extract company data from parsed XML response
  extractCompanyData(parsedData) {
    try {
      const companies = [];

      // Navigate through the XML structure to find company data
      if (parsedData && parsedData.ENVELOPE && parsedData.ENVELOPE.BODY) {
        const body = parsedData.ENVELOPE.BODY;

        // Handle different possible XML structures
        if (body.IMPORTDATA && body.IMPORTDATA.REQUESTDATA) {
          const requestData = body.IMPORTDATA.REQUESTDATA;

          // If TALLYMESSAGE exists and contains company data
          if (requestData.TALLYMESSAGE) {
            const messages = Array.isArray(requestData.TALLYMESSAGE)
              ? requestData.TALLYMESSAGE
              : [requestData.TALLYMESSAGE];

            messages.forEach((message) => {
              if (message.COMPANY) {
                const companyData = Array.isArray(message.COMPANY)
                  ? message.COMPANY
                  : [message.COMPANY];

                companyData.forEach((company) => {
                  companies.push({
                    name: company.NAME || "Unknown Company",
                    guid: company.GUID || "",
                    remoteid: company.REMOTEID || "",
                    alterid: company.ALTERID || "",
                    masterid: company.MASTERID || "",
                    // Add more fields as needed
                  });
                });
              }
            });
          }
        }

        // Alternative structure - direct company list
        if (body.DATA && body.DATA.COLLECTION) {
          const collection = body.DATA.COLLECTION;
          if (collection.COMPANY) {
            const companyData = Array.isArray(collection.COMPANY)
              ? collection.COMPANY
              : [collection.COMPANY];

            companyData.forEach((company) => {
              companies.push({
                name: company.NAME || company.$ || "Unknown Company",
                guid: company.GUID || "",
                remoteid: company.REMOTEID || "",
                alterid: company.ALTERID || "",
                masterid: company.MASTERID || "",
              });
            });
          }
        }
      }

      return companies;
    } catch (error) {
      console.error("Error extracting company data:", error);
      return [];
    }
  }

  // Get company details by name
  async getCompanyDetails(companyName) {
    const companyDetailsXML = `
      <ENVELOPE>
        <HEADER>
          <TALLYREQUEST>Export Data</TALLYREQUEST>
        </HEADER>
        <BODY>
          <EXPORTDATA>
            <REQUESTDESC>
              <REPORTNAME>Company</REPORTNAME>
              <STATICVARIABLES>
                <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                <SVCOMPANY>${companyName}</SVCOMPANY>
              </STATICVARIABLES>
            </REQUESTDESC>
          </EXPORTDATA>
        </BODY>
      </ENVELOPE>
    `;

    try {
      const xmlResponse = await this.sendXMLRequest(companyDetailsXML);
      const parsedData = await this.parseXMLResponse(xmlResponse);
      return { success: true, data: parsedData };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

export default TallyService;
